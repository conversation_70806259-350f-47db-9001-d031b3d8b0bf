package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.TaskAbnormalTypeEnum;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 工单工时填报服务接口
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Deprecated
public interface ICostTaskDailyPaperService {

    /***
     * ~ 分页查询 ~
     * @param pageRequest 分页对象
     * @param filter 查询条件
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaper>
     * <AUTHOR>
     * @date 2022/8/23 15:22
     */
    Page<CostTaskDailyPaper> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * ~ 根据ID查询 ~
     * @param id id
     * @return com.gok.pboot.pms.entity.DailyPaper
     * <AUTHOR>
     * @date 2022/8/23 15:23
     */
    CostTaskDailyPaper getById(Long id);

    /**
     * ~ 根据ID查询详情 ~
     * @param id id
     * @return com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO
     * <AUTHOR>
     * @date 2022/8/25 9:43
     */
    CostTaskDailyPaperDetailsVO getDetailsById(Long id);

    /**
     * ~ 根据日期查询详情，如果传入的日期为节假日，则前推至工作日 ~
     * @param date date
     * @return com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO
     * <AUTHOR>
     * @date 2022/9/5 10:42
     */
    CostTaskDailyPaperDetailsVO getDetailsByDateForCurrentUser(LocalDate date);

    /**
     * ~ 根据日期查询详情 ~
     * @param date 日期
     * @return com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO
     * <AUTHOR>
     * @date 2022/9/19 14:23
     */
    CostTaskDailyPaperDetailsVO getDetailsByDateForCurrentUserAccurate(LocalDate date);

    /**
     * ~ 添加/编辑 日报 ~
     * @param request 请求实体
     * <AUTHOR>
     * @date 2022/8/24 9:58
     */
    void newAddOrUpdate(CostTaskDailyPaperAddOrUpdateDTO request);

    /**
     * ~ 按指定的日期列出当月日报 ~
     * @param request 请求实体
     * @return java.util.List<com.gok.pboot.pms.entity.vo.DailyPaperVO>
     * <AUTHOR>
     * @date 2022/8/26 14:41
     */
    List<CostTaskDailyPaperVO> listDailyPaperMonthly(CostTaskDailyPaperMonthlyDTO request);

    /**
     * ~ 统计指定日期月份的日报填报数据 ~
     * @param request 请求参数
     * @return com.gok.pboot.pms.entity.vo.DailyPapersStatisticMonthlyVO
     *
     * <AUTHOR>
     */
    CostTaskDailyPapersStatisticMonthlyVO getStatisticMonthly(CostTaskDailyPaperMonthlyDTO request);

    /**
     * ~ 检查条目，如果需要，更新日报审核状态 ~
     * @param dailyPaperIds 日报ID列表
     * @param targetStatus 目标状态
     * <AUTHOR>
     * @date 2022/9/7 17:26
     */
    void updateApprovalStatusIfNeeded(List<Long> dailyPaperIds, ApprovalStatusEnum targetStatus);

    /**
     * ~ 自动提交指定日期前一天的日报的调度任务触发 ~
     * <AUTHOR>
     * @param now 日期 yyyy-MM-dd
     * @date 2022/9/16 14:39
     */
    void autoSubmitJob(String now);

    /**
     * 查询异常工时列表
     *
     * @param pageRequest 分页参数
     * @param dto 查询条件
     * @return 异常工时列表
     */
    Page<CostTaskDailyPaperAbnormalVO> findAbnormalPage(PageRequest pageRequest, CostTaskDailyPaperAbnormalDTO dto);

    /**
     * 导出异常工时列表
     * @param dto 查询条件
     * @return 异常工时列表
     */
    List<CostTaskDailyPaperAbnormalVO> exportAbnormalPaper(CostTaskDailyPaperAbnormalDTO dto);

    /**
     * 发送异常工时消息推送
     *
     * @param dto 查询条件
     */
    void sendAbnormalMsg(CostTaskDailyPaperAbnormalDTO dto);

    /**
     * 定时任务发送异常工时消息推送
     *
     * @param abnormalTypeEnum 异常类型枚举
     */
    void taskSendAbnormalMsg(TaskAbnormalTypeEnum abnormalTypeEnum);

    /**
     * 根据工单ID获取日报详情
     *
     * @param pageRequest    分页参数
     * @param taskId         工单ID
     * @param approvalStatus approvalStatus
     * @return 日报详情分页数据
     */
    Page<TaskDailyPaperDetailVO> getDailyDetailsByTaskId(PageRequest pageRequest, Long taskId, Integer approvalStatus);

    /**
     * 售前工单工时审核分页列表
     *
     * @param pageRequest 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    Page<CostSupportTaskApprovalVO> findSupportTaskApprovalPage(PageRequest pageRequest, CostSupportTaskApprovalDTO dto);

    /**
     * 查询工单工时审核列表
     *
     * @param dto 查询条件
     * @return 工单工时审核列表
     */
    CostTaskDailyPaperApprovalVO findTaskDailyPaperApprovalList(PageRequest pageRequest,CostSupportTaskApprovalDTO dto);
    /**
     * 审核工单工时
     * @param dto
     * @return
     */
    ApiResult<String> changeApprovalStatus(CostChangeApprovalStatusDTO dto);

    /**
     * 批量审核
     * @param dailyPaperEntryIdStr
     * @return
     */
    ApiResult<String> batchApproval(String dailyPaperEntryIdStr);



    /**
     * 任务日报页面
     *
     * @param pageRequest pageRequest
     * @param taskId         任务 ID
     * @param approvalStatus 审批状态
     * @return {@link CostTaskDailyDetailVO }
     */
    CostTaskDailyDetailVO taskDailyPaperPage(PageRequest pageRequest, Long taskId, Integer approvalStatus);

    /**
     * 获取异常计数
     *
     * @param dto DTO
     * @return {@link Map }<{@link Integer }, {@link Integer }>
     */
    Map<Integer, Integer> getAbnormalCount(CostTaskDailyPaperAbnormalDTO dto);
}