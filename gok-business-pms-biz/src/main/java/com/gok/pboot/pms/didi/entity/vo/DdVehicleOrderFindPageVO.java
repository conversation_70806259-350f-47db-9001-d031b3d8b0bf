package com.gok.pboot.pms.didi.entity.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴用车订单分页查询VO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class DdVehicleOrderFindPageVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 乘车人ID
     */
    private Long passengerId;

    /**
     * 乘车人工号
     */
    private String passengerEmployeeNo;

    /**
     * 乘车人姓名
     */
    private String passengerName;

    /**
     * 乘车人部门ID
     */
    private Long passengerDeptId;

    /**
     * 乘车人部门名称
     */
    private String passengerDeptName;

    /**
     * 乘坐时间
     */
    private LocalDateTime travelTime;

    /**
     * 到达时间
     */
    private LocalDateTime arrivalTime;

    /**
     * 用车类型
     */
    private String vehicleType;

    /**
     * 出发城市
     */
    private String departureCity;

    /**
     * 出发地址
     */
    private String departureAddress;

    /**
     * 到达城市
     */
    private String arrivalCity;

    /**
     * 到达地址
     */
    private String arrivalAddress;

    /**
     * 用车行驶距离(公里)
     */
    private BigDecimal travelDistance;

    /**
     * 实付金额
     */
    private BigDecimal companyActualPayment;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 预订日期
     */
    private LocalDate bookingDate;

    /**
     * 预订人ID
     */
    private Long bookingUserId;

    /**
     * 预订人工号
     */
    private String bookingEmployeeNo;

    /**
     * 预订人姓名
     */
    private String bookingEmployeeName;

    /**
     * 预订人部门ID
     */
    private Long bookingDeptId;

    /**
     * 预订人部门名称
     */
    private String bookingDeptName;

    /**
     * 出差申请单号
     */
    private String businessTripApplicationNo;

    /**
     * 出差事由
     */
    private String businessTripReason;

    /**
     * 成本中心ID
     */
    private Long costCenterId;

    /**
     * 成本中心名称
     */
    private String costCenterName;

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 所属项目名称
     */
    private String projectName;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 所属公司ID
     */
    private Long companyId;

    /**
     * 所属公司名称
     */
    private String companyName;

    /**
     * 所属账期(YYYY-MM)
     */
    private String accountingPeriod;

    /**
     * 报销单号
     */
    private String expenseReportNo;

    /**
     * 发起状态(0=待发起,1=已发起,2=发起错误-OA接口报错,3=发起错误-预算不足)
     */
    private Integer initiationStatus;

    private String initiationStatusStr;

    /**
     * 发起日期
     */
    private LocalDate initiationDate;

    /**
     * 流程发起ID
     */
    private Long requestId;

    /**
     * 发起备注
     */
    private String initiationRemark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改人ID
     */
    private Long modifierId;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 修改时间
     */
    private LocalDateTime mtime;

} 