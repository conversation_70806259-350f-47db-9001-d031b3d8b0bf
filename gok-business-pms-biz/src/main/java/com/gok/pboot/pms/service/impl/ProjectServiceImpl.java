package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.admin.entity.SysDictItem;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.dto.UserPmsDTO;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteOutPmsService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.*;
import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.entity.ProjectCollect;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.dto.RoleProjectPageDto;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.entity.vo.facade.ProjectInfoVO;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.IProjectService;
import com.gok.pboot.pms.service.IProjectTaskeService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Service
@Slf4j
@Transactional(readOnly = false, rollbackFor = Exception.class)
@AllArgsConstructor
public class ProjectServiceImpl implements IProjectService {
    private final ProjectMapper mapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final TaskMapper taskMapper;
    private final TaskUserMapper taskUserMapper;
    private final ProjectTaskMapper projectTaskMapper;

    private final ProjectTaskeMapper projectTaskeMapper;

    private final ProjectCollectMapper projectCollectMapper;
    private final PmsRetriever pmsRetriever;

    @Resource
    private RemoteOutPmsService remoteOutPmsService;
    @Resource
    private RemoteRoleService remoteRoleService;

    private final IProjectTaskeService projectTaskeService;

    private final ProjectScopeHandle projectScopeHandle;
    @Resource
    private final RemoteOutService remoteOutService;
    @Resource
    private final RemoteDeptService remoteDeptService;
    private final RemoteBcpDictService remoteBcpDictService;

    public static final String PARAM_FILTER_PROJECT_ID = "projectId";
    public static final String PARAM_FILTER_PERMISSION = "permission";
    public static final String PARAM_FILTER_APPLICATION = "application";
    public static final String PARAM_FILTER_MENU_TYPE = "menuType";

    public static final List<String> DATA_SCOPE_PERMISSION_LIST = ImmutableList.of("PROJECT_OVERVIEW", "BUSINESS_OPPORTUNITY_INFORMATION", "PROJECT_QUEST", "PROJECT_STAKEHOLDERS", "MAN_HOUR_STATISTICS", "WEEKLY_PROJECT_REPORT", "MEETING_MINUTES", "PROJECT_RISK", "FINANCIAL_DATA", "PROJECT_CONTRACT", "PROJECT_QUEST_LIST", "PROJECT_TEAM_MEMBER", "PROJECT_TEAM_MEMBER_LIST", "CUSTOMER_STAKEHOLDERS", "CUSTOMER_STAKEHOLDERS_LIST", "MAN_HOUR_STATISTICS_LIST", "WEEKLY_PROJECT_REPORT_LIST", "MEETING_MINUTES_LIST", "PROJECT_RISK_LIST", "PROJECT_OVERVIEW_INSIDE", "BUSINESS_OPPORTUNITY_INFORMATION_INSIDE", "PROJECT_QUEST_INSIDE", "PROJECT_STAKEHOLDERS_INSIDE", "MAN_HOUR_STATISTICS_INSIDE", "WEEKLY_PROJECT_REPORT_INSIDE", "MEETING_MINUTES_INSIDE", "PROJECT_RISK_INSIDE", "FINANCIAL_DATA_INSIDE", "PROJECT_CONTRACT_INSIDE", "PROJECT_QUEST_LIST_INSIDE", "PROJECT_TEAM_MEMBER_INSIDE", "PROJECT_TEAM_MEMBER_LIST_INSIDE", "CUSTOMER_STAKEHOLDERS_INSIDE", "CUSTOMER_STAKEHOLDERS_LIST_INSIDE", "MAN_HOUR_STATISTICS_LIST_INSIDE", "WEEKLY_PROJECT_REPORT_LIST_INSIDE", "MEETING_MINUTES_LIST_INSIDE", "PROJECT_RISK_LIST_INSIDE");


    @Override
    @Deprecated
    public Page<ProjectVO> findPage2(PageRequest pageRequest, Map<String, Object> filter) {
//        Page<com.gok.pboot.pms.entity.vo.ProjectInfoVO> poList = iProjectInfoService.findPage(pageRequest, filter);
        Page<com.gok.pboot.pms.entity.vo.ProjectInfoVO> poList = new Page<>();

        List<ProjectVO> records = new ArrayList<>();
        List<ProjectVO> finalRecords = records;
        List<com.gok.pboot.pms.entity.vo.ProjectInfoVO> records1 = poList.getRecords();
        //List<ProjectInfo> records1 = poList.getRecords();
        records1.forEach(r -> {
            ProjectVO projectVO = ProjectInfo.toProjectVO(r);
            finalRecords.add(projectVO);
        });

        //Page<ProjectVO> list = mapper.findListPageNoRole(new Page<ProjectVO>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        //list.setTotal(poList.getTotal());
        HashSet<ProjectVO> set = new HashSet<>(records);
        records = ListUtil.toList(set);
        records.forEach(x -> x.setProjectStatusName(EnumUtils.getNameByValue(ProjectStatusEnum.class, x.getProjectStatus())));

        Page<ProjectVO> list = new Page<>();
        list.setRecords(records);
        list.setTotal(poList.getTotal());
        list.setPages(poList.getPages());
        list.setCurrent(poList.getCurrent());
        list.setSize(poList.getSize());

        return list;

    }

    @Override
    public ApiResult getByIdCount(Long id) {
        ProjectInfoVO projectInfoVo = mapper.selectByIdVo(id);
        if (projectInfoVo.getProjectDepartment() == null) {
            projectInfoVo.setProjectDepartment("");
        }
        projectInfoVo.setProjectStatusName(EnumUtils.getNameByValue(ProjectStatusEnum.class, projectInfoVo.getProjectStatus()));

        //总任务数、总工时数
        Map<String, Object> map = taskMapper.totalMap(id);
        Long totalTasks = (Long) map.get("totalTasks");
        projectInfoVo.setTotalTasks(totalTasks);
        projectInfoVo.setTotalMhours(CommonUtils.unitConversion((BigDecimal) map.get("totalMhours")));
        return ApiResult.success(projectInfoVo);
    }

    @Override
    public List<ProjectWithTasksInDailyPaperEntryVO> findByCurrUserIdForDailyPaperEntry(LocalDate date) {
        // 通过 当前用户id、项目工时类型，获取可查看的项目集合
        List<ProjectInDailyPaperEntryVO> projects = mapper.findByUserIdForDailyPaperEntry(SecurityUtils.getUser().getId(), ProjectCollectTypeEnum.WORKING_HOURS.getValue()).stream().filter(project -> {
            // 商机状态的项目的日期填报只能填在创建以后
            if (ProjectStatusEnum.SJ.getValue().equals(project.getProjectStatus())) {
                return project.getCtime().isBefore(date) || project.getCtime().equals(date);
            }
            return true;
        }).map(ProjectInDailyPaperEntryVO::new).collect(Collectors.toList());
        List<TaskInDailyPaperEntryVO> tasksForEachProject;
        Long userId = SecurityUtils.getUser().getId();
        log.info("user: {}, userId:{}, 查询可用项目", SecurityUtils.getUser(), userId);
        List<ProjectWithTasksInDailyPaperEntryVO> result = Lists.newArrayListWithCapacity(projects.size());

        // 获取可查看的项目id集合
        List<Long> list = new ArrayList<>();
        for (ProjectInDailyPaperEntryVO project : projects) {
            list.add(project.getId());
        }
        Map<Long, List<TaskInDailyPaperEntryVO>> entryMap = new HashMap<>();
        // 通过用户id和项目id集合获取对应任务集合，通过项目id来关联项目和任务
        List<TaskInDailyPaperEntryListVo> vos = taskMapper.findByUserIdAndProjectIdForEntryVos(userId, list);
        // 组装 项目id : 任务集合 map，用以后续填充到项目vo对象中
        vos.forEach(v -> {
            if (!Optional.ofNullable(entryMap.get(v.getProjectId())).isPresent()) {
                List<TaskInDailyPaperEntryVO> entryVOS = new ArrayList<>();
                entryVOS.add(BeanUtil.copyProperties(v, TaskInDailyPaperEntryVO.class));
                entryMap.put(v.getProjectId(), entryVOS);
            } else {
                entryMap.get(v.getProjectId()).add(BeanUtil.copyProperties(v, TaskInDailyPaperEntryVO.class));
            }
        });

        for (ProjectInDailyPaperEntryVO p : projects) {
            // 将对应项目下的任务放到项目vo中
            if (!Optional.ofNullable(entryMap.get(p.getId())).isPresent()) {
                continue;
            }
            tasksForEachProject = entryMap.get(p.getId());
            ProjectWithTasksInDailyPaperEntryVO entryVO = new ProjectWithTasksInDailyPaperEntryVO(p, tasksForEachProject);
            String businessName = EnumUtils.getNameByValue(ProjectStatusEnum.class, p.getProjectStatus());
            String projectName = entryVO.getProjectName();
            entryVO.setProjectName(projectName + "-" + businessName);
            result.add(entryVO);
        }

        // 如果项目下没有任务，则不返回该项目
        result = result.stream().filter(r -> !r.getTasks().isEmpty()).collect(Collectors.toList());

        return result;
    }

    @Override
    public List<ProjectWithTasksInDailyPaperEntryVO> findByCurrUserIdForDailyPaperEntryMix(LocalDate date) {
        // 获取项目对应的新、旧任务列表，并设置旧任务标识
        List<ProjectWithTasksInDailyPaperEntryVO> projectWithOldTaskList = findByCurrUserIdForDailyPaperEntry(date);
        projectWithOldTaskList.forEach(p -> p.getTasks().forEach(t -> t.setOldTaskFlag(YesOrNoEnum.YES.getValue())));
        List<ProjectWithTasksInDailyPaperEntryVO> projectWithNewTaskList = findByCurrUserIdForDailyPaperEntryNew(date);
        projectWithNewTaskList.forEach(p -> p.getTasks().forEach(t -> t.setOldTaskFlag(YesOrNoEnum.NO.getValue())));


        // 获取项目对应的新任务Map：key:id  value:tasks
        Map<Long, ProjectWithTasksInDailyPaperEntryVO> newTaskMap = projectWithNewTaskList.stream().collect(Collectors.toMap(ProjectWithTasksInDailyPaperEntryVO::getId, p -> p, (a, b) -> a));
        // 将项目的新、旧任务合并
        projectWithOldTaskList.forEach(p -> {
            ProjectWithTasksInDailyPaperEntryVO taskInDailyPaperEntryVOS = newTaskMap.get(p.getId());
            if (Optional.ofNullable(taskInDailyPaperEntryVOS).isPresent()) {
                p.getTasks().addAll(taskInDailyPaperEntryVOS.getTasks());
                newTaskMap.remove(p.getId());
            }
        });

        projectWithOldTaskList.addAll(newTaskMap.values());

        return projectWithOldTaskList;
    }

    @Override
    public List<ProjectWithTasksInDailyPaperEntryVO> findByCurrUserIdForDailyPaperEntryNew(LocalDate date) {
        // 通过 当前用户id、项目工时类型，获取可查看的项目集合
        List<ProjectInDailyPaperEntryVO> projects = mapper.findByUserIdForDailyPaperEntryNew(SecurityUtils.getUser().getId(), ProjectCollectTypeEnum.WORKING_HOURS.getValue()).stream().filter(project -> {
            // 商机状态的项目的日期填报只能填在创建以后
            if (ProjectStatusEnum.SJ.getValue().equals(project.getProjectStatus())) {
                return project.getCtime().isBefore(date) || project.getCtime().equals(date);
            }
            return true;
        }).map(ProjectInDailyPaperEntryVO::new).collect(Collectors.toList());

        // 获取可查看的项目id集合
        List<Long> projectIds = projects.stream().map(ProjectInDailyPaperEntryVO::getId).distinct().collect(Collectors.toList());
        // 通过项目id集合批量获取项目工时审核员姓名集合
        List<ProjectInDailyPaperEntry> entryNew2 = mapper.findByUserIdForDailyPaperEntryNew2(projectIds);
        // 组装 项目id : 审核员姓名集合 map
        Map<Long, List<String>> audioNamesMap = new HashMap<>();
        for (ProjectInDailyPaperEntry entry : entryNew2) {
            if (audioNamesMap.get(entry.getId()) == null) {
                List<String> audioNames = new ArrayList<>(entry.getAuditorNames());
                audioNamesMap.put(entry.getId(), audioNames);
            } else {
                audioNamesMap.get(entry.getId()).addAll(entry.getAuditorNames());
            }
        }
        // 填充项目vo对象的审核员集合
        for (ProjectInDailyPaperEntryVO project : projects) {
            if (audioNamesMap.get(project.getId()) != null) {
                project.setAuditorNames(audioNamesMap.get(project.getId()));
            }
        }
        Long userId = SecurityUtils.getUser().getId();
        // 通过 项目id集合、用户id、项目收藏类型 获取收藏项目的相关信息
        List<ProjectInDailyPaperEntry> entryNew3 = mapper.findByUserIdForDailyPaperEntryNew3(projectIds, userId, ProjectCollectTypeEnum.WORKING_HOURS.getValue());
        Map<Long, ProjectInDailyPaperEntry> collectMap = entryNew3.stream().collect(Collectors.toMap(ProjectInDailyPaperEntry::getId, p -> p, (a, b) -> b));
        // 填充项目vo对象的收藏项目相关信息
        for (ProjectInDailyPaperEntryVO project : projects) {
            if (collectMap.get(project.getId()) != null) {
                ProjectInDailyPaperEntry projectEntry = collectMap.get(project.getId());
                project.setCollectId(projectEntry.getCollectId());
                project.setCollectTime(projectEntry.getCollectTime());
                project.setCollectUserId(projectEntry.getCollectUserId());
            }
        }

        List<TaskInDailyPaperEntryVO> tasksForEachProject;
        List<ProjectWithTasksInDailyPaperEntryVO> result = Lists.newArrayListWithCapacity(projects.size());

        List<Long> list = new ArrayList<>();
        for (ProjectInDailyPaperEntryVO project : projects) {
            list.add(project.getId());
        }
        Map<Long, List<TaskInDailyPaperEntryVO>> entryMap = new HashMap<>();
        // 通过用户id和项目id集合获取对应任务集合，通过项目id来关联项目和任务
        List<TaskInDailyPaperEntryListVo> taskVo1 = projectTaskMapper.findByUserIdAndProjectIdForEntryVos(userId, list);
        List<TaskInDailyPaperEntryListVo> taskVo2 = projectTaskMapper.findByUserIdAndProjectIdForEntryVos2(userId, list);
        taskVo1.addAll(taskVo2);
        // 通过任务id去重
        List<TaskInDailyPaperEntryListVo> vos = taskVo1.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TaskInDailyPaperEntryListVo::getId))), ArrayList::new));
        // 组装 项目id : 任务集合 map，用以后续填充到项目vo对象中
        vos.forEach(v -> {
            if (!Optional.ofNullable(entryMap.get(v.getProjectId())).isPresent()) {
                List<TaskInDailyPaperEntryVO> entryVOS = new ArrayList<>();
                entryVOS.add(BeanUtil.copyProperties(v, TaskInDailyPaperEntryVO.class));
                entryMap.put(v.getProjectId(), entryVOS);
            } else {
                entryMap.get(v.getProjectId()).add(BeanUtil.copyProperties(v, TaskInDailyPaperEntryVO.class));
            }
        });

        for (ProjectInDailyPaperEntryVO p : projects) {
            // 将对应项目下的任务放到项目vo中
            if (!Optional.ofNullable(entryMap.get(p.getId())).isPresent()) {
                continue;
            }
            tasksForEachProject = entryMap.get(p.getId());
            ProjectWithTasksInDailyPaperEntryVO entryVO = new ProjectWithTasksInDailyPaperEntryVO(p, tasksForEachProject);
            String businessName = EnumUtils.getNameByValue(ProjectStatusEnum.class, p.getProjectStatus());
            String projectName = entryVO.getProjectName();
            entryVO.setProjectName(projectName + "-" + businessName);
            result.add(entryVO);
        }
        // 如果项目下没有任务，则不返回该项目
        result = result.stream().filter(r -> !r.getTasks().isEmpty()).collect(Collectors.toList());

        return result;
    }

    @Override
    public List<ProjectWithTasksInDailyPaperEntryUnifyVO> findCurrentForDailyPaperEntryUnify(LocalDate date) {
        Long userId = SecurityUtils.getUser().getId();
        // 通过 当前用户id、项目工时类型，获取可查看的项目集合
        List<ProjectInfo> projectInfoList = projectInfoMapper.findByUserIdForDailyPaperEntry(userId, date);
        Map<Long, ProjectInfo> projectMap = CollStreamUtil.toMap(projectInfoList, ProjectInfo::getId, e -> e);
        List<ProjectWithTasksInDailyPaperEntryUnifyVO> projectVos = projectInfoList.stream().filter(project -> {
            // 商机状态的项目的日期填报只能填在创建以后
            String projectStatus = project.getProjectStatus();
            Predicate<String> isNumber = s -> CharSequenceUtil.isNotBlank(s) && s.matches("\\d+");
            if (isNumber.test(projectStatus) && ProjectStatusEnum.SJ.getValue().equals(Integer.valueOf(projectStatus))) {
                return project.getCtime().isBefore(date) || project.getCtime().equals(date);
            }
            return true;
        }).map(ProjectWithTasksInDailyPaperEntryUnifyVO::of).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProjectWithTasksInDailyPaperEntryUnifyVO::getId))), ArrayList::new));

        if (CollUtil.isEmpty(projectVos)) {
            return Collections.emptyList();
        }

        // 获取可查看的项目id集合
        List<Long> projectIds = projectVos.stream().map(ProjectWithTasksInDailyPaperEntryUnifyVO::getId).distinct().collect(Collectors.toList());
        // 设置项目下任务
        Map<Long, List<TaskInDailyPaperEntryUnifyVO>> tasksMap = new HashMap<>();
        List<Long> taskIds = Lists.newArrayList();
        Set<Long> taskIdsFiltered;
        projectTaskeMapper.findByUserIdAndProjectIdForEntry(userId, projectIds, date)
                .stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProjectTaske::getId))), ArrayList::new))
                .forEach(t -> {
                    Long projectId = t.getProjectId();
                    TaskInDailyPaperEntryUnifyVO task = new TaskInDailyPaperEntryUnifyVO();
                    task.setId(t.getId());
                    task.setTaskName(t.getTitle());
                    task.setTaskKind(t.getKind());
                    if (t.getKind() != null && EnumUtils.existsEnumValue(t.getKind(), ProjectTaskKindEnum.class)) {
                        task.setTaskKindTxt(EnumUtils.getNameByValue(ProjectTaskKindEnum.class, t.getKind()));
                    } else {
                        task.setTaskKindTxt("无");
                    }
                    String leaderNames = t.getLeaderNames();
                    task.setAuditorNames(CharSequenceUtil.isBlank(leaderNames) ? Collections.emptyList() : Arrays.asList(leaderNames.split(StrUtil.COMMA)));
                    List<TaskInDailyPaperEntryUnifyVO> tasks = tasksMap.get(projectId);
                    if (CollUtil.isNotEmpty(tasks)) {
                        tasks.add(task);
                    } else {
                        tasks = new ArrayList<>();
                        tasks.add(task);
                        tasksMap.put(projectId, tasks);
                    }
                    taskIds.add(t.getId());
                });
        taskIdsFiltered = projectTaskeMapper.findUnFinishedTaskIds(taskIds, userId);
        projectVos.forEach(p -> {
            List<TaskInDailyPaperEntryUnifyVO> tasks = tasksMap.get(p.getId());

            if (CollUtil.isNotEmpty(tasks)) {
                tasks.forEach(t -> t.setTaskFinishFlag(!taskIdsFiltered.contains(t.getId())));
                p.setTasks(tasks);
            }
        });

        // 填充项目vo对象的收藏项目相关信息
        Map<Long, ProjectCollect> collectMap = projectCollectMapper.findForDailyPaperEntry(projectIds, ProjectCollectTypeEnum.WORKING_HOURS.getValue(), userId)
                .stream().collect(Collectors.toMap(ProjectCollect::getProjectId, pc -> pc, (a, b) -> b));
        projectVos.forEach(p -> {
            ProjectCollect pc = collectMap.get(p.getId());
            if (Optional.ofNullable(pc).isPresent()) {
                p.setCollectId(pc.getId());
                p.setCollectTime(pc.getCtime().toString());
                p.setCollectUserId(pc.getCreatorId());
            }
        });

        // 过滤无任务列表的项目
        projectVos = projectVos.stream().filter(p -> CollUtil.isNotEmpty(p.getTasks()))
                .filter(p -> {
                    ProjectInfo projectInfo = projectMap.getOrDefault(p.getId(), new ProjectInfo());
                    return !ProjectStatusEnum.valueEquals(projectInfo.getProjectStatus(), ProjectStatusEnum.GQ)
                            || Objects.equals(projectInfo.getManagerUserId(), userId);
                })
                .sorted(Comparator.comparing(r -> r, (x, y) -> {
                    if (Optional.ofNullable(x.getCollectUserId()).isPresent() && userId.equals(x.getCollectUserId())) {
                        if (Optional.ofNullable(y.getCollectUserId()).isPresent() && userId.equals(y.getCollectUserId()) && LocalDateTime.parse(y.getCollectTime().substring(0, 19), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).isAfter(LocalDateTime.parse(x.getCollectTime().substring(0, 19), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))) {
                            return 1;
                        } else {
                            return -1;
                        }
                    } else if (Optional.ofNullable(y.getCollectUserId()).isPresent() && userId.equals(y.getCollectUserId())) {
                        return 1;
                    } else {
                        return -1;
                    }
                })).collect(Collectors.toList());

        return projectVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void projectTaskPersonnel(String para) {
        //获取需要更新的项目数据
        List<ProjectUpdataVO> list = mapper.selectUpdateList(para);

        if (list.size() == 0) {
            return;
        }

        //更新的项目id集合
        List<Long> prjs = BaseEntityUtils.mapCollectionToList(list, ProjectUpdataVO::getId);
        //通过项目的id集合获取默认任务的id列表和人员list
        // <projectId, taskInfo>
        Map<Long, TaskInfoVO> taskInfoVOList = taskMapper.selectDefaultPersonnel(prjs).stream().collect(Collectors.toMap(TaskInfoVO::getProjectId, ti -> ti, (oldVal, newVal) -> {
            oldVal.getTaskUserInfoVoList().addAll(newVal.getTaskUserInfoVoList());

            return oldVal;
        }));
        List<Task> taskNeedAdd = Lists.newArrayListWithExpectedSize(5);
        List<TaskUser> taskUserNeedAdd = Lists.newArrayListWithExpectedSize(10);

        //更新数据
        list.forEach(x -> {
            String[] results = x.getXmcy().split(",");
            //OA那边的项目成员id
            Long[] longs = Arrays.stream(results).filter(NumberUtils::isCreatable).map(Long::parseLong).toArray(Long[]::new);
            List<SysUserVO> users = getUsersByIds(longs);
            Map<Long, String> userIdWithNameMap = users.stream().filter(u -> u.getUserId() != null && !StringUtils.isEmpty(u.getName())).collect(Collectors.toMap(SysUserVO::getUserId, SysUserVO::getName, (u1, u2) -> u1));
            Set<Long> userIdSetInExistTask;
            TaskInfoVO taskInfo = taskInfoVOList.get(x.getId());
            Task taskPo;
            TaskUser taskUserAdd;
            String userNickName;

            //判断有无默认任务、无则需要先生成、有则比对添加【只做添加操作】
            // 后续需要修改 原先的查询任务接口和统计任务 把无默认任务生成的步骤放在这
            if (taskInfo == null) {
                // 没有默认任务
                taskPo = new Task().setProjectId(x.getId()).setTaskName("默认任务").setTaskStatus(TaskStatusEnum.ZC.getValue()).setTaskType(TaskTypeEnum.MRRW.getValue());
                buildInsert(taskPo);
                taskNeedAdd.add(taskPo);
                taskUserNeedAdd.addAll(users.stream().map(u -> {
                    TaskUser taskUser = new TaskUser().setUserId(u.getUserId()).setUserName(u.getName()).setTaskId(taskPo.getId());

                    buildInsert(taskUser);

                    return taskUser;
                }).collect(Collectors.toList()));
            } else {
                // 有默认任务
                if (!users.isEmpty()) {
                    userIdSetInExistTask = taskInfo.getTaskUserInfoVoList().stream().map(TaskUserInfoVO::getUserId).collect(Collectors.toSet());
                    for (Long userId : longs) {
                        if (userIdSetInExistTask.contains(userId)) {
                            continue;
                        }
                        userNickName = userIdWithNameMap.get(userId);
                        if (userNickName == null) {  // 没有用户姓名，说明没有找到用户数据，可能用户已经被删除，此时不作添加
                            continue;
                        }
                        taskUserAdd = new TaskUser().setUserId(userId).setUserName(userNickName).setTaskId(taskInfo.getId());
                        buildInsert(taskUserAdd);
                        taskUserNeedAdd.add(taskUserAdd);
                    }
                }
            }
        });
        // 操作数据库
        if (!taskNeedAdd.isEmpty()) {
            taskMapper.batchSave(taskNeedAdd);
        }
        if (!taskUserNeedAdd.isEmpty()) {
            taskUserMapper.batchSave(taskUserNeedAdd);
        }
        log.info("从OA同步了" + list.size() + "条项目!");
    }

    /**
     * ①（项目经理、销售）权限的进入该页面，只能看到自己负责的项目
     * ②工时审核员权限进入，只能看到自己作为工时审核员的项目列表
     * ③业务部门管理员进入只能看到挂靠在当前部门的项目
     * ④工时系统管理员可以看到所有项目
     * 各角色可同时存在
     */
    @Override
    public Page<ProjectVO> findByPermissions(Page page, RoleProjectPageDto dto) {
        Collection<Long> projectIds = pmsRetriever.getProjectIdsAvailable();
        PigxUser user = SecurityUtils.getUser();
        dto.setUserId(user.getId());
        dto.setPrivilegeUserId(user.getId());

        // 额外增加项目列表权限
        dto.setProjectIds(projectIds);
        if (projectIds.isEmpty()) {
            return page;
        }
        Page<ProjectVO> byPermissions = mapper.findByPermissions(Page.of(dto.getPageNumber(), dto.getPageSize()), dto);
        if (CollectionUtils.isNotEmpty(byPermissions.getRecords())) {
            byPermissions.getRecords().forEach(x -> x.setProjectStatusName(EnumUtils.getNameByValue(ProjectStatusEnum.class, x.getProjectStatus())));
        }
        return byPermissions;
    }

    @Override
    public R syncOaSalesTask(String para) {
        return R.ok();
    }

    private List<SysUserVO> getUsersByIds(Long[] ids) {
        // 远程调用 仅用到用户信息的用户id、name
        UserPmsDTO userPmsDTO = new UserPmsDTO();
        userPmsDTO.setUserIds(Arrays.asList(ids));
        com.gok.components.common.util.R<List<SysUserOutVO>> userListByMultiParameterPms = remoteOutPmsService.getUserListByMultiParameterPms(userPmsDTO);
        List<SysUserOutVO> sysUserOutVOList = new ArrayList<>();
        if (userListByMultiParameterPms != null && userListByMultiParameterPms.getData() != null) {
            sysUserOutVOList = userListByMultiParameterPms.getData().stream().distinct().collect(Collectors.toList());
        }
        List<SysUserVO> sysUserVOList = new ArrayList<>();
        if (!sysUserOutVOList.isEmpty()) {
            for (SysUserOutVO sysUserOutVO : sysUserOutVOList) {
                SysUserVO sysUserVO = SysUserVO.from(sysUserOutVO);
                sysUserVOList.add(sysUserVO);
            }
        }
        return sysUserVOList.stream().distinct().collect(Collectors.toList());
    }

    private void buildInsert(BeanEntity<Long> bean) {
        bean.setId(IdWorker.getId());
        bean.setCreator("定时任务");
        bean.setCtime(new Timestamp(System.currentTimeMillis()));
        bean.setDelFlag(BaseConstants.NO);
    }

    @Override
    @Transactional
    public Long attentionProject(List<Long> projectIds) {
        Long userId = SecurityUtils.getUser().getId();
        List<ProjectAttention> projectAttentions = new ArrayList<>();

        mapper.deleteAttentionProjectByUserId(userId);
        if (projectIds.isEmpty()) {
            return userId;
        }
        projectIds = projectIds.stream().distinct().collect(Collectors.toList());
        for (Long projectId : projectIds) {
            ProjectAttention projectAttention = new ProjectAttention();
            projectAttention.setProjectId(projectId);
            projectAttention.setUserId(userId);
            BaseBuildEntityUtil.buildInsert(projectAttention);
            projectAttentions.add(projectAttention);
        }
        mapper.addAttentionProject(projectAttentions);
        return userId;
    }

    @Override
    public Page<ProjectAttentionVO> findAttentionProject(PageRequest pageRequest, Map<String, Object> filter) {
        Long userId = SecurityUtils.getUser().getId();
        filter.put("userId", userId);
        Page<ProjectAttentionVO> projectVOPage = mapper.findAttentionProjectPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        List<ProjectAttentionVO> records = projectVOPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(r -> r.setProjectStatusName(r.getProjectStatus() == null ?
                    StringUtils.EMPTY : EnumUtils.getNameByValue(ProjectStatusEnum.class, r.getProjectStatus())));
        }
        projectVOPage.setRecords(records);
        return projectVOPage;
    }

    @Override
    public List<ProjectVO> getAttentionProjectList() {
        Long userId = SecurityUtils.getUser().getId();
        List<ProjectVO> attentionProjectList = mapper.getAttentionProjectList(userId);
        if (CollUtil.isNotEmpty(attentionProjectList)) {
            attentionProjectList = attentionProjectList.stream().distinct().collect(Collectors.toList());
        }
        return attentionProjectList;
    }

    @Override
    public Boolean attentionProject(Long projectId) {
        Long userId = SecurityUtils.getUser().getId();
        ProjectAttention projectAttention = new ProjectAttention();
        projectAttention.setProjectId(projectId);
        projectAttention.setUserId(userId);
        BaseBuildEntityUtil.buildInsert(projectAttention);
        List<ProjectAttention> projectAttentions = Collections.singletonList(projectAttention);
        mapper.addAttentionProject(projectAttentions);
        return true;
    }


    @Override
    public ApiResult<ProjectBaseInfoVO> getProjectBaseInfoById(Long id) {
        List<Long> projectIdsAvailable = pmsRetriever.getProjectIdsAvailable();
        ProjectInfo projectInfo = projectInfoMapper.selectById(id);
        if (ObjectUtils.isEmpty(projectInfo) || !projectIdsAvailable.contains(id)) {
            return ApiResult.failure("项目不存在或无权限");
        }

        List<SysDictItem> sysDictItems = CollUtil.emptyIfNull(remoteBcpDictService.batchLeverList(new HashSet<>(Arrays.asList("业务类型"))).getData().get("业务类型"));
        List<Long> parentIds = sysDictItems.stream().map(SysDictItem::getParentId).distinct().collect(Collectors.toList());
        SysDictItem projectTypeDictItem = CollUtil.emptyIfNull(sysDictItems)
                .stream()
                .filter(e -> StrUtil.equals(String.valueOf(projectInfo.getProjectType()), e.getItemValue()) && !parentIds.contains(e.getItemId()))
                .findAny().orElseGet(SysDictItem::new);
        ProjectBaseInfoVO result = ProjectBaseInfoVO.of(projectInfo, getDeptIdMap(), projectTypeDictItem);
        return ApiResult.success(result);
    }

    @Override
    public ProjectOverViewInnerVO getProjectBaseInfoInnerById(Long id) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(id);

        ProjectOverViewInnerVO vo = ProjectOverViewInnerVO.from(projectInfo);
        //部门
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap = SysDeptUtils.getDeptIdMap(deptList);
        vo.setBusinessDepartment(projectInfo.getBusinessDepartmentId() == null ? StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, projectInfo.getBusinessDepartmentId()));
        vo.setProjectDepartment(projectInfo.getProjectDepartmentId() == null ? StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, projectInfo.getProjectDepartmentId()));
        return vo;
    }

    @Override
    public void removeAttentionProject(Long projectId) {
        ProjectAttention projectAttention = new ProjectAttention();
        projectAttention.setProjectId(projectId);
        projectAttention.setUserId(SecurityUtils.getUser().getId());
        mapper.removeAttentionProject(projectAttention);
    }


    @Override
    public List<SysMenuVo> getMenuAuthority(HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        Object projectIdObj = filter.get(PARAM_FILTER_PROJECT_ID);
        Object permissionObj = filter.get(PARAM_FILTER_PERMISSION);
        Object menuType = filter.get(PARAM_FILTER_MENU_TYPE);
        Long application = Long.valueOf(request.getHeader(PARAM_FILTER_APPLICATION));
        // 获取普通角色菜单权限
        List<SysMenuVo> sysMenuVos = new ArrayList<>();
        com.gok.components.common.util.R<List<SysMenuVo>> funcAuthListByIdAndAppId = remoteOutService.getFuncAuthListByIdAndAppId(SecurityUtils.getUser().getId(), application);
        if (Optional.ofNullable(funcAuthListByIdAndAppId).isPresent() && Optional.ofNullable(permissionObj).isPresent()) {
            String permission = String.valueOf(permissionObj);
            List<SysMenuVo> allSysMenuVos = funcAuthListByIdAndAppId.getData();
            if (CollUtil.isNotEmpty(allSysMenuVos)) {
                Optional<SysMenuVo> any = allSysMenuVos.stream().filter(s -> permission.equals(s.getPermission())).findAny();
                if (any.isPresent()) {
                    SysMenuVo sysMenuVo = any.get();
                    sysMenuVos = allSysMenuVos.stream().filter(s -> sysMenuVo.getMenuId().equals(s.getParentId())).collect(Collectors.toList());
                }
            }
        }

        // 获取业务角色菜单权限
        List<SysMenuVo> buttonAuthorities = new ArrayList<>();
        if (Optional.ofNullable(projectIdObj).isPresent() && Optional.ofNullable(permissionObj).isPresent()) {
            Long userId = SecurityUtils.getUser().getId();
            Long projectId = Long.valueOf(String.valueOf(projectIdObj));
            String permission = String.valueOf(permissionObj);
            List<String> projectAuthorities = new ArrayList<>();

            // 获取当前用户在此项目的业务角色
            List<Integer> roleTypes = projectTaskeService.isIronTriangle(projectId, userId);
            if (CollUtil.isNotEmpty(roleTypes)) {
                roleTypes.forEach(r -> projectAuthorities.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, r)));
            }
            if (projectTaskeService.isOperationsAssistant(projectId, userId)) {
                projectAuthorities.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue()));
            }
            if (projectTaskeService.isMember(projectId, ImmutableList.of(userId))) {
                projectAuthorities.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, RoleTypeEnum.PROJECT_MEMBER.getValue()));
            }
            if (projectTaskeService.isLeader(projectId, Collections.singletonList(userId))) {
                projectAuthorities.add(BusinessRoleCodeEnum.TASK_LEADER.getName());
            }

            if (Optional.ofNullable(menuType).isPresent() && CollUtil.isNotEmpty(projectAuthorities)) {
                buttonAuthorities = remoteRoleService.getMenuAuthListByRoleListAndClientId(application, projectAuthorities, menuType.toString(), permission).getData();
            } else if (CollUtil.isNotEmpty(projectAuthorities)) {
                buttonAuthorities = remoteRoleService.getMenuAuthListByRoleListAndClientId(application, projectAuthorities, null, permission).getData();
            }

            // 获取数据权限
            SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
            List<Long> userIds = dataScope.getUserIdList();
            if (CollUtil.isNotEmpty(userIds)) {
                List<SysMenuVo> buttonAuthorities2 = new ArrayList<>();
                List<String> projectAuthorities2 = new ArrayList<>();
                List<Integer> roleTypes2 = projectTaskeService.isIronTriangle(projectId, userIds);
                if (CollUtil.isNotEmpty(roleTypes2)) {
                    roleTypes2.forEach(r -> projectAuthorities2.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, r)));
                }
                if (projectTaskeService.isOperationsAssistant(projectId, userIds)) {
                    projectAuthorities2.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue()));
                }
                if (projectTaskeService.isMember(projectId, userIds)) {
                    projectAuthorities2.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, RoleTypeEnum.PROJECT_MEMBER.getValue()));
                }
                if (projectTaskeService.isLeader(projectId, userIds)) {
                    projectAuthorities2.add(BusinessRoleCodeEnum.TASK_LEADER.getName());
                }

                if (Optional.ofNullable(menuType).isPresent() && CollUtil.isNotEmpty(projectAuthorities2)) {
                    buttonAuthorities2 = remoteRoleService.getMenuAuthListByRoleListAndClientId(application, projectAuthorities2, menuType.toString(), permission).getData();
                } else if (CollUtil.isNotEmpty(projectAuthorities2)) {
                    buttonAuthorities2 = remoteRoleService.getMenuAuthListByRoleListAndClientId(application, projectAuthorities2, null, permission).getData();
                }
                if (CollUtil.isNotEmpty(buttonAuthorities2)) {
                    buttonAuthorities2 = buttonAuthorities2.stream().filter(b -> DATA_SCOPE_PERMISSION_LIST.contains(b.getPermission())).collect(Collectors.toList());
                    buttonAuthorities.addAll(buttonAuthorities2);
                }
            }
        }

        buttonAuthorities.addAll(sysMenuVos);

        return buttonAuthorities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SysMenuVo::getPermission))), ArrayList::new)).stream().sorted(Comparator.comparing(SysMenuVo::getSortOrder)).collect(Collectors.toList());
    }

    private Map<Long, SysDept> getDeptIdMap() {
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap;
        if (CollectionUtils.isEmpty(deptList)) {
            deptIdMap = ImmutableMap.of();
        } else {
            deptIdMap = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList).stream().collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
        }
        return deptIdMap;
    }

}
