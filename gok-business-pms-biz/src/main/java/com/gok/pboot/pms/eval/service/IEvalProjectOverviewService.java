/*
 * @Author: <PERSON> lin<PERSON>@goktech.cn
 * @Date: 2025-05-13 09:45:02
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-05-13 10:50:52
 * @FilePath: \display-platform\gok-business-pms\gok-business-pms-biz\src\main\java\com\gok\pboot\pms\eval\service\IEvalProjectOverviewService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.gok.pboot.pms.eval.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.eval.entity.domain.EvalCustomerSatisfactionSurvey;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectOverviewDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;

import java.util.List;


/**
 * 项目整体评价Service
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
public interface IEvalProjectOverviewService extends IService<EvalProjectOverview> {

    /**
     * 自动保存项目整体评价
     *
     * @param projectIds 项目ID列表
     * @return {@link List}<{@link Long}>
     */
    void autoSaveProjectOverviewEval(List<Long> projectIds);

    /**
     * 查询项目评价详情
     *
     * @param projectId 项目ID
     * @return 项目评价详情
     */
    EvalProjectOverviewVO findEvalProjectOverviewByProjectId(Long projectId);

    /**
     * 查询项目评价详情列表
     *
     * @return 项目评价详情列表
     */
    Page<EvalProjectOverviewVO> findEvalProjectOverviewList(PageRequest pageRequest, EvalProjectOverviewDTO request);

    /**
     * 查询当前用户未评价的项目ID列表
     *
     * @return 未评价的项目ID列表
     */
    List<Long> findUnevaluatedProjectIds(EvalProjectOverviewDTO request);

    /**
     * 导出项目评价详情列表
     *
     * @return 项目评价详情列表
     */
    List<EvalProjectOverviewVO> export(EvalProjectOverviewDTO request);

    /**
     * 归档项目评价
     *
     * @param projectId 项目ID
     * @return 已归档的项目评价ID
     */
    Long archiveEval(Long projectId);

    /**
     * 批量校准项目评价数据
     *
     * @param surveyList 客户满意度实体类集合
     * @return
     */
    List<Long> batchCalibrateEval(List<EvalCustomerSatisfactionSurvey> surveyList);

} 