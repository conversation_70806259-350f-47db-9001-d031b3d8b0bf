package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.module.file.entity.SysFile;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.domain.ProjectSchedulePlan;
import com.gok.pboot.pms.cost.entity.domain.ProjectSchedulePlanActivity;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.dto.ProjectSchedulePlanActivityDTO;
import com.gok.pboot.pms.cost.entity.dto.ProjectSchedulePlanDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.*;
import com.gok.pboot.pms.cost.mapper.CostManageEstimationResultsMapper;
import com.gok.pboot.pms.cost.mapper.CostManageVersionMapper;
import com.gok.pboot.pms.cost.mapper.ProjectSchedulePlanActivityMapper;
import com.gok.pboot.pms.cost.mapper.ProjectSchedulePlanMapper;
import com.gok.pboot.pms.cost.service.ICostDeliverTaskService;
import com.gok.pboot.pms.cost.service.ICostManageVersionService;
import com.gok.pboot.pms.cost.service.IProjectSchedulePlanActivityService;
import com.gok.pboot.pms.cost.service.IProjectSchedulePlanService;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/07/31
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectSchedulePlanServiceImpl extends ServiceImpl<ProjectSchedulePlanMapper, ProjectSchedulePlan> implements IProjectSchedulePlanService {

    private final ProjectInfoMapper projectInfoMapper;
    private final CostManageVersionMapper costManageVersionMapper;
    private final ProjectSchedulePlanActivityMapper schedulePlanActivityMapper;
    private final CostManageEstimationResultsMapper costManageEstimationResultsMapper;

    private final ICostDeliverTaskService costDeliverTaskService;
    private final ICostManageVersionService costManageVersionService;
    private final IProjectSchedulePlanActivityService projectSchedulePlanActivityService;

    private final DbApiUtil dbApiUtil;

    @Value("${oa.xsfyOaId:62}")
    private Long xsfyOaId;

    @Value("${oa.xmglfysqrgOaId:8102}")
    private Long xmglfysqrgOaId;

    @Override
    public ProjectSchedulePlanVO findList(Long projectId, Long versionId) {
        CostManageVersion latestVersion = getLatestCostManageVersion(projectId, versionId);

        ProjectSchedulePlanVO result = new ProjectSchedulePlanVO();
        if (null == latestVersion) {
            log.info("未找到项目ID:{} 版本ID:{} 对应项目计划版本", projectId, versionId);
            return result;
        }
        versionId = latestVersion.getId();
        projectId = latestVersion.getProjectId();
        Long requestId = latestVersion.getRequestId();

        // 设置项目基本信息
        populateProjectBasicInfo(result, latestVersion, projectId);

        // 查询OA流程状态
        populateOAProcessStatus(result, latestVersion);

        List<ProjectScheduleActivityPlanVO> activityEntityList = schedulePlanActivityMapper.findList(projectId, versionId);
        if (CollUtil.isEmpty(activityEntityList)) {
            return result;
        }

        // 处理活动列表
        processActivities(result, activityEntityList, projectId);

        return result;
    }

    /**
     * 设置项目基本信息
     */
    private void populateProjectBasicInfo(ProjectSchedulePlanVO result, CostManageVersion latestVersion, Long projectId) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        result.setProjectId(projectId);
        result.setProjectName(projectInfo.getItemName());
        result.setVersionId(latestVersion.getId());
        result.setVersionName(latestVersion.getVersionName());
        result.setStatus(latestVersion.getStatus());
    }

    /**
     * 查询OA流程状态
     */
    private void populateOAProcessStatus(ProjectSchedulePlanVO result, CostManageVersion latestVersion) {
        CostManageStatusEnum statusEnum;
        Long requestId = latestVersion.getRequestId();
        if (null != requestId) {
            List<CostManageVersionVO> costManageVersionList =
                    dbApiUtil.getOaRequestStatusToObj(Collections.singletonList(requestId), CostManageVersionVO.class);
            Integer currentNodeType = ObjectUtil.isEmpty(costManageVersionList) ? null : costManageVersionList.get(0).getRequestStatus();
            if (null == currentNodeType) {
                result.setRequestId(null);
                result.setRequestName(null);
                result.setStatus(CostManageStatusEnum.DRAFT.getValue());
                result.setStatusName(CostManageStatusEnum.DRAFT.getName());
            } else {
                Integer requestStatus = CostRequestStatusEnum.getValByNodeType(currentNodeType);
                statusEnum = CostManageStatusEnum.getEnumByFlowStatus(requestStatus);
                result.setRequestId(requestId);
                result.setRequestName(latestVersion.getRequestName());
                result.setStatus(statusEnum.getValue());
                result.setStatusName(statusEnum.getName());
                result.setRequestStatus(requestStatus);
                result.setRequestStatusName(CostRequestStatusEnum.getNameByNodeType(currentNodeType));
                // 若流程状态是已归档，目标状态设置为已确认
                if (CostRequestStatusEnum.FINISH.getValue().equals(requestStatus)) {
                    result.setStatus(CostManageStatusEnum.CONFIRMED.getValue());
                    result.setStatusName(CostManageStatusEnum.CONFIRMED.getName());
                }
            }
        } else {
            // 导入数据为确认状态
            statusEnum = EnumUtils.getEnumByValue(CostManageStatusEnum.class, latestVersion.getStatus());
            result.setStatus(statusEnum.getValue());
            result.setStatusName(statusEnum.getName());
        }
    }

    /**
     * 处理活动列表
     */
    private void processActivities(ProjectSchedulePlanVO result, List<ProjectScheduleActivityPlanVO> activityEntityList, Long projectId) {
        // 获取所有文件集合
        String corroboration = activityEntityList.stream()
                .filter(c -> StrUtil.isNotBlank(c.getCorroboration()))
                .map(ProjectScheduleActivityPlanVO::getCorroboration)
                .collect(Collectors.joining(StrUtil.COMMA));
        Map<Long, SysFile> fileMap = StrUtil.isNotBlank(corroboration)
                ? costDeliverTaskService.getFileMap(corroboration)
                : ImmutableMap.of();

        // 获取所有工单成本集合
        Map<String, BigDecimal> assignedCostByStage = new HashMap<>();
        Map<String, BigDecimal> confirmedCostByStage = new HashMap<>();
        populateCostData(assignedCostByStage, confirmedCostByStage, projectId);

        List<ProjectScheduleActivityPlanVO> activities = activityEntityList.stream().map(vo -> {
            vo.setActivityStatusTxt(EnumUtils.getNameByValue(ActivityStatusEnum.class, vo.getActivityStatus()));
            // 获取文件列表
            List<CostCompletionFilesVO> corroborationFileList = fileMap.values().stream()
                    .filter(f -> handleFilesToList(vo.getCorroboration()).contains(f.getId()))
                    .map(file -> new CostCompletionFilesVO()
                            .setFileId(file.getId())
                            .setFileName(file.getOriginal())
                            .setFileUrl(file.getFileUrl()))
                    .collect(Collectors.toList());
            vo.setCorroborationFileList(corroborationFileList);
            vo.setAssignedCost(assignedCostByStage.get(vo.getStage()));
            vo.setConfirmedCost(confirmedCostByStage.get(vo.getStage()));
            return vo;
        }).collect(Collectors.toList());
        result.setActivities(activities);
    }

    /**
     * 获取工单成本数据
     */
    private void populateCostData(Map<String, BigDecimal> assignedCostByStage, Map<String, BigDecimal> confirmedCostByStage, Long projectId) {
        List<CostDeliverTask> costDeliverTasks = costDeliverTaskService
                .lambdaQuery()
                .eq(CostDeliverTask::getProjectId, projectId)
                .eq(CostDeliverTask::getTaskLevel, 1)
                .list();

        // 分配成本
        assignedCostByStage.putAll(costDeliverTasks.stream()
                .filter(task -> StrUtil.isNotBlank(task.getStage()) && StrUtil.isNotBlank(AESEncryptor.justDecrypt(task.getBudgetCost())))
                .collect(Collectors.groupingBy(
                        CostDeliverTask::getStage,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                task -> new BigDecimal(AESEncryptor.justDecrypt(task.getBudgetCost())),
                                BigDecimal::add
                        )
                )));

        // 确认成本
        confirmedCostByStage.putAll(costDeliverTasks.stream()
                .filter(task -> StrUtil.isNotBlank(task.getStage())
                        && StrUtil.isNotBlank(task.getBudgetCost())
                        && CostTaskStatusEnum.YWC.getValue().equals(task.getTaskStatus())
                        && StrUtil.isNotBlank(AESEncryptor.justDecrypt(task.getActualLaborCost())))
                .collect(Collectors.groupingBy(
                        CostDeliverTask::getStage,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                task -> new BigDecimal(AESEncryptor.justDecrypt(task.getActualLaborCost())),
                                BigDecimal::add
                        )
                )));
    }

    private CostManageVersion getLatestCostManageVersion(Long projectId, Long versionId) {
        CostManageVersionDTO versionQuery = CostManageVersionDTO.builder()
                .versionType(CostManageVersionEnum.XMJDJH.getValue())
                .projectId(projectId)
                .id(versionId)
                .build();
        return costManageVersionService.getLatestCostManageVersion(versionQuery);
    }

    @Override
    @Transactional
    public List<Long> batchSaveOrUpdate(ProjectSchedulePlanDTO request) {
        Long projectId = request.getProjectId();
        List<ProjectSchedulePlanActivityDTO> requestActivities = request.getActivities();
        if (CollUtil.isEmpty(requestActivities)) {
            return ListUtil.empty();
        }

        // 获取当前项目最新的项目进度计划
        CostManageVersion latestCostManageVersion = getLatestCostManageVersion(request.getProjectId(), null);
        List<ProjectSchedulePlan> oldSchedulePlanList = getExistSchedulePlanList(projectId, latestCostManageVersion);
        List<ProjectSchedulePlanActivity> oldSchedulePlanActivityList = getOldSchedulePlanActivityList(projectId, latestCostManageVersion);

        // 判断是否需要新增版本
        boolean isSaveOperation = isSaveOperation(oldSchedulePlanList, oldSchedulePlanActivityList, requestActivities);

        // 验证预算成本
        validateBudgetCost(projectId, requestActivities, isSaveOperation);

        // 新增操作创建新的版本
        if (isSaveOperation) {
            latestCostManageVersion = costManageVersionService.saveSchedulePlan(projectId);
        }

        // 创建新的项目进度计划列表
        List<ProjectSchedulePlan> newSchedulePlanList =
                saveOrUpdateSchedulePlanList(projectId, latestCostManageVersion, requestActivities, oldSchedulePlanList, oldSchedulePlanActivityList, isSaveOperation);

        return newSchedulePlanList.stream().map(ProjectSchedulePlan::getId).collect(Collectors.toList());
    }

    /**
     * 获取已存在项目进度计划列表
     *
     * @param projectId
     * @param latestCostManageVersion
     * @return
     */
    private List<ProjectSchedulePlan> getExistSchedulePlanList(Long projectId, CostManageVersion latestCostManageVersion) {
        return null == latestCostManageVersion
                ? ListUtil.empty()
                : baseMapper.selectList(new QueryWrapper<ProjectSchedulePlan>().lambda()
                .eq(ProjectSchedulePlan::getProjectId, projectId)
                .eq(ProjectSchedulePlan::getVersionId, latestCostManageVersion.getId())
                .eq(ProjectSchedulePlan::getDelFlag, YesOrNoEnum.NO.getValue()));
    }

    private List<ProjectSchedulePlanActivity> getOldSchedulePlanActivityList(Long projectId, CostManageVersion latestCostManageVersion) {
        return null == latestCostManageVersion
                ? ListUtil.empty()
                : schedulePlanActivityMapper.selectList(new QueryWrapper<ProjectSchedulePlanActivity>().lambda()
                .eq(ProjectSchedulePlanActivity::getProjectId, projectId)
                .eq(ProjectSchedulePlanActivity::getVersionId, latestCostManageVersion.getId())
                .eq(ProjectSchedulePlanActivity::getDelFlag, YesOrNoEnum.NO.getValue()));
    }

    private List<CostManageEstimationResultsVO> validateBudgetCost(Long projectId,
                                                                   List<ProjectSchedulePlanActivityDTO> requestActivities,
                                                                   boolean isSaveOperation) {
        List<CostManageEstimationResultsVO> bbCostBudget = getBBCostBudget(projectId);
        if (!isSaveOperation) {
            if (CollUtil.isEmpty(bbCostBudget)) {
                throw new BusinessException("请先维护成本管理-B表成本数据");
            }
            BigDecimal budgetCost = bbCostBudget.stream()
                    .map(CostManageEstimationResultsVO::getBudgetAmountIncludedTax)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal requestBudgetCost = requestActivities.stream()
                    .collect(Collectors.toMap(
                            ProjectSchedulePlanActivityDTO::getStage, // key: stage
                            ProjectSchedulePlanActivityDTO::getBudgetCost, // value: budgetCost
                            (existing, replacement) -> existing)) // 如果stage重复，保留第一个budgetCost
                    .values()
                    .stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (requestBudgetCost.compareTo(budgetCost) < 0) {
                throw new BusinessException("预算人工成本有结余，请分配完");
            } else if (requestBudgetCost.compareTo(budgetCost) > 0) {
                throw new BusinessException("预算人工成本超支，请重新分配");
            }
        }
        return bbCostBudget;
    }

    private List<ProjectSchedulePlan> saveOrUpdateSchedulePlanList(Long projectId,
                                                                   CostManageVersion costManageVersion,
                                                                   List<ProjectSchedulePlanActivityDTO> requestActivities,
                                                                   List<ProjectSchedulePlan> oldSchedulePlanList,
                                                                   List<ProjectSchedulePlanActivity> oldSchedulePlanActivityList,
                                                                   boolean isSaveOperation) {
        Long versionId = costManageVersion.getId();
        List<ProjectSchedulePlan> newSchedulePlanList = new ArrayList<>();
        List<ProjectSchedulePlanActivityDTO> newActivityList = new ArrayList<>();

        // 按阶段分组 key-阶段 value-对应阶段下的活动 遍历创建新的项目进度计划
        Map<String, List<ProjectSchedulePlanActivityDTO>> requestStageGroupMap = requestActivities.stream()
                .collect(Collectors.groupingBy(ProjectSchedulePlanActivityDTO::getStage));
        for (Map.Entry<String, List<ProjectSchedulePlanActivityDTO>> requestStage : requestStageGroupMap.entrySet()) {
            String stage = requestStage.getKey();
            List<ProjectSchedulePlanActivityDTO> stageActivities = requestStage.getValue();
            Integer stageSort = stageActivities.get(0).getStageSort();
            BigDecimal budgetCost = stageActivities.get(0).getBudgetCost();

            ProjectSchedulePlan entity;
            if (isSaveOperation) {
                entity = ProjectSchedulePlan.builder()
                        .projectId(projectId)
                        .versionId(versionId)
                        .budgetCost(budgetCost)
                        .sort(stageSort)
                        .stage(stage)
                        .build();
                BaseBuildEntityUtil.buildInsert(entity);
            } else {
                entity = oldSchedulePlanList.stream()
                        .filter(schedulePlan -> schedulePlan.getStage().equals(stage))
                        .findFirst()
                        .orElse(null);
                BaseBuildEntityUtil.buildUpdate(entity);
            }

            stageActivities = stageActivities.stream().peek(activityDTO -> {
                activityDTO.setPlanId(entity.getId());
                activityDTO.setVersionId(versionId);
            }).collect(Collectors.toList());
            newActivityList.addAll(stageActivities);
            newSchedulePlanList.add(entity);
        }

        // 数据入库
        if (isSaveOperation) {
            this.saveBatch(newSchedulePlanList);
        } else {
            this.updateBatchById(newSchedulePlanList);
        }
        projectSchedulePlanActivityService.batchSaveOrUpdate(projectId, newActivityList, oldSchedulePlanActivityList, isSaveOperation);

        return newSchedulePlanList;
    }

    /**
     * 获取B表成本
     *
     * @param projectId
     * @return true-存在 false-不存在
     */
    private List<CostManageEstimationResultsVO> getBBCostBudget(Long projectId) {
        //查询最新版本
        CostManageVersionDTO costManageVersionDTO = CostManageVersionDTO.builder()
                .costBudgetTypeList(Arrays.asList(CostBudgetTypeEnum.BBCB.getValue()))
                .projectId(projectId)
                .build();
        CostManageVersion costVersion = costManageVersionMapper.getLatestCostManageEstimation(costManageVersionDTO);
        if (null == costVersion) {
            return ListUtil.empty();
        }

        List<Long> preAccountOaIdList = Arrays.asList(xsfyOaId, xmglfysqrgOaId);

        //查询当前最新预算成本列表
        List<CostManageEstimationResultsVO> results =
                CollUtil.emptyIfNull(costManageEstimationResultsMapper.findByVersionId(Arrays.asList(costVersion.getId())))
                        .stream()
                        .filter(e -> CostTypeEnum.RGCB.getValue().equals(e.getCostType())
                                && !preAccountOaIdList.contains(e.getAccountOaId())
                                && !BigDecimal.ZERO.equals(e.getBudgetAmountIncludedTax()))
                        .collect(Collectors.toList());


        return results;
    }

    private boolean isSaveOperation(List<ProjectSchedulePlan> oldSchedulePlanList,
                                    List<ProjectSchedulePlanActivity> oldSchedulePlanActivityList,
                                    List<ProjectSchedulePlanActivityDTO> newSchedulePlanActivityList) {
        // 不存在项目进度计划或者行数变更则直接判断为新增操作
        if (CollUtil.isEmpty(oldSchedulePlanActivityList)
                || newSchedulePlanActivityList.size() != oldSchedulePlanActivityList.size()) {
            return true;
        }

        // key-阶段 value-项目进度计划
        Map<String, ProjectSchedulePlan> oldSchedulePlanMap = oldSchedulePlanList.stream()
                .collect(Collectors.toMap(ProjectSchedulePlan::getStage, Function.identity(), (existing, replacement) -> existing));

        // key-阶段-活动 value-项目进度计划活动
        Map<String, ProjectSchedulePlanActivity> oldActivityMap = oldSchedulePlanActivityList.stream()
                .collect(Collectors.toMap(
                        activity -> StrUtil.format("{}-{}", activity.getStage(), activity.getActivity()),
                        activity -> activity,
                        (existing, replacement) -> existing
                ));
        for (ProjectSchedulePlanActivityDTO newActivity : newSchedulePlanActivityList) {
            String stage = newActivity.getStage();
            ProjectSchedulePlan oldSchedulePlan = oldSchedulePlanMap.get(stage);
            ProjectSchedulePlanActivity oldActivity =
                    oldActivityMap.get(StrUtil.format("{}-{}", stage, newActivity.getActivity()));
            // 未找到对应旧数据则判断为新增操作
            if (null == oldSchedulePlan || null == oldActivity) {
                return true;
            }

            // 存在则判断是否内容变更
            if (!StrUtil.equals(Optional.ofNullable(newActivity.getDeliverables()).orElse(StrUtil.EMPTY), Optional.ofNullable(oldActivity.getDeliverables()).orElse(StrUtil.EMPTY))
                    || !StrUtil.equals(Optional.ofNullable(newActivity.getCompletionCriteria()).orElse(StrUtil.EMPTY), Optional.ofNullable(oldActivity.getCompletionCriteria()).orElse(StrUtil.EMPTY))
                    || !Objects.equals(newActivity.getPlanStartDate(), oldActivity.getPlanStartDate())
                    || !Objects.equals(newActivity.getPlanEndDate(), oldActivity.getPlanEndDate())
                    || newActivity.getBudgetCost().compareTo(oldSchedulePlan.getBudgetCost()) != 0) {
                return true;
            }

        }

        return false;
    }

    private static List<Long> handleFilesToList(String files) {
        if (StrUtil.isBlank(files)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(files, StrPool.COMMA))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

}
