package com.gok.pboot.pms.didi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gok.base.admin.feign.RemoteFinancialService;
import com.gok.base.admin.vo.AccountRelationshipVO;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.upms.dto.InnerDeptMapDto;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.components.common.user.PigxUser;
import com.gok.module.file.dto.InnerDoc;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.core.util.DateUtils;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.RSAUtils;
import com.gok.pboot.pms.common.base.StatisticsPage;
import com.gok.pboot.pms.config.ExpenseItemConfig;
import com.gok.pboot.pms.cost.entity.domain.CostConfigAccount;
import com.gok.pboot.pms.didi.entity.domain.DdCommonOrder;
import com.gok.pboot.pms.didi.entity.domain.DdFlightTicketOrder;
import com.gok.pboot.pms.didi.entity.domain.DdHotelOrder;
import com.gok.pboot.pms.didi.entity.domain.DdVehicleOrder;
import com.gok.pboot.pms.didi.entity.dto.DdFlightTicketOrderImportDTO;
import com.gok.pboot.pms.didi.entity.dto.DdHotelOrderImportDTO;
import com.gok.pboot.pms.didi.entity.dto.DdVehicleOrderImportDTO;
import com.gok.pboot.pms.didi.entity.dto.MergedOrderData;
import com.gok.pboot.pms.didi.entity.vo.*;
import com.gok.pboot.pms.didi.mapper.DdOrderMapper;
import com.gok.pboot.pms.didi.service.IDdOrderService;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.vo.OaAccountVO;
import com.gok.pboot.pms.entity.vo.ProjectAccountVO;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.enumeration.BusinessOrderReimbursedStatusEnum;
import com.gok.pboot.pms.enumeration.OAFormTypeEnum;
import com.gok.pboot.pms.enumeration.OrderTypeEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.handler.BcpDataHandler;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.oa.client.OaClient;
import com.gok.pboot.pms.oa.dto.*;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.gok.pboot.pms.service.RosterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 滴滴订单管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DdOrderServiceImpl implements IDdOrderService {
    private final IProjectInfoService projectInfoService;
    private final DdOrderMapper ddOrderMapper;
    private final DbApiUtil dbApiUtil;
    private final OaUtil oaUtil;
    private final OaClient oaClient;
    private final BcpDataHandler bcpDataHandler;
    private final RosterService rosterService;
    private final ProjectScopeHandle projectScopeHandle;
    @Resource
    private RemoteOutMultiDeptService remoteOutMultiDeptService;
    @Resource
    private RemoteFinancialService remoteFinancialService;

    @Resource
    private RemoteBcpDictService remoteBcpDictService;

    @Resource
    private ExpenseItemConfig expenseItemConfig;

    @Resource
    private ApplicationContext applicationContext;

    private static final String ORDER_TYPE = "orderType";

    @Value("${oa.url.httpUrl}")
    private String url;

    @Value("${oa.resourcesAppId}")
    private String appId;

    @Value("${oa.spk}")
    private String spk;

    @Value("${oa.deptCatName}")
    private String deptCatName;

    @Value("${oa.systemCode}")
    private String systemCode;

    @Override
    public StatisticsPage<DdFlightTicketOrderFindPageVO> findFlightTicketPageList(StatisticsPage<DdFlightTicketOrderFindPageVO> page, Map<String, Object> queryDTO) {
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (!Boolean.TRUE.equals(dataScope.getIsAll())) {
            if (CollUtil.isEmpty(dataScope.getDeptIdList())
                    && CollUtil.isEmpty(dataScope.getUserIdList())) {
                return page;
            }
            scopeHandle(queryDTO, dataScope);
        }
        queryDTO.put(ORDER_TYPE, OrderTypeEnum.FLIGHT_TICKET.getCode());
        ddOrderMapper.findFlightTicketPageList(page, queryDTO);
        List<DdFlightTicketOrderFindPageVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return page;
        }
        // 查询部门信息
        Map<Long, SysDept> allSysDeptMap = bcpDataHandler.getAllSysDeptMap();
        records.forEach(item -> {
            item.setInitiationStatusStr(EnumUtils.getNameByValue(BusinessOrderReimbursedStatusEnum.class, item.getInitiationStatus()));
            item.setBookingDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getBookingDeptId(), StringPool.DASH));
            item.setPassengerDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getPassengerDeptId(), StringPool.DASH));
        });
        if (page.getTotal() != 0) {
            Map<String, BigDecimal> statistics = ddOrderMapper.flightTicketOrderStatistics(queryDTO);
            page.setStatistics(statistics);
        }
        return page;
    }

    @Override
    public StatisticsPage<DdHotelOrderFindPageVO> findHotelPageList(StatisticsPage<DdHotelOrderFindPageVO> page, Map<String, Object> queryDTO) {
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (!Boolean.TRUE.equals(dataScope.getIsAll())) {
            if (CollUtil.isEmpty(dataScope.getDeptIdList())
                    && CollUtil.isEmpty(dataScope.getUserIdList())) {
                return page;
            }
            scopeHandle(queryDTO, dataScope);
        }
        queryDTO.put(ORDER_TYPE, OrderTypeEnum.HOTEL.getCode());
        ddOrderMapper.findHotelPageList(page, queryDTO);
        List<DdHotelOrderFindPageVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return page;
        }
        // 查询部门信息
        Map<Long, SysDept> allSysDeptMap = bcpDataHandler.getAllSysDeptMap();
        records.forEach(item -> {
            item.setInitiationStatusStr(EnumUtils.getNameByValue(BusinessOrderReimbursedStatusEnum.class, item.getInitiationStatus()));
            item.setBookingDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getBookingDeptId(), StringPool.DASH));
            item.setCheckinDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getCheckinDeptId(), StringPool.DASH));
        });
        if (page.getTotal() != 0) {
            Map<String, BigDecimal> statistics = ddOrderMapper.hotelOrderStatistics(queryDTO);
            page.setStatistics(statistics);
        }
        return page;
    }

    @Override
    public StatisticsPage<DdVehicleOrderFindPageVO> findVehiclePageList(StatisticsPage<DdVehicleOrderFindPageVO> page, Map<String, Object> queryDTO) {
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (!Boolean.TRUE.equals(dataScope.getIsAll())) {
            if (CollUtil.isEmpty(dataScope.getDeptIdList())
                    && CollUtil.isEmpty(dataScope.getUserIdList())) {
                return page;
            }
            scopeHandle(queryDTO, dataScope);
        }
        queryDTO.put(ORDER_TYPE, OrderTypeEnum.VEHICLE.getCode());
        ddOrderMapper.findVehiclePageList(page, queryDTO);
        List<DdVehicleOrderFindPageVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return page;
        }
        // 查询部门信息
        Map<Long, SysDept> allSysDeptMap = bcpDataHandler.getAllSysDeptMap();
        records.forEach(item -> {
            item.setInitiationStatusStr(EnumUtils.getNameByValue(BusinessOrderReimbursedStatusEnum.class, item.getInitiationStatus()));
            item.setBookingDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getBookingDeptId(), StringPool.DASH));
            item.setPassengerDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getPassengerDeptId(), StringPool.DASH));
        });
        if (page.getTotal() != 0) {
            Map<String, BigDecimal> statistics = ddOrderMapper.vehicleOrderStatistics(queryDTO);
            page.setStatistics(statistics);
        }
        return page;
    }

    private void scopeHandle(Map<String, Object> queryDTO,
                             SysUserDataScopeVO dataScope) {
        queryDTO.put("scope", true);
        queryDTO.put("userIdList", dataScope.getUserIdList());
        List<Long> deptIdList = dataScope.getDeptIdList();
        if (CollUtil.isEmpty(deptIdList)) {
            return;
        }
        // 获取合同主体维度架构数据
        List<MultiDimensionDeptDto> multiDeptList = bcpDataHandler.getMultiDeptList("合同主体");
        Map<Long, String> multiDeptMap = CollStreamUtil.toMap(multiDeptList, MultiDimensionDeptDto::getDeptId, MultiDimensionDeptDto::getName);
        Set<String> subjectNames = new HashSet<>();
        deptIdList.forEach(deptId -> {
            String subject = multiDeptMap.get(deptId);
            if (subject != null) {
                subjectNames.add(subject);
            }
        });
        queryDTO.put("subjectNames", subjectNames);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DdOrderImportResultVO importOrders(MultipartFile file) {
        List<String> errorMessages = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(0);
        try {
            // 读取机票订单数据
            List<DdFlightTicketOrderImportDTO> flightTicketOrders = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), DdFlightTicketOrderImportDTO.class, new AnalysisEventListener<DdFlightTicketOrderImportDTO>() {
                @Override
                public void invoke(DdFlightTicketOrderImportDTO data, AnalysisContext context) {
                    if (data != null && StringUtils.hasText(data.getOrderNo())) {
                        flightTicketOrders.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 处理机票订单数据
                    processFlightTicketOrders(flightTicketOrders, errorMessages, successCount);
                }
            }).headRowNumber(2).sheet("机票订单").doRead();

            // 读取酒店订单数据
            List<DdHotelOrderImportDTO> hotelOrders = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), DdHotelOrderImportDTO.class, new AnalysisEventListener<DdHotelOrderImportDTO>() {
                @Override
                public void invoke(DdHotelOrderImportDTO data, AnalysisContext context) {
                    if (data != null && StringUtils.hasText(data.getOrderNo())) {
                        hotelOrders.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 处理酒店订单数据
                    processHotelOrders(hotelOrders, errorMessages, successCount);
                }
            }).headRowNumber(2).sheet("酒店订单").doRead();

            // 读取用车订单数据
            List<DdVehicleOrderImportDTO> vehicleOrders = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), DdVehicleOrderImportDTO.class, new AnalysisEventListener<DdVehicleOrderImportDTO>() {
                @Override
                public void invoke(DdVehicleOrderImportDTO data, AnalysisContext context) {
                    if (data != null && StringUtils.hasText(data.getOrderNo())) {
                        vehicleOrders.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 处理用车订单数据
                    processVehicleOrders(vehicleOrders, errorMessages, successCount);
                }
            }).headRowNumber(2).sheet("用车订单").doRead();

        } catch (IOException e) {
            log.error("导入订单数据失败", e);
            errorMessages.add("文件读取失败：" + e.getMessage());
        }

        return new DdOrderImportResultVO(successCount.get(), errorMessages);
    }

    @Override
    public void generateReimbursement(Map<String, Object> queryDTO) {
        PigxUser user = SecurityUtils.getUser();
        // 获取所有类型的可发起报销订单
        List<DdCommonOrder> allOrders = getAllCanInitiateOrders(queryDTO);
        if (allOrders.isEmpty()) {
            throw new BusinessException("没有可发起报销的订单");
        }
        //获取中台核算部门关系
        Map<Long, List<InnerDeptMapDto>> innerDeptMap = remoteOutMultiDeptService.getInnerDeptMapDto(deptCatName).getData().stream().collect(Collectors.groupingBy(InnerDeptMapDto::getInnerDeptId));

        // 通过Spring上下文获取代理对象来调用异步方法
        DdOrderServiceImpl self = applicationContext.getBean(DdOrderServiceImpl.class);
        self.generateReimbursementAsync(queryDTO, user, allOrders, innerDeptMap);
    }

    @Async
    public void generateReimbursementAsync(Map<String, Object> queryDTO, PigxUser user, List<DdCommonOrder> allOrders, Map<Long, List<InnerDeptMapDto>> innerDeptMap) {
        try {
            log.info("开始生成报销单，查询条件：{}", queryDTO);
            // 获取OA用户信息
            OaAccountVO oaAccountVO = dbApiUtil.getOaAccountInfoByUserId(user.getId());
            if (oaAccountVO == null || oaAccountVO.getOaId() == null) {
                log.error("获取OA用户信息失败，用户ID：{}", user.getId());
                return;
            }
            // 获取token
            String token = oaUtil.getToken();
            if (StrUtil.isBlank(token)) {
                log.error("获取OA token失败");
                return;
            }
            // 获取workflowId
            Map<Integer, Integer> workflowIdMap = new HashMap<>();
            Integer fybxId = dbApiUtil.getWorkflowIdByName(OAFormTypeEnum.FYBXD.getName());
            Integer sjfybxId = dbApiUtil.getWorkflowIdByName(OAFormTypeEnum.SJFYBX.getName());
            Integer zjxmfybxId = dbApiUtil.getWorkflowIdByName(OAFormTypeEnum.ZJXMFYBX.getName());
            workflowIdMap.put(OAFormTypeEnum.FYBXD.getValue(), fybxId);
            workflowIdMap.put(OAFormTypeEnum.SJFYBX.getValue(), sjfybxId);
            workflowIdMap.put(OAFormTypeEnum.ZJXMFYBX.getValue(), zjxmfybxId);

            log.info("找到可发起报销的订单数量：{}", allOrders.size());

            //获取数字财务指标科目关系
            Map<Long, List<AccountRelationshipVO>> indicatorAccountRelationshipMap = remoteFinancialService.getIndicatorAccountRelationshipBySystemCode(systemCode, SecurityConstants.FROM_IN).getData().stream().collect(Collectors.groupingBy(AccountRelationshipVO::getAccountingDeptId));

            //获取OA部门月预算
            Map<Long, List<OaBmyysbDTO>> bmyysbMap = dbApiUtil.getInfoByBmyysb().stream().collect(Collectors.groupingBy(OaBmyysbDTO::getYsbm));

            //获取OA部门
            Map<Long, OaDeptDTO> deptMap = dbApiUtil.getInfoByDept().stream().collect(Collectors.toMap(OaDeptDTO::getDeptId, v -> v));

            List<Long> projectIds = allOrders.stream().map(DdCommonOrder::getProjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, ProjectAccountVO> projectAccountMap = new HashMap<>();
            Map<Long, List<OaXmysbDTO>> xmysbMap = new HashMap<>();
            if (!projectIds.isEmpty()) {
                // 获取OA项目台帐
                projectAccountMap = dbApiUtil.getProjectAccountByName(null, projectIds).stream().collect(Collectors.toMap(ProjectAccountVO::getProjectId, v -> v));

                //获取OA预算台账
                xmysbMap = dbApiUtil.getInfoByXmysb(null, projectIds).stream().collect(Collectors.groupingBy(OaXmysbDTO::getXmmc));
            }

            //查询OA在途流程预算
            Map<Integer, List<OaBudgetFlowDTO>> budgetFlowMap = dbApiUtil.getBudgetByFlow().stream().collect(Collectors.groupingBy(OaBudgetFlowDTO::getFlowType));

            // 根据流程类型、公司、项目(一级部门)分组生成报销单
            Map<String, List<DdCommonOrder>> groupedOrders = groupOrdersByReimbursementRule(allOrders, bmyysbMap, deptMap);

            //根据流程类型、项目(部门)分组校验预算
            Map<String, List<DdCommonOrder>> budgetOrders = groupOrdersByFlowProjectGroup(allOrders, deptMap);

            //校验预算
            checkBudget(budgetOrders, bmyysbMap, xmysbMap, budgetFlowMap, innerDeptMap, indicatorAccountRelationshipMap);

            // 调用OA接口生成报销单
            for (Map.Entry<String, List<DdCommonOrder>> entry : groupedOrders.entrySet()) {
                String groupKey = entry.getKey();
                List<DdCommonOrder> orderGroup = entry.getValue();
                log.info("生成报销单，分组键：{}，订单数量：{}", groupKey, orderGroup.size());
                try {
                    // 解析分组键，获取OA表单类型
                    String[] keyParts = groupKey.split("_");
                    Integer oaFormType = Integer.valueOf(keyParts[0]);
                    Long companyId = Long.valueOf(keyParts[1]);
                    Long deptProjectId = Long.valueOf(keyParts[2]);

                    // 调用OA接口创建报销单
                    String errorMsg = callOACreateReimbursement(oaAccountVO, workflowIdMap, token, oaFormType, orderGroup, companyId, deptProjectId, projectAccountMap);
                    if (errorMsg != null) {
                        log.error("创建OA报销单失败，分组键：{}", groupKey);
                        orderGroup.forEach(order -> {
                            if (order.getInitiationRemark() == null) {
                                order.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_OA.getValue());
                                order.setInitiationRemark(errorMsg);
                            }
                        });
                    }
                } catch (Exception e) {
                    log.error("创建OA报销单失败，分组键：{}，错误：{}", groupKey, e.getMessage(), e);
                    orderGroup.forEach(order -> {
                        if (order.getInitiationRemark() == null) {
                            order.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_OA.getValue());
                            order.setInitiationRemark(e.getMessage());
                        }
                    });
                }
            }

            // 批量更新订单状态
            updateOrderReimbursementStatus(allOrders);
        } catch (Exception e) {
            log.error("生成报销失败", e);
        }
    }

    /**
     * 获取所有类型的可发起报销订单
     */
    private List<DdCommonOrder> getAllCanInitiateOrders(Map<String, Object> queryDTO) {
        List<DdCommonOrder> allOrders = new ArrayList<>();
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (!Boolean.TRUE.equals(dataScope.getIsAll())) {
            if (CollUtil.isEmpty(dataScope.getDeptIdList())
                    && CollUtil.isEmpty(dataScope.getUserIdList())) {
                return allOrders;
            }
            scopeHandle(queryDTO, dataScope);
        }

        // 查询机票订单并转换为通用订单
        queryDTO.put(ORDER_TYPE, OrderTypeEnum.FLIGHT_TICKET.getCode());
        List<DdFlightTicketOrder> flightOrders = ddOrderMapper.findCanInitiateFlightTicketOrders(queryDTO);
        for (DdFlightTicketOrder order : flightOrders) {
            DdCommonOrder commonOrder = new DdCommonOrder();
            BeanUtil.copyProperties(order, commonOrder);
            commonOrder.setOrderType(OrderTypeEnum.FLIGHT_TICKET);
            commonOrder.setInitiationRemark(null);
            allOrders.add(commonOrder);
        }

        // 查询酒店订单并转换为通用订单
        queryDTO.put(ORDER_TYPE, OrderTypeEnum.HOTEL.getCode());
        List<DdHotelOrder> hotelOrders = ddOrderMapper.findCanInitiateHotelOrders(queryDTO);
        for (DdHotelOrder order : hotelOrders) {
            DdCommonOrder commonOrder = new DdCommonOrder();
            BeanUtil.copyProperties(order, commonOrder);
            commonOrder.setOrderType(OrderTypeEnum.HOTEL);
            commonOrder.setInitiationRemark(null);
            allOrders.add(commonOrder);
        }

        // 查询用车订单并转换为通用订单
        queryDTO.put(ORDER_TYPE, OrderTypeEnum.VEHICLE.getCode());
        List<DdVehicleOrder> vehicleOrders = ddOrderMapper.findCanInitiateVehicleOrders(queryDTO);
        for (DdVehicleOrder order : vehicleOrders) {
            DdCommonOrder commonOrder = new DdCommonOrder();
            BeanUtil.copyProperties(order, commonOrder);
            commonOrder.setOrderType(OrderTypeEnum.VEHICLE);
            commonOrder.setInitiationRemark(null);
            allOrders.add(commonOrder);
        }

        // 查询部门信息
        Map<Long, SysDept> allSysDeptMap = bcpDataHandler.getAllSysDeptMap();
        allOrders.forEach(item -> {
            item.setBookingDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getBookingDeptId(), StringPool.DASH));
            item.setPassengerDeptName(bcpDataHandler.collectFullName(allSysDeptMap, item.getPassengerDeptId(), StringPool.DASH));
        });

        return allOrders;
    }

    /**
     * 根据报销规则对订单进行分组
     */
    private Map<String, List<DdCommonOrder>> groupOrdersByReimbursementRule(List<DdCommonOrder> orders,
                                                                            Map<Long, List<OaBmyysbDTO>> bmyysbMap,
                                                                            Map<Long, OaDeptDTO> deptMap) {
        Map<String, List<DdCommonOrder>> groupedOrders = new HashMap<>();
        for (DdCommonOrder order : orders) {
            String groupKey = getReimbursementGroupKey(order, bmyysbMap, deptMap);
            if (groupKey != null) {
                groupedOrders.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(order);
            }
        }

        return groupedOrders;
    }

    /**
     * 获取报销分组键
     */
    private String getReimbursementGroupKey(DdCommonOrder order, Map<Long, List<OaBmyysbDTO>> bmyysbMap, Map<Long, OaDeptDTO> deptMap) {
        // 场景一：所属项目为空，同【所属公司】、同【预算一级部门】进行报销单合并
        if (order.getProjectId() == null && order.getCompanyId() != null && order.getCostCenterId() != null) {
            // 成本中心ID通过OA部门转成OA部门id，OA部门id通过OA部门月预算获取预算一级部门
            Long oaDeptId = deptMap.get(order.getCostCenterId()).getOaDeptId();
            List<OaBmyysbDTO> bmyysbList = bmyysbMap.get(oaDeptId);
            order.setOaDeptId(oaDeptId);
            if (oaDeptId != null && bmyysbList != null && !bmyysbList.isEmpty()) {
                Long budgetFirstLevelDept = bmyysbList.get(0).getYsyjbm();
                if (budgetFirstLevelDept != null) {
                    return OAFormTypeEnum.FYBXD.getValue() + "_" + order.getCompanyId() + "_" + budgetFirstLevelDept + "_" + order.getOrderType().getType();
                }
            }
        }
        // 场景二：成本中心为空，同OA【所属公司】、同OA【项目名称】进行报销单合并
        else if (order.getCostCenterId() == null && order.getCompanyId() != null && order.getProjectId() != null) {
            return getProjectReimbursementType(order.getProjectStatus()) + "_" + order.getCompanyId() + "_" + order.getProjectId() + "_" + order.getOrderType().getType();
        }
        //设置状态备注
        log.info("未匹配到报销规则，成本中心无法找到对应预算，订单号：{}", order.getOrderNo());
        order.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue());
        order.setInitiationRemark("未匹配到报销规则，成本中心无法找到对应预算");
        return null;
    }

    /**
     * 根据报销规则对订单进行分组
     */
    private Map<String, List<DdCommonOrder>> groupOrdersByFlowProjectGroup(List<DdCommonOrder> orders,
                                                                           Map<Long, OaDeptDTO> deptMap) {
        Map<String, List<DdCommonOrder>> groupedOrders = new HashMap<>();
        for (DdCommonOrder order : orders) {
            String groupKey = getFlowProjectGroupKey(order, deptMap);
            if (groupKey != null) {
                groupedOrders.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(order);
            }
        }

        return groupedOrders;
    }

    /**
     * 获取报销分组键
     */
    private String getFlowProjectGroupKey(DdCommonOrder order, Map<Long, OaDeptDTO> deptMap) {
        // 场景一：所属项目为空，同OA【流程类型】、同【预算部门】进行报销单合并
        if (order.getProjectId() == null && order.getCompanyId() != null && order.getCostCenterId() != null) {
            Long oaDeptId = deptMap.get(order.getCostCenterId()).getOaDeptId();
            if (oaDeptId != null) {
                return OAFormTypeEnum.FYBXD.getValue() + "_" + oaDeptId;
            }
        }
        // 场景二：成本中心为空，同OA【流程类型】、同OA【项目名称】进行报销单合并
        else if (order.getCostCenterId() == null && order.getCompanyId() != null && order.getProjectId() != null) {
            return getProjectReimbursementType(order.getProjectStatus()) + "_" + order.getProjectId();
        }
        //设置状态备注
        log.info("校验预算异常，未匹配到报销规则，成本中心无法找到对应预算，订单号：{}", order.getOrderNo());
        order.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue());
        order.setInitiationRemark("未匹配到报销规则，成本中心无法找到对应预算");
        return null;
    }


    /**
     * 根据项目状态获取报销单类型
     */
    private Integer getProjectReimbursementType(Integer projectStatus) {
        // 根据项目状态判断报销单类型
        if (projectStatus != null) {
            if (ProjectStatusEnum.SJ.getValue().equals(projectStatus)) {
                // 商机项目 - XM-28商机项目费用报销
                return OAFormTypeEnum.SJFYBX.getValue();
            } else if (ProjectStatusEnum.ZJ.getValue().equals(projectStatus)) {
                // 在建项目 - XM-15在建项目费用报销
                return OAFormTypeEnum.ZJXMFYBX.getValue();
            }
        }

        return OAFormTypeEnum.ZJXMFYBX.getValue(); // 默认在建项目
    }

    /**
     * 校验预算
     */
    private void checkBudget(Map<String, List<DdCommonOrder>> budgetOrders,
                             Map<Long, List<OaBmyysbDTO>> bmyysbMap,
                             Map<Long, List<OaXmysbDTO>> xmysbMap,
                             Map<Integer, List<OaBudgetFlowDTO>> budgetFlowMap,
                             Map<Long, List<InnerDeptMapDto>> innerDeptMap,
                             Map<Long, List<AccountRelationshipVO>> indicatorAccountRelationshipMap) {
        //查询OA科目
        Map<Long, CostConfigAccount> subjectConfigMap = dbApiUtil.getCostSubjectConfigInfoList().stream().collect(Collectors.toMap(CostConfigAccount::getOaId, v -> v));
        for (Map.Entry<String, List<DdCommonOrder>> entry : budgetOrders.entrySet()) {
            String groupKey = entry.getKey();
            List<DdCommonOrder> orderGroup = entry.getValue();

            // 解析分组键，获取OA表单类型
            String[] keyParts = groupKey.split("_");
            Integer oaFormType = Integer.valueOf(keyParts[0]);
            DdCommonOrder order = orderGroup.isEmpty() ? null : orderGroup.get(0);
            StringBuilder initiationRemark = new StringBuilder();
            BigDecimal totalAmount = orderGroup.stream().map(DdCommonOrder::getCompanyActualPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (Objects.equals(OAFormTypeEnum.FYBXD.getValue(), oaFormType)) {
                //校验部门预算
                List<OaBudgetFlowDTO> budgetFlowList = budgetFlowMap.get(OAFormTypeEnum.FYBXD.getValue());
                OaBmyysbDTO bmyysb = validateDepartmentBudget(order, totalAmount, innerDeptMap, indicatorAccountRelationshipMap, bmyysbMap, budgetFlowList, initiationRemark);
                orderGroup.forEach(o -> {
                    if (bmyysb == null) {
                        o.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue());
                        if (o.getInitiationRemark() == null) {
                            o.setInitiationRemark(String.valueOf(initiationRemark));
                        }
                    } else {
                        o.setBmyysb(bmyysb);
                    }
                });
            } else if (Objects.equals(OAFormTypeEnum.SJFYBX.getValue(), oaFormType)) {
                //校验商机项目预算
                List<OaBudgetFlowDTO> budgetFlowList = budgetFlowMap.get(OAFormTypeEnum.SJFYBX.getValue());
                OaXmysbDTO xmysb = validateProjectBudget(order, totalAmount, expenseItemConfig.getSjfybx().get("kmmc"), xmysbMap, budgetFlowList, subjectConfigMap, initiationRemark);
                orderGroup.forEach(o -> {
                    if (xmysb == null) {
                        o.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue());
                        if (o.getInitiationRemark() == null) {
                            o.setInitiationRemark(String.valueOf(initiationRemark));
                        }
                    } else {
                        o.setXmysb(xmysb);
                    }
                });

            } else if (Objects.equals(OAFormTypeEnum.ZJXMFYBX.getValue(), oaFormType)) {
                //校验在建项目预算
                List<OaBudgetFlowDTO> budgetFlowList = budgetFlowMap.get(OAFormTypeEnum.ZJXMFYBX.getValue());
                OaXmysbDTO xmysb = validateProjectBudget(order, totalAmount, expenseItemConfig.getZjxmfybx().get("kmmc"), xmysbMap, budgetFlowList, subjectConfigMap, initiationRemark);
                orderGroup.forEach(o -> {
                    if (xmysb == null) {
                        o.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATION_ERROR_BUDGET.getValue());
                        if (o.getInitiationRemark() == null) {
                            o.setInitiationRemark(String.valueOf(initiationRemark));
                        }
                    } else {
                        o.setXmysb(xmysb);
                    }
                });
            }
        }

    }

    /**
     * 校验部门月预算
     */
    private OaBmyysbDTO validateDepartmentBudget(DdCommonOrder order, BigDecimal totalAmount,
                                                 Map<Long, List<InnerDeptMapDto>> innerDeptMap,
                                                 Map<Long, List<AccountRelationshipVO>> indicatorAccountRelationshipMap,
                                                 Map<Long, List<OaBmyysbDTO>> bmyysbMap,
                                                 List<OaBudgetFlowDTO> budgetFlowList,
                                                 StringBuilder initiationRemark) {
        if (order == null) {
            initiationRemark.append("订单为空");
            return null;
        }
        List<OaBmyysbDTO> deptBudgets = bmyysbMap.get(order.getOaDeptId());
        if (CollUtil.isEmpty(deptBudgets)) {
            initiationRemark.append("未查找到相关部门预算，部门id:").append(order.getOaDeptId());
            return null;
        }
        //行政部门转核算部门
        Long virtualDeptId = innerDeptMap.get(order.getCostCenterId()).isEmpty() ? null :
                innerDeptMap.get(order.getCostCenterId()).get(0).getVirtualDeptId();

        //通过核算部门查询科目编码
        log.info("虚拟部门id：{}，部门科目关系：{}", virtualDeptId, JSONObject.toJSONString(indicatorAccountRelationshipMap));
        List<AccountRelationshipVO> accountRelationshipList = indicatorAccountRelationshipMap.get(virtualDeptId);
        if (accountRelationshipList == null || accountRelationshipList.isEmpty()) {
            return null;
        }
        String accountCode = accountRelationshipList.get(0).getCode();

        // 查找当前月份的预算
        OaBmyysbDTO oaBmyysbDTO = deptBudgets.stream()
                .filter(budget -> budget.getKmdm().equals(accountCode))
                .findFirst()
                .orElse(null);

        if (oaBmyysbDTO == null) {
            initiationRemark.append("未查找到相关科目预算，科目id:").append(accountCode).append("，科目名称：").append(accountRelationshipList.get(0).getName());
            return null;
        }

        // 校验可用余额：预算台账减去在途流程预算减去报销金额
        BigDecimal onTheWayAmount = budgetFlowList.stream()
                .filter(budget -> Objects.equals(budget.getYsId(), oaBmyysbDTO.getId()))
                .map(OaBudgetFlowDTO::getJehj)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal availableAmount = oaBmyysbDTO.getByyskyye();
        log.info("预算id：{}，可用余额：{}，报销金额：{}，在途金额：{}", oaBmyysbDTO.getId(), availableAmount, totalAmount, onTheWayAmount);
        if (availableAmount == null || availableAmount.compareTo(totalAmount.add(onTheWayAmount)) < 0) {
            initiationRemark.append("部门预算不足，科目名称：").append(accountRelationshipList.get(0).getName()).append("，预算id").append(oaBmyysbDTO.getId()).append("，可用余额：").append(availableAmount).append("，本次合计报销金额：").append(totalAmount).append("，在途金额：").append(onTheWayAmount);
            return null;
        }

        // 校验通过
        return oaBmyysbDTO;
    }

    /**
     * 校验项目预算
     */
    private OaXmysbDTO validateProjectBudget(DdCommonOrder order, BigDecimal totalAmount,
                                             String kmmc,
                                             Map<Long, List<OaXmysbDTO>> xmysbMap,
                                             List<OaBudgetFlowDTO> budgetFlowList,
                                             Map<Long, CostConfigAccount> subjectConfigMap,
                                             StringBuilder initiationRemark) {
        if (order == null) {
            initiationRemark.append("订单为空");
            return null;
        }
        List<OaXmysbDTO> projectBudgets = xmysbMap.get(order.getProjectId());
        if (CollUtil.isEmpty(projectBudgets)) {
            initiationRemark.append("未查找到相关项目预算，项目id:").append(order.getProjectId());
            return null;
        }

        // 查找对应科目id的预算
        log.info("项目id：{}，科目编码：{}，项目预算：{}", order.getProjectId(), kmmc, JSONObject.toJSONString(projectBudgets));
        OaXmysbDTO oaXmysbDTO = projectBudgets.stream()
                .filter(budget -> kmmc.equals(String.valueOf(budget.getKmmc())))
                .findFirst()
                .orElse(null);
        String accountName = subjectConfigMap.get(Long.valueOf(kmmc)) == null ? null : subjectConfigMap.get(Long.valueOf(kmmc)).getAccountName();
        if (oaXmysbDTO == null) {
            initiationRemark.append("未查找到相关科目预算，科目id：").append(kmmc).append("，科目名称：").append(accountName);
            return null;
        }

        // 校验可用余额：预算台账减去在途流程预算减去报销金额
        BigDecimal onTheWayAmount = budgetFlowList.stream()
                .filter(budget -> Objects.equals(budget.getYsId(), oaXmysbDTO.getId()))
                .map(OaBudgetFlowDTO::getJehj)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal availableAmount = oaXmysbDTO.getKyysje();
        log.info("预算id：{}，可用余额：{}，报销金额：{}，在途金额：{}", oaXmysbDTO.getId(), availableAmount, totalAmount, onTheWayAmount);
        if (availableAmount == null || availableAmount.compareTo(totalAmount.add(onTheWayAmount)) < 0) {
            initiationRemark.append("项目预算不足，预算id：").append(oaXmysbDTO.getId()).append("，科目名称：").append(accountName).append("，可用余额：").append(availableAmount).append("，本次合计报销金额：").append(totalAmount).append("，在途金额：").append(onTheWayAmount);
            return null;
        }
        // 校验通过
        return oaXmysbDTO;
    }


    /**
     * 处理机票订单数据
     */
    private void processFlightTicketOrders(List<DdFlightTicketOrderImportDTO> orders,
                                           List<String> errorMessages,
                                           AtomicInteger successCount) {
        if (orders.isEmpty()) {
            return;
        }

        List<DdFlightTicketOrder> validEntities = new ArrayList<>();
        List<DdFlightTicketOrder> updateEntities = new ArrayList<>();

        List<String> invalidOrders = new ArrayList<>();
        Set<String> orderNoSet = new HashSet<>();
        Set<String> duplicateOrderNos = new HashSet<>();
        Set<String> projectNameSet = new HashSet<>();
        Set<String> workCodeSet = new HashSet<>();
        orders.forEach(order -> {
            if (!orderNoSet.add(order.getOrderNo())) {
                duplicateOrderNos.add(order.getOrderNo());
            }
            String costCenterProject = order.getCostCenterProject();
            if (costCenterProject != null && !costCenterProject.startsWith("部门费用-")) {
                projectNameSet.add(costCenterProject);
            }
            if (StrUtil.isNotBlank(order.getBookingEmployeeNo())) {
                workCodeSet.add(order.getBookingEmployeeNo());
            }
            if (StrUtil.isNotBlank(order.getPassengerEmployeeNo())) {
                workCodeSet.add(order.getPassengerEmployeeNo());
            }
        });


        // 查询已存在的酒店订单
        Map<String, DdFlightTicketOrder> flightTicketOrderMap = CollUtil.isEmpty(orderNoSet)
                ? Collections.emptyMap()
                : ddOrderMapper.getFlightTicketOrders(orderNoSet)
                .stream()
                .collect(Collectors.toMap(DdFlightTicketOrder::getOrderNo, v -> v));
        // 查询项目信息
        Map<String, ProjectInfo> projectInfoMap = CollUtil.isEmpty(projectNameSet)
                ? Collections.emptyMap()
                : projectInfoService.lambdaQuery()
                .in(ProjectInfo::getItemName, projectNameSet)
                .list().stream()
                .collect(Collectors.toMap(ProjectInfo::getItemName, v -> v, (a, b) -> a));
        // 查询部门信息
        List<SysDeptOutVO> deptList = bcpDataHandler.getAllSysDeptList();
        Map<String, Long> deptIdMapByName = CollStreamUtil.toMap(deptList, SysDeptOutVO::getName, SysDeptOutVO::getDeptId);
        // 查询用户信息
        Map<String, Roster> rosterMap = CollUtil.isEmpty(workCodeSet)
                ? Collections.emptyMap()
                : rosterService.lambdaQuery()
                .in(Roster::getWorkCode, workCodeSet)
                .list().stream()
                .collect(Collectors.toMap(Roster::getWorkCode, v -> v, (a, b) -> a));

        List<DictKvVo> dictKvVoList = BaseBuildEntityUtil.apiResult(remoteBcpDictService.getRemoteDictKvList("所属公司"));
        Map<String, Long> companyMap = CollStreamUtil.toMap(dictKvVoList, DictKvVo::getName, e -> Long.valueOf(e.getValue()));

        for (DdFlightTicketOrderImportDTO order : orders) {
            try {
                if (duplicateOrderNos.contains(order.getOrderNo())) {
                    invalidOrders.add("机票订单[" + order.getOrderNo() + "]：订单号重复");
                    continue;
                }

                // 数据校验
                String errorMsg = validateFlightTicketOrder(order, projectInfoMap, deptIdMapByName, rosterMap, companyMap);

                if (StringUtils.hasText(errorMsg)) {
                    invalidOrders.add("机票订单[" + order.getOrderNo() + "]：" + errorMsg);
                    continue;
                }
                // 转换为实体
                DdFlightTicketOrder entity = convertToFlightTicketOrder(order);

                if (flightTicketOrderMap.containsKey(order.getOrderNo())) {
                    DdFlightTicketOrder flightTicketOrder = flightTicketOrderMap.get(order.getOrderNo());
                    if (EnumUtils.valueEquals(flightTicketOrder.getInitiationStatus(), BusinessOrderReimbursedStatusEnum.INITIATED)) {
                        invalidOrders.add("机票订单[" + order.getOrderNo() + "]：已发起报销");
                        continue;
                    }
                    updateEntities.add(BaseBuildEntityUtil.buildUpdate(entity));
                } else {
                    validEntities.add(BaseBuildEntityUtil.buildInsert(entity));
                }

            } catch (Exception e) {
                log.error("处理机票订单数据失败，订单号：{}", order.getOrderNo(), e);
                invalidOrders.add("机票订单[" + order.getOrderNo() + "]：处理失败 - " + e.getMessage());
            }
        }
        if (CollUtil.isNotEmpty(invalidOrders)) {
            errorMessages.addAll(invalidOrders);
            return;
        }


        // 批量插入有效数据
        try {
            if (CollUtil.isNotEmpty(validEntities)) {
                ddOrderMapper.batchInsertFlightTicketOrders(validEntities);
                successCount.addAndGet(validEntities.size());
            }
            if (CollUtil.isNotEmpty(updateEntities)) {
                // 使用全量覆盖更新方法
                ddOrderMapper.batchFullUpdateFlightTicketOrder(updateEntities);
                successCount.addAndGet(updateEntities.size());
            }
        } catch (Exception e) {
            log.error("批量新增/更新机票订单失败", e);
            invalidOrders.add("机票订单批量处理失败，请联系管理员");
        }
        errorMessages.addAll(invalidOrders);

    }

    /**
     * 处理酒店订单数据
     */
    private void processHotelOrders(List<DdHotelOrderImportDTO> orders,
                                    List<String> errorMessages,
                                    AtomicInteger successCount) {
        if (orders.isEmpty()) {
            return;
        }

        List<DdHotelOrder> validEntities = new ArrayList<>();
        List<DdHotelOrder> updateEntities = new ArrayList<>();

        List<String> invalidOrders = new ArrayList<>();
        Set<String> duplicateOrderNos = new HashSet<>();
        Set<String> orderNoSet = new HashSet<>();
        Set<String> projectNameSet = new HashSet<>();
        Set<String> workCodeSet = new HashSet<>();
        orders.forEach(order -> {
            if (!orderNoSet.add(order.getOrderNo())) {
                duplicateOrderNos.add(order.getOrderNo());
            }
            String costCenterProject = order.getCostCenterProject();
            if (costCenterProject != null && !costCenterProject.startsWith("部门费用-")) {
                projectNameSet.add(costCenterProject);
            }
            if (StrUtil.isNotBlank(order.getBookingEmployeeNo())) {
                workCodeSet.add(order.getBookingEmployeeNo());
            }
            if (StrUtil.isNotBlank(order.getCheckinEmployeeNo())) {
                workCodeSet.add(order.getCheckinEmployeeNo());
            }
        });


        // 查询已存在的酒店订单
        Map<String, DdHotelOrder> hotelOrderMap = CollUtil.isEmpty(orderNoSet)
                ? Collections.emptyMap()
                : ddOrderMapper.getHotelOrders(orderNoSet)
                .stream()
                .collect(Collectors.toMap(DdHotelOrder::getOrderNo, v -> v));
        // 查询项目信息
        Map<String, ProjectInfo> projectInfoMap = CollUtil.isEmpty(projectNameSet)
                ? Collections.emptyMap()
                : projectInfoService.lambdaQuery()
                .in(ProjectInfo::getItemName, projectNameSet)
                .list().stream()
                .collect(Collectors.toMap(ProjectInfo::getItemName, v -> v, (a, b) -> a));
        // 查询部门信息
        List<SysDeptOutVO> deptList = bcpDataHandler.getAllSysDeptList();
        Map<String, Long> deptIdMapByName = CollStreamUtil.toMap(deptList, SysDeptOutVO::getName, SysDeptOutVO::getDeptId);
        // 查询用户信息
        Map<String, Roster> rosterMap = CollUtil.isEmpty(workCodeSet)
                ? Collections.emptyMap()
                : rosterService.lambdaQuery()
                .in(Roster::getWorkCode, workCodeSet)
                .list().stream()
                .collect(Collectors.toMap(Roster::getWorkCode, v -> v, (a, b) -> a));
        List<DictKvVo> dictKvVoList = BaseBuildEntityUtil.apiResult(remoteBcpDictService.getRemoteDictKvList("所属公司"));
        Map<String, Long> companyMap = CollStreamUtil.toMap(dictKvVoList, DictKvVo::getName, e -> Long.valueOf(e.getValue()));

        for (DdHotelOrderImportDTO order : orders) {
            try {
                if (duplicateOrderNos.contains(order.getOrderNo())) {
                    invalidOrders.add("酒店订单[" + order.getOrderNo() + "]：订单号重复");
                    continue;
                }
                // 数据校验
                String errorMsg = validateHotelOrder(order, projectInfoMap, deptIdMapByName, rosterMap, companyMap);
                if (StringUtils.hasText(errorMsg)) {
                    invalidOrders.add("酒店订单[" + order.getOrderNo() + "]：" + errorMsg);
                    continue;
                }

                // 转换为实体
                DdHotelOrder entity = convertToHotelOrder(order);
                if (hotelOrderMap.containsKey(order.getOrderNo())) {
                    DdHotelOrder hotelOrder = hotelOrderMap.get(order.getOrderNo());
                    if (EnumUtils.valueEquals(hotelOrder.getInitiationStatus(), BusinessOrderReimbursedStatusEnum.INITIATED)) {
                        invalidOrders.add("酒店订单[" + order.getOrderNo() + "]：已发起报销");
                        continue;
                    }
                    updateEntities.add(BaseBuildEntityUtil.buildUpdate(entity));
                } else {
                    validEntities.add(BaseBuildEntityUtil.buildInsert(entity));
                }

            } catch (Exception e) {
                log.error("处理酒店订单数据失败，订单号：{}", order.getOrderNo(), e);
                invalidOrders.add("酒店订单[" + order.getOrderNo() + "]：处理失败 - " + e.getMessage());
            }
        }
        if (CollUtil.isNotEmpty(invalidOrders)) {
            errorMessages.addAll(invalidOrders);
            return;
        }
        // 批量插入有效数据
        try {
            if (CollUtil.isNotEmpty(validEntities)) {
                ddOrderMapper.batchInsertHotelOrders(validEntities);
                successCount.addAndGet(validEntities.size());
            }
            if (CollUtil.isNotEmpty(updateEntities)) {
                // 使用全量覆盖更新方法
                ddOrderMapper.batchFullUpdateHotelOrder(updateEntities);
                successCount.addAndGet(updateEntities.size());
            }
        } catch (Exception e) {
            log.error("批量新增/更新酒店订单失败", e);
            invalidOrders.add("酒店订单批量处理失败，请联系管理员");
        }
        errorMessages.addAll(invalidOrders);
    }

    /**
     * 处理用车订单数据
     */
    private void processVehicleOrders(List<DdVehicleOrderImportDTO> orders,
                                      List<String> errorMessages,
                                      AtomicInteger successCount) {
        if (orders.isEmpty()) {
            return;
        }

        List<DdVehicleOrder> validEntities = new ArrayList<>();
        List<DdVehicleOrder> updateEntities = new ArrayList<>();

        List<String> invalidOrders = new ArrayList<>();
        Set<String> orderNoSet = new HashSet<>();
        Set<String> duplicateOrderNos = new HashSet<>();
        Set<String> projectNameSet = new HashSet<>();
        Set<String> workCodeSet = new HashSet<>();
        orders.forEach(order -> {
            if (!orderNoSet.add(order.getOrderNo())) {
                duplicateOrderNos.add(order.getOrderNo());
            }
            String costCenterProject = order.getCostCenterProject();
            if (costCenterProject != null && !costCenterProject.startsWith("部门费用-")) {
                projectNameSet.add(costCenterProject);
            }
            if (StrUtil.isNotBlank(order.getBookingEmployeeNo())) {
                workCodeSet.add(order.getBookingEmployeeNo());
            }
            if (StrUtil.isNotBlank(order.getPassengerEmployeeNo())) {
                workCodeSet.add(order.getPassengerEmployeeNo());
            }
        });

        // 查询已存在的酒店订单
        Map<String, DdVehicleOrder> vehicleOrderMap = CollUtil.isEmpty(orderNoSet)
                ? Collections.emptyMap()
                : ddOrderMapper.getVehicleOrders(orderNoSet)
                .stream()
                .collect(Collectors.toMap(DdVehicleOrder::getOrderNo, v -> v));
        // 查询项目信息
        Map<String, ProjectInfo> projectInfoMap = CollUtil.isEmpty(projectNameSet)
                ? Collections.emptyMap()
                : projectInfoService.lambdaQuery()
                .in(ProjectInfo::getItemName, projectNameSet)
                .list().stream()
                .collect(Collectors.toMap(ProjectInfo::getItemName, v -> v, (a, b) -> a));
        // 查询部门信息
        List<SysDeptOutVO> deptList = bcpDataHandler.getAllSysDeptList();
        Map<String, Long> deptIdMapByName = CollStreamUtil.toMap(deptList, SysDeptOutVO::getName, SysDeptOutVO::getDeptId);
        // 查询用户信息
        Map<String, Roster> rosterMap = CollUtil.isEmpty(workCodeSet)
                ? Collections.emptyMap()
                : rosterService.lambdaQuery()
                .in(Roster::getWorkCode, workCodeSet)
                .list().stream()
                .collect(Collectors.toMap(Roster::getWorkCode, v -> v, (a, b) -> a));

        List<DictKvVo> dictKvVoList = BaseBuildEntityUtil.apiResult(remoteBcpDictService.getRemoteDictKvList("所属公司"));
        Map<String, Long> companyMap = CollStreamUtil.toMap(dictKvVoList, DictKvVo::getName, e -> Long.valueOf(e.getValue()));

        for (DdVehicleOrderImportDTO order : orders) {
            try {
                if (duplicateOrderNos.contains(order.getOrderNo())) {
                    invalidOrders.add("用车订单[" + order.getOrderNo() + "]：订单号重复");
                    continue;
                }
                // 数据校验
                String errorMsg = validateVehicleOrder(order, projectInfoMap, deptIdMapByName, rosterMap, companyMap);
                if (StringUtils.hasText(errorMsg)) {
                    invalidOrders.add("用车订单[" + order.getOrderNo() + "]：" + errorMsg);
                    continue;
                }

                // 转换为实体
                DdVehicleOrder entity = convertToVehicleOrder(order);
                if (vehicleOrderMap.containsKey(order.getOrderNo())) {
                    DdVehicleOrder vehicleOrder = vehicleOrderMap.get(order.getOrderNo());
                    if (EnumUtils.valueEquals(vehicleOrder.getInitiationStatus(), BusinessOrderReimbursedStatusEnum.INITIATED)) {
                        invalidOrders.add("用车订单[" + order.getOrderNo() + "]：已发起报销");
                        continue;
                    }
                    updateEntities.add(BaseBuildEntityUtil.buildUpdate(entity));
                } else {
                    validEntities.add(BaseBuildEntityUtil.buildInsert(entity));
                }

            } catch (Exception e) {
                log.error("处理用车订单数据失败，订单号：{}", order.getOrderNo(), e);
                invalidOrders.add("用车订单[" + order.getOrderNo() + "]：处理失败 - " + e.getMessage());
            }
        }
        if (CollUtil.isNotEmpty(invalidOrders)) {
            errorMessages.addAll(invalidOrders);
            return;
        }

        // 批量插入有效数据
        try {
            if (CollUtil.isNotEmpty(validEntities)) {
                ddOrderMapper.batchInsertVehicleOrders(validEntities);
                successCount.addAndGet(validEntities.size());
            }
            if (CollUtil.isNotEmpty(updateEntities)) {
                // 使用全量覆盖更新方法
                ddOrderMapper.batchFullUpdateVehicleOrder(updateEntities);
                successCount.addAndGet(updateEntities.size());
            }
        } catch (Exception e) {
            log.error("批量新增/更新用车订单失败", e);
            invalidOrders.add("用车订单批量处理失败，请联系管理员");
        }
        errorMessages.addAll(invalidOrders);
    }

    /**
     * 校验机票订单数据
     */
    private String validateFlightTicketOrder(DdFlightTicketOrderImportDTO order,
                                             Map<String, ProjectInfo> projectInfoMap,
                                             Map<String, Long> deptIdMapByName,
                                             Map<String, Roster> rosterMap,
                                             Map<String, Long> companyMap) {
        if (!StringUtils.hasText(order.getOrderNo())) {
            return "订单号不能为空";
        }
        if (StringUtils.hasText(order.getPassengerEmployeeNo())) {
            Roster roster = rosterMap.get(order.getPassengerEmployeeNo());
            if (roster == null) {
                return "乘机人工号不存在";
            }
            order.setPassengerName(roster.getAliasName());
            order.setPassengerId(roster.getDeptId());
            order.setPassengerDeptId(roster.getId());
        }
        if (!StringUtils.hasText(order.getPassengerName())) {
            return "乘机人姓名不能为空";
        }
        if (order.getCompanyActualPayment() == null) {
            return "企业实付金额不能为空";
        }
        if (order.getBookingDate() == null) {
            return "预订日期不能为空";
        }
        if (!StringUtils.hasText(order.getBookingEmployeeNo())) {
            return "预订人工号不能为空";
        }
        if (!rosterMap.containsKey(order.getBookingEmployeeNo())) {
            return "预订人工号不存在";
        } else {
            Roster roster = rosterMap.get(order.getBookingEmployeeNo());
            order.setBookingEmployeeName(roster.getAliasName());
            order.setBookingDeptId(roster.getDeptId());
            order.setBookingUserId(roster.getId());
        }

        String costCenterProject = order.getCostCenterProject();
        if (!StringUtils.hasText(costCenterProject)) {
            return "成本中心/所属项目不能为空";
        } else {
            String parsed = parseCostCenterProject(costCenterProject, order, projectInfoMap, deptIdMapByName);
            if (parsed != null) {
                return parsed;
            }
        }
        if (!StringUtils.hasText(order.getCompanyName())) {
            return "所属公司不能为空";
        }
        String[] split = order.getCompanyName().split(">");
        order.setCompanyName(split.length > 1 ? split[1] : split[0]);
        if (!companyMap.containsKey(order.getCompanyName())) {
            return "所属公司不存在";
        }
        Long companyId = companyMap.get(order.getCompanyName());
        order.setCompanyId(companyId);

        return null;
    }

    /**
     * 校验酒店订单数据
     */
    private String validateHotelOrder(DdHotelOrderImportDTO order,
                                      Map<String, ProjectInfo> projectInfoMap,
                                      Map<String, Long> deptIdMapByName,
                                      Map<String, Roster> rosterMap,
                                      Map<String, Long> companyMap) {
        if (!StringUtils.hasText(order.getOrderNo())) {
            return "订单号不能为空";
        }
        if (StringUtils.hasText(order.getCheckinEmployeeNo())) {
            Roster roster = rosterMap.get(order.getCheckinEmployeeNo());
            if (roster == null) {
                return "入住人工号不存在";
            }
            order.setCheckinPersonName(roster.getAliasName());
            order.setCheckinDeptId(roster.getDeptId());
            order.setCheckinUserId(roster.getId());
        }

        if (!StringUtils.hasText(order.getCheckinPersonName())) {
            return "入住人姓名不能为空";
        }
        if (order.getCompanyActualPayment() == null) {
            return "企业实付金额不能为空";
        }
        if (order.getBookingDate() == null) {
            return "预订日期不能为空";
        }
        if (!StringUtils.hasText(order.getBookingEmployeeNo())) {
            return "预订人工号不能为空";
        }
        if (!rosterMap.containsKey(order.getBookingEmployeeNo())) {
            return "预订人工号不存在";
        } else {
            Roster roster = rosterMap.get(order.getBookingEmployeeNo());
            order.setBookingEmployeeName(roster.getAliasName());
            order.setBookingDeptId(roster.getDeptId());
            order.setBookingUserId(roster.getId());
        }

        String costCenterProject = order.getCostCenterProject();
        if (!StringUtils.hasText(costCenterProject)) {
            return "成本中心/所属项目不能为空";
        } else {
            String parsed = parseCostCenterProject(costCenterProject, order, projectInfoMap, deptIdMapByName);
            if (parsed != null) {
                return parsed;
            }
        }
        if (!StringUtils.hasText(order.getCompanyName())) {
            return "所属公司不能为空";
        }
        String[] split = order.getCompanyName().split(">");
        order.setCompanyName(split.length > 1 ? split[1] : split[0]);
        if (!companyMap.containsKey(order.getCompanyName())) {
            return "所属公司不存在";
        }
        Long companyId = companyMap.get(order.getCompanyName());
        order.setCompanyId(companyId);
        return null;
    }

    /**
     * 校验用车订单数据
     */
    private String validateVehicleOrder(DdVehicleOrderImportDTO order,
                                        Map<String, ProjectInfo> projectInfoMap,
                                        Map<String, Long> deptIdMapByName,
                                        Map<String, Roster> rosterMap,
                                        Map<String, Long> companyMap) {
        if (!StringUtils.hasText(order.getOrderNo())) {
            return "订单号不能为空";
        }
        if (StringUtils.hasText(order.getPassengerEmployeeNo())) {
            Roster roster = rosterMap.get(order.getPassengerEmployeeNo());
            if (roster == null) {
                return "乘车人工号不存在";
            }
            order.setPassengerName(roster.getAliasName());
            order.setPassengerId(roster.getDeptId());
            order.setPassengerDeptId(roster.getId());
        }
        if (!StringUtils.hasText(order.getPassengerName())) {
            return "乘车人姓名不能为空";
        }
        if (order.getCompanyActualPayment() == null) {
            return "企业实付金额不能为空";
        }
        if (order.getBookingDate() == null) {
            return "预订日期不能为空";
        }
        if (!StringUtils.hasText(order.getBookingEmployeeNo())) {
            return "预订人工号不能为空";
        }
        if (!rosterMap.containsKey(order.getBookingEmployeeNo())) {
            return "预订人工号不存在";
        } else {
            Roster roster = rosterMap.get(order.getBookingEmployeeNo());
            order.setBookingEmployeeName(roster.getAliasName());
            order.setBookingDeptId(roster.getDeptId());
            order.setBookingUserId(roster.getId());
        }
        String costCenterProject = order.getCostCenterProject();
        if (!StringUtils.hasText(costCenterProject)) {
            return "成本中心/所属项目不能为空";
        } else {
            String parsed = parseCostCenterProject(costCenterProject, order, projectInfoMap, deptIdMapByName);
            if (parsed != null) {
                return parsed;
            }
        }
        if (!StringUtils.hasText(order.getCompanyName())) {
            return "所属公司不能为空";
        }
        String[] split = order.getCompanyName().split(">");
        order.setCompanyName(split.length > 1 ? split[1] : split[0]);
        if (!companyMap.containsKey(order.getCompanyName())) {
            return "所属公司不存在";
        }
        Long companyId = companyMap.get(order.getCompanyName());
        order.setCompanyId(companyId);
        return null;
    }

    /**
     * 转换为机票订单实体
     */
    private static DdFlightTicketOrder convertToFlightTicketOrder(DdFlightTicketOrderImportDTO dto) {
        DdFlightTicketOrder entity = new DdFlightTicketOrder();
        entity.setOrderNo(dto.getOrderNo());
        entity.setPassengerEmployeeNo(dto.getPassengerEmployeeNo());
        entity.setPassengerId(dto.getPassengerId());
        entity.setPassengerDeptId(dto.getPassengerDeptId());

        entity.setPassengerName(dto.getPassengerName());
        entity.setTicketNo(dto.getTicketNo());
        entity.setTicketStatus(dto.getTicketStatus());
        entity.setDepartureLocation(dto.getDepartureLocation());
        entity.setArrivalLocation(dto.getArrivalLocation());
        entity.setFlightNo(dto.getFlightNo());
        entity.setDepartureTime(dto.getDepartureTime());
        entity.setLandingTime(dto.getLandingTime());
        entity.setCompanyActualPayment(dto.getCompanyActualPayment());
        entity.setServiceFee(dto.getServiceFee());
        entity.setBookingDate(dto.getBookingDate());
        entity.setBookingUserId(dto.getBookingUserId());
        entity.setBookingEmployeeNo(dto.getBookingEmployeeNo());
        entity.setBookingDeptId(dto.getBookingDeptId());

        entity.setBookingEmployeeName(dto.getBookingEmployeeName());
        entity.setBusinessTripApplicationNo(dto.getBusinessTripApplicationNo());
        entity.setBusinessTripReason(dto.getBusinessTripReason());

        entity.setCostCenterId(dto.getCostCenterId());
        entity.setCostCenterName(dto.getCostCenterName());
        entity.setProjectId(dto.getProjectId());
        entity.setProjectCode(dto.getProjectCode());
        entity.setProjectName(dto.getProjectName());

        entity.setCompanyId(dto.getCompanyId());
        entity.setCompanyName(dto.getCompanyName());
        entity.setAccountingPeriod(dto.getBookingDate().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        entity.setInitiationStatus(BusinessOrderReimbursedStatusEnum.WAIT_INITIATION.getValue());

        return entity;
    }

    /**
     * 转换为酒店订单实体
     */
    private static DdHotelOrder convertToHotelOrder(DdHotelOrderImportDTO dto) {
        DdHotelOrder entity = new DdHotelOrder();
        entity.setOrderNo(dto.getOrderNo());
        entity.setCheckinEmployeeNo(dto.getCheckinEmployeeNo());
        entity.setCheckinUserId(dto.getCheckinUserId());
        entity.setCheckinDeptId(dto.getCheckinDeptId());
        entity.setCheckinPersonName(dto.getCheckinPersonName());
        entity.setCityName(dto.getCityName());
        entity.setHotelName(dto.getHotelName());
        entity.setRoomType(dto.getRoomType());
        entity.setCheckinTime(dto.getCheckinTime());
        entity.setCheckoutTime(dto.getCheckoutTime());
        entity.setCompanyActualPayment(dto.getCompanyActualPayment());
        entity.setServiceFee(dto.getServiceFee());
        entity.setNumberOfDays(dto.getNumberOfDays());
        entity.setNumberOfRooms(dto.getNumberOfRooms());
        entity.setRoomNights(dto.getRoomNights());
        entity.setUnitPrice(dto.getUnitPrice());
        entity.setRoomStandardDifference(dto.getRoomStandardDifference());
        entity.setOrderStatus(dto.getOrderStatus());
        entity.setBookingDate(dto.getBookingDate());
        entity.setBookingEmployeeNo(dto.getBookingEmployeeNo());
        entity.setBookingUserId(dto.getBookingUserId());
        entity.setBookingDeptId(dto.getBookingDeptId());

        entity.setBookingEmployeeName(dto.getBookingEmployeeName());
        entity.setBusinessTripApplicationNo(dto.getBusinessTripApplicationNo());
        entity.setBusinessTripReason(dto.getBusinessTripReason());

        entity.setCostCenterId(dto.getCostCenterId());
        entity.setCostCenterName(dto.getCostCenterName());
        entity.setProjectId(dto.getProjectId());
        entity.setProjectCode(dto.getProjectCode());
        entity.setProjectName(dto.getProjectName());

        entity.setCompanyId(dto.getCompanyId());
        entity.setCompanyName(dto.getCompanyName());
        entity.setAccountingPeriod(dto.getBookingDate().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        entity.setInitiationStatus(BusinessOrderReimbursedStatusEnum.WAIT_INITIATION.getValue());

        return entity;
    }

    /**
     * 转换为用车订单实体
     */
    private static DdVehicleOrder convertToVehicleOrder(DdVehicleOrderImportDTO dto) {
        DdVehicleOrder entity = new DdVehicleOrder();
        entity.setOrderNo(dto.getOrderNo());
        entity.setPassengerEmployeeNo(dto.getPassengerEmployeeNo());
        entity.setPassengerId(dto.getPassengerId());
        entity.setPassengerDeptId(dto.getPassengerDeptId());
        entity.setPassengerName(dto.getPassengerName());
        entity.setTravelTime(dto.getTravelTime());
        entity.setArrivalTime(dto.getArrivalTime());
        entity.setVehicleType(dto.getVehicleType());
        entity.setDepartureCity(dto.getDepartureCity());
        entity.setDepartureAddress(dto.getDepartureAddress());
        entity.setArrivalCity(dto.getArrivalCity());
        entity.setArrivalAddress(dto.getArrivalAddress());
        entity.setTravelDistance(dto.getTravelDistance());
        entity.setCompanyActualPayment(dto.getCompanyActualPayment());
        entity.setServiceFee(dto.getServiceFee());
        entity.setPaymentType(dto.getPaymentType());
        entity.setBookingDate(dto.getBookingDate());
        entity.setBookingUserId(dto.getBookingUserId());
        entity.setBookingDeptId(dto.getBookingDeptId());
        entity.setBookingEmployeeNo(dto.getBookingEmployeeNo());
        entity.setBookingEmployeeName(dto.getBookingEmployeeName());
        entity.setBusinessTripApplicationNo(dto.getBusinessTripApplicationNo());
        entity.setBusinessTripReason(dto.getBusinessTripReason());

        entity.setCostCenterId(dto.getCostCenterId());
        entity.setCostCenterName(dto.getCostCenterName());
        entity.setProjectId(dto.getProjectId());
        entity.setProjectCode(dto.getProjectCode());
        entity.setProjectName(dto.getProjectName());

        entity.setCompanyId(dto.getCompanyId());
        entity.setCompanyName(dto.getCompanyName());
        entity.setAccountingPeriod(dto.getBookingDate().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        entity.setInitiationStatus(BusinessOrderReimbursedStatusEnum.WAIT_INITIATION.getValue());

        return entity;
    }

    /**
     * 解析成本中心/所属项目
     */
    private String parseCostCenterProject(String costCenterProject,
                                          Object entity,
                                          Map<String, ProjectInfo> projectInfoMap,
                                          Map<String, Long> deptIdMapByName) {
        if (!StringUtils.hasText(costCenterProject)) {
            return null;
        }
        if (costCenterProject.startsWith("部门费用-")) {
            // 部门费用
            String deptName = costCenterProject.substring("部门费用-".length());
            if (!deptIdMapByName.containsKey(deptName)) {
                return "成本中心/所属项目-该部门不存在";
            }
            Long deptId = deptIdMapByName.get(deptName);
            if (entity instanceof DdFlightTicketOrderImportDTO) {
                DdFlightTicketOrderImportDTO order = (DdFlightTicketOrderImportDTO) entity;
                order.setCostCenterName(deptName);
                order.setCostCenterId(deptId);
            } else if (entity instanceof DdHotelOrderImportDTO) {
                DdHotelOrderImportDTO order = (DdHotelOrderImportDTO) entity;
                order.setCostCenterName(deptName);
                order.setCostCenterId(deptId);
            } else if (entity instanceof DdVehicleOrderImportDTO) {
                DdVehicleOrderImportDTO order = (DdVehicleOrderImportDTO) entity;
                order.setCostCenterName(deptName);
                order.setCostCenterId(deptId);
            }
        } else {
            if (!projectInfoMap.containsKey(costCenterProject)) {
                return "成本中心/所属项目-该项目不存在";
            }
            ProjectInfo projectInfo = projectInfoMap.get(costCenterProject);
            // 项目费用
            if (entity instanceof DdFlightTicketOrderImportDTO) {
                DdFlightTicketOrderImportDTO order = (DdFlightTicketOrderImportDTO) entity;
                order.setProjectName(costCenterProject);
                order.setProjectId(projectInfo.getId());
                order.setProjectCode(projectInfo.getItemNo());
            } else if (entity instanceof DdHotelOrderImportDTO) {
                DdHotelOrderImportDTO order = (DdHotelOrderImportDTO) entity;
                order.setProjectName(costCenterProject);
                order.setProjectId(projectInfo.getId());
                order.setProjectCode(projectInfo.getItemNo());
            } else if (entity instanceof DdVehicleOrderImportDTO) {
                DdVehicleOrderImportDTO order = (DdVehicleOrderImportDTO) entity;
                order.setProjectName(costCenterProject);
                order.setProjectId(projectInfo.getId());
                order.setProjectCode(projectInfo.getItemNo());
            }
        }
        return null;
    }

    /**
     * 调用OA接口创建报销单
     */
    private String callOACreateReimbursement(OaAccountVO oaAccountVO, Map<Integer, Integer> workflowIdMap, String token, Integer oaFormType, List<DdCommonOrder> orders, Long companyId, Long deptProjectId, Map<Long, ProjectAccountVO> projectAccountMap) throws JsonProcessingException {
        // 根据OA表单类型获取对应的枚举
        OAFormTypeEnum formTypeEnum = OAFormTypeEnum.getBusinessTypeEnum(oaFormType);
        if (formTypeEnum == null) {
            log.error("未找到对应的OA表单类型：{}", oaFormType);
            return "未找到对应的OA表单类型";
        }
        if (orders.isEmpty()) {
            return "订单数据为空";
        }

        // 构建OA请求DTO
        OaCreateRequestDTO oaCreateRequestDTO = new OaCreateRequestDTO();
        oaCreateRequestDTO.setWorkflowId(workflowIdMap.get(formTypeEnum.getValue()));
        oaCreateRequestDTO.setOaAccountVO(oaAccountVO);

        // 构建主表数据
        List<OaMainParamDTO> mainDataList = buildMainData(oaAccountVO, token, orders, companyId, deptProjectId, formTypeEnum, projectAccountMap);
        if (mainDataList.isEmpty()) {
            log.error("构建主表数据失败：{}", deptProjectId);
            return "构建主表数据失败";
        }
        oaCreateRequestDTO.setMainData(mainDataList);

        // 合并相同费用项的订单
        List<MergedOrderData> mergedOrders = mergeOrdersByExpenseItem(orders, formTypeEnum);

        // 构建明细表数据
        List<Map<String, Object>> detailDataList = buildDetailData(mergedOrders, formTypeEnum);
        if (detailDataList.isEmpty()) {
            log.error("构建明细表数据失败：{}", deptProjectId);
            return "构建明细表数据失败";
        }
        oaCreateRequestDTO.setDetailData(detailDataList);

        // 设置其他参数 isnextflow ：新建流程是否默认提交到第二节点，可选值为[0 ：不流转 1：流转 (默认)]
        Map<String, Object> otherParams = new HashMap<>();
        otherParams.put("isnextflow", "1");
        oaCreateRequestDTO.setOtherParams(otherParams);

        // 设置请求名称
        String requestName = buildRequestName(formTypeEnum, oaAccountVO);
        oaCreateRequestDTO.setRequestName(requestName);

        // 对userID进行加密
        String encryptUserId = RSAUtils.encrypt(String.valueOf(oaAccountVO.getOaId()), spk);

        // 序列化参数
        ObjectMapper objectMapper = new ObjectMapper();
        String mainParamStr = objectMapper.writeValueAsString(mainDataList);
        String otherParamsStr = JSONUtil.toJsonStr(otherParams);
        String detailParamStr = objectMapper.writeValueAsString(detailDataList);

        log.info("调用OA接口创建报销单，参数：workflowId={}, requestName={}", oaCreateRequestDTO.getWorkflowId(), requestName);

        // 调用OA接口
        JSONObject result = oaClient.doCreateRequest(token, appId, encryptUserId, url,
                oaCreateRequestDTO.getWorkflowId().toString(),
                requestName,
                mainParamStr,
                otherParamsStr,
                detailParamStr);

        if (!"SUCCESS".equals(result.getString("code"))) {
            log.error("调用OA接口失败，返回结果：{}", result);
            return "调用OA接口失败，返回结果：" + result;
        }

        // 获取请求ID
        JSONObject data = result.getJSONObject("data");
        Long requestId = data.getLong("requestid");

        // 更新订单请求ID和报销状态和报销单号
        orders.forEach(order -> {
            order.setRequestId(requestId);
            order.setExpenseReportNo(requestName);
            order.setInitiationStatus(BusinessOrderReimbursedStatusEnum.INITIATED.getValue());
            order.setInitiationRemark("成功创建OA报销单");
        });

        log.info("成功创建OA报销单，请求ID：{}", requestId);
        return null;
    }

    /**
     * 构建主表数据
     */
    private List<OaMainParamDTO> buildMainData(OaAccountVO oaAccountVO, String token, List<DdCommonOrder> orders, Long companyId, Long deptProjectId, OAFormTypeEnum formTypeEnum, Map<Long, ProjectAccountVO> projectAccountMap) {
        List<OaMainParamDTO> mainDataList = new ArrayList<>();
        //流程信息
        mainDataList.add(buildMainParam("ApplicantID", String.valueOf(oaAccountVO.getOaId())));
        mainDataList.add(buildMainParam("bh", String.valueOf(oaAccountVO.getWorkcode())));
        mainDataList.add(buildMainParam("ApplicantDeptID", oaAccountVO.getDepartmentid()));
        mainDataList.add(buildMainParam("ApplicantTel", oaAccountVO.getMobile()));
        mainDataList.add(buildMainParam("ApplicantJobID", oaAccountVO.getJobtitle()));
        mainDataList.add(buildMainParam("CreateDate", DateUtils.getDate()));
        mainDataList.add(buildMainParam("CreatorID", String.valueOf(oaAccountVO.getOaId())));
        mainDataList.add(buildMainParam("CreatorDeptID", oaAccountVO.getDepartmentid()));
        mainDataList.add(buildMainParam("CreatorJobID", oaAccountVO.getJobtitle()));

        //收款方信息
        mainDataList.add(buildMainParam("dfmc", orders.get(0).getOrderType().getDfmc())); //对方名称
        mainDataList.add(buildMainParam("khx", orders.get(0).getOrderType().getKhx()));  //开户行
        mainDataList.add(buildMainParam("yxzh", orders.get(0).getOrderType().getYxzh()));  //银行账号

        // 生成Excel文件并上传到OA流程附件
        StringBuilder fileIdBuilder = generateAndUploadExcelFiles(orders, oaAccountVO, token);
        //判断fileIdBuilder是否为空
        if (fileIdBuilder == null || fileIdBuilder.length() == 0) {
            return mainDataList;
        }

        mainDataList.add(buildMainParam("RelateAttachment", fileIdBuilder.toString()));  //相关附件

        //计算orders合计金额
        String totalAmount = orders.stream().filter(order -> order.getBmyysb() != null || order.getXmysb() != null)
                .map(DdCommonOrder::getCompanyActualPayment).reduce(BigDecimal.ZERO, BigDecimal::add).toString();

        // 根据表单类型添加特定字段
        if (formTypeEnum == OAFormTypeEnum.FYBXD) {
            // CW-03 费用报销单特定字段
            mainDataList.add(buildMainParam("zffs", "1"));  //支付方式 默认值：转账
            mainDataList.add(buildMainParam("ssgs", String.valueOf(companyId)));  //所属 公司
            mainDataList.add(buildMainParam("djzs", "1"));  //单据张数 默认值：1
            mainDataList.add(buildMainParam("zje", totalAmount));  //本次报销总金额
            mainDataList.add(buildMainParam("fkje", totalAmount)); //总付款金额
            mainDataList.add(buildMainParam("ysyjbm", String.valueOf(deptProjectId)));  //预算一级部门

        } else if (formTypeEnum == OAFormTypeEnum.SJFYBX) {
            // XM-28 商机项目费用报销特定字段
            ProjectAccountVO projectAccountVO = projectAccountMap.get(deptProjectId);
            if (projectAccountVO == null) {
                return mainDataList;
            }
            mainDataList.add(buildMainParam("pjname", String.valueOf(deptProjectId)));  //项目名称
            mainDataList.add(buildMainParam("ssgs", String.valueOf(companyId)));  //所属公司
            mainDataList.add(buildMainParam("xmzt", projectAccountVO.getXmzt()));  //项目状态
            mainDataList.add(buildMainParam("bxr", String.valueOf(oaAccountVO.getOaId())));  //报销人
            mainDataList.add(buildMainParam("jsleixing", projectAccountVO.getJslxbb()));  //技术类型
            mainDataList.add(buildMainParam("bxrq", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));  //报销日期
            mainDataList.add(buildMainParam("khjl", projectAccountVO.getXmxsry()));  //客户经理
            mainDataList.add(buildMainParam("xmbm", projectAccountVO.getProjectNumber()));  //项目编码
            mainDataList.add(buildMainParam("ywgsbm", projectAccountVO.getYwgsbm()));  //业务归属部门(一级部门)
            mainDataList.add(buildMainParam("ywgsbmejbm", projectAccountVO.getYwgsbmejbm()));  //业务归属部门(二级部门)
            mainDataList.add(buildMainParam("bxlx", "2")); //报销类型 默认值：已到票付款
            mainDataList.add(buildMainParam("khmc", projectAccountVO.getZzkh()));  //客户名称
            mainDataList.add(buildMainParam("jehj", totalAmount));  //本次报销金额
            mainDataList.add(buildMainParam("fkje", totalAmount));  //付款总金额

        } else if (formTypeEnum == OAFormTypeEnum.ZJXMFYBX) {
            // XM-15 在建项目费用报销特定字段
            ProjectAccountVO projectAccountVO = projectAccountMap.get(deptProjectId);
            if (projectAccountVO == null) {
                return mainDataList;
            }
            mainDataList.add(buildMainParam("xmmci", String.valueOf(deptProjectId)));  //项目名称
            mainDataList.add(buildMainParam("xmbm", projectAccountVO.getProjectNumber()));  //项目编码
            mainDataList.add(buildMainParam("xmzt", projectAccountVO.getXmzt()));  //项目状态
            mainDataList.add(buildMainParam("jslx", projectAccountVO.getJslxbb()));  //技术类型
            mainDataList.add(buildMainParam("ssgs", String.valueOf(companyId)));  //所属公司
            mainDataList.add(buildMainParam("bxr", String.valueOf(oaAccountVO.getOaId())));  //报销人
            mainDataList.add(buildMainParam("bxrq", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));  //报销日期
            mainDataList.add(buildMainParam("xsry", projectAccountVO.getXmxsry()));  //客户经理
            mainDataList.add(buildMainParam("xmjl", projectAccountVO.getXmjl()));  //项目经理
            mainDataList.add(buildMainParam("xmfzr", projectAccountVO.getXmfzr()));  //交付负责人
            mainDataList.add(buildMainParam("ywgsbm", projectAccountVO.getYwgsbm()));  //业务归属部门(一级部门)
            mainDataList.add(buildMainParam("ywgsbmejbm", projectAccountVO.getYwgsbmejbm()));  //业务归属部门(二级部门)
            mainDataList.add(buildMainParam("xmgsbmejbm", projectAccountVO.getXmgsbmejbm()));  //项目归属部门(二级部门)
            mainDataList.add(buildMainParam("jehj", totalAmount));  //本次报销金额
            mainDataList.add(buildMainParam("fkje", totalAmount));  //付款总金额
            mainDataList.add(buildMainParam("fylx", "6")); //费用类型 默认值：项目费用
            mainDataList.add(buildMainParam("bxlx", "0")); //报销类型 默认值：已到票付款
            mainDataList.add(buildMainParam("skflb", "0"));  //收款方类别 默认值：供应商

        }
        return mainDataList;
    }

    /**
     * 构建主表参数DTO
     */
    private OaMainParamDTO buildMainParam(String fieldName, Object fieldValue) {
        return OaMainParamDTO.builder().fieldName(fieldName).fieldValue(fieldValue == null ? StringPool.EMPTY : String.valueOf(fieldValue)).build();
    }


    /**
     * 根据费用项合并订单
     */
    private List<MergedOrderData> mergeOrdersByExpenseItem(List<DdCommonOrder> orders, OAFormTypeEnum formTypeEnum) {
        Map<String, MergedOrderData> mergedOrdersMap = new LinkedHashMap<>();

        for (DdCommonOrder order : orders) {
            String mergeKey = null;
            String expenseItem = null;
            String expenseSummary = "【" + order.getAccountingPeriod() + "】" + order.getOrderType().getExpenseSummary();

            if (formTypeEnum == OAFormTypeEnum.FYBXD) {
                OaBmyysbDTO bmyysb = order.getBmyysb();
                if (bmyysb == null) {
                    continue; // 跳过无效订单
                }
                // 根据科目代码和订单类型动态获取费用项
                expenseItem = expenseItemConfig.getExpenseItem("FYBXD", bmyysb.getKmdm(), order.getOrderType().getCode());
                // 合并键：部门预算编码 + 科目代码 + 费用项
                mergeKey = bmyysb.getId() + "_" + bmyysb.getKmdm() + "_" + expenseItem;

            } else if (formTypeEnum == OAFormTypeEnum.SJFYBX) {
                OaXmysbDTO xmysb = order.getXmysb();
                if (xmysb == null) {
                    continue; // 跳过无效订单
                }
                // 根据科目代码和订单类型动态获取费用项
                expenseItem = expenseItemConfig.getExpenseItem("SJFYBX", xmysb.getKmdm(), order.getOrderType().getCode());
                // 合并键：项目预算 + 科目代码 + 费用项
                mergeKey = xmysb.getId() + "_" + xmysb.getKmdm() + "_" + expenseItem;

            } else if (formTypeEnum == OAFormTypeEnum.ZJXMFYBX) {
                OaXmysbDTO xmysb = order.getXmysb();
                if (xmysb == null) {
                    continue; // 跳过无效订单
                }
                // 根据科目代码和订单类型动态获取费用项
                expenseItem = expenseItemConfig.getExpenseItem("ZJXMFYBX", xmysb.getKmdm(), order.getOrderType().getCode());
                // 合并键：项目预算编码 + 科目代码 + 费用项
                mergeKey = xmysb.getId() + "_" + xmysb.getKmdm() + "_" + expenseItem;
            }

            // 如果有有效的合并键，则进行合并处理
            if (mergeKey != null) {
                MergedOrderData mergedOrder = mergedOrdersMap.get(mergeKey);
                if (mergedOrder == null) {
                    // 创建新的合并订单
                    mergedOrder = new MergedOrderData(mergeKey, expenseItem, expenseSummary, order);
                    mergedOrdersMap.put(mergeKey, mergedOrder);
                }
                // 累加金额
                mergedOrder.addAmount(order.getCompanyActualPayment());
            }
        }

        return new ArrayList<>(mergedOrdersMap.values());
    }

    /**
     * 构建明细表数据
     */
    private List<Map<String, Object>> buildDetailData(List<MergedOrderData> mergedOrders, OAFormTypeEnum formTypeEnum) {
        List<Map<String, Object>> detailDataList = new ArrayList<>();
        Map<String, Object> detailDataMap = new HashMap<>();
        List<Map<String, Object>> tableRecordList = new ArrayList<>();

        for (MergedOrderData mergedOrder : mergedOrders) {
            DdCommonOrder order = mergedOrder.getRepresentativeOrder();
            List<OaMainParamDTO> tableFieldList = new ArrayList<>();

            if (formTypeEnum == OAFormTypeEnum.FYBXD) {
                //校验部门预算
                OaBmyysbDTO bmyysb = order.getBmyysb();
                log.info("部门预算:" + JSONObject.toJSONString(bmyysb));
                if (bmyysb == null || bmyysb.getId() == null) {
                    return detailDataList;
                }
                // CW-03费用报销单特定字段 (formtable_main_125_dt1)
                tableFieldList.add(buildMainParam("bmyysbm", bmyysb.getId())); // 部门月预算编码
                tableFieldList.add(buildMainParam("ysbm", bmyysb.getYsbm())); // 预算部门
                tableFieldList.add(buildMainParam("ysejbm", bmyysb.getYsejbm())); // 预算二级部门
                tableFieldList.add(buildMainParam("ysyjbm", bmyysb.getYsyjbm())); // 预算一级部门
                tableFieldList.add(buildMainParam("hjkm", bmyysb.getHjkm())); // 会计科目
                tableFieldList.add(buildMainParam("kmdm", bmyysb.getKmdm())); // 科目代码
                tableFieldList.add(buildMainParam("fyxlx", bmyysb.getFyxlb())); // 费用项类型
                tableFieldList.add(buildMainParam("byyskyye", bmyysb.getByyskyye())); // 本月预算可用余额
                tableFieldList.add(buildMainParam("fyx", mergedOrder.getExpenseItem())); // 费用项
                tableFieldList.add(buildMainParam("bxje", mergedOrder.getTotalAmount().toString())); // 报销金额（合并后）
                tableFieldList.add(buildMainParam("cxje", "0")); // 冲销金额
                tableFieldList.add(buildMainParam("je", mergedOrder.getTotalAmount().toString())); // 付款金额（合并后）
                tableFieldList.add(buildMainParam("fyzy", mergedOrder.getExpenseSummary())); // 费用摘要

            } else if (formTypeEnum == OAFormTypeEnum.SJFYBX) {
                //校验商机项目预算
                OaXmysbDTO xmysb = order.getXmysb();
                log.info("项目预算:" + JSONObject.toJSONString(xmysb));
                if (xmysb == null || xmysb.getId() == null) {
                    return detailDataList;
                }
                // XM-28商机项目费用报销特定字段 (formtable_main_304_dt1)
                tableFieldList.add(buildMainParam("xmys", xmysb.getId())); // 项目预算
                tableFieldList.add(buildMainParam("kmmc_pj", xmysb.getKmmc())); // 科目名称
                tableFieldList.add(buildMainParam("kmdm", xmysb.getKmdm())); // 科目代码
                tableFieldList.add(buildMainParam("fyxlb_pj", xmysb.getFyxlb())); // 费用项类别
                tableFieldList.add(buildMainParam("fyx_pj", mergedOrder.getExpenseItem())); // 费用项
                tableFieldList.add(buildMainParam("sfkhcd", "1")); // 是否客户承担 默认值：否
                tableFieldList.add(buildMainParam("kyysje", xmysb.getKyysje())); // 可用预算金额
                tableFieldList.add(buildMainParam("je", mergedOrder.getTotalAmount().toString())); // 付款金额（合并后）
                tableFieldList.add(buildMainParam("bxje", mergedOrder.getTotalAmount().toString())); // 报销金额（合并后）
                tableFieldList.add(buildMainParam("cxje", "0")); // 冲销金额
                tableFieldList.add(buildMainParam("memo", mergedOrder.getExpenseSummary())); // 说明

            } else if (formTypeEnum == OAFormTypeEnum.ZJXMFYBX) {
                //校验项目预算
                OaXmysbDTO xmysb = order.getXmysb();
                log.info("项目预算:" + JSONObject.toJSONString(xmysb));
                if (xmysb == null || xmysb.getId() == null) {
                    return detailDataList;
                }
                // XM-15在建项目费用报销特定字段 (formtable_main_74_dt1)
                tableFieldList.add(buildMainParam("xmysbm", xmysb.getId())); // 项目预算编码
                tableFieldList.add(buildMainParam("kmmc", xmysb.getKmmc())); // 科目名称
                tableFieldList.add(buildMainParam("kmdm", xmysb.getKmdm())); // 科目代码
                tableFieldList.add(buildMainParam("fyx", mergedOrder.getExpenseItem())); // 费用项
                tableFieldList.add(buildMainParam("fyxlb", xmysb.getFyxlb())); // 费用项类别
                tableFieldList.add(buildMainParam("kyysje", xmysb.getKyysje())); // 可用预算金额
                tableFieldList.add(buildMainParam("bxje", mergedOrder.getTotalAmount().toString())); // 报销金额（合并后）
                tableFieldList.add(buildMainParam("cxje", "0")); // 冲销金额
                tableFieldList.add(buildMainParam("je", mergedOrder.getTotalAmount().toString())); // 付款金额（合并后）
                tableFieldList.add(buildMainParam("sfkhcd", "1")); // 是否客户承担 默认值：否
                tableFieldList.add(buildMainParam("memo", mergedOrder.getExpenseSummary())); // 说明
            }

            if (!tableFieldList.isEmpty()) {
                Map<String, Object> tableRecordMap = new HashMap<>();
                tableRecordMap.put("recordOrder", 0);
                tableRecordMap.put("workflowRequestTableFields", tableFieldList);
                tableRecordList.add(tableRecordMap);
            }
        }

        if (!tableRecordList.isEmpty()) {
            detailDataMap.put("tableDBName", formTypeEnum.getDetailTable());
            detailDataMap.put("workflowRequestTableRecords", tableRecordList);
            detailDataList.add(detailDataMap);
        }

        return detailDataList;
    }


    /**
     * 构建请求名称
     */
    private String buildRequestName(OAFormTypeEnum formTypeEnum, OaAccountVO oaAccountVO) {
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return String.format("%s-%s-%s", formTypeEnum.getName(), oaAccountVO.getLastname(), currentDate);
    }

    /**
     * 更新订单的报销状态
     */
    private void updateOrderReimbursementStatus(List<DdCommonOrder> orders) {
        List<DdFlightTicketOrder> flightOrdersToUpdate = new ArrayList<>();
        List<DdHotelOrder> hotelOrdersToUpdate = new ArrayList<>();
        List<DdVehicleOrder> vehicleOrdersToUpdate = new ArrayList<>();

        //查询OA流程编号
        List<Long> requestIds = orders.stream().map(DdCommonOrder::getRequestId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, String> flowRequestMarkMap = new HashMap<>();
        if (CollUtil.isNotEmpty(requestIds)) {
            flowRequestMarkMap = dbApiUtil.getFlowRequestMark(requestIds).stream().collect(Collectors.toMap(OaFlowRequestMarkDTO::getREQUESTID, OaFlowRequestMarkDTO::getREQUESTMARK));
        }

        for (DdCommonOrder commonOrder : orders) {
            String expenseReportNo = commonOrder.getRequestId() == null || flowRequestMarkMap.isEmpty() ? null : flowRequestMarkMap.get(commonOrder.getRequestId());
            if (commonOrder.getOrderType() == OrderTypeEnum.FLIGHT_TICKET) {
                DdFlightTicketOrder flightOrder = new DdFlightTicketOrder();
                flightOrder.setId(commonOrder.getId());
                flightOrder.setOrderNo(commonOrder.getOrderNo());
                flightOrder.setInitiationStatus(commonOrder.getInitiationStatus());
                flightOrder.setExpenseReportNo(expenseReportNo);
                flightOrder.setRequestId(commonOrder.getRequestId());
                flightOrder.setInitiationRemark(commonOrder.getInitiationRemark());
                flightOrder.setInitiationDate(LocalDate.now());
                flightOrdersToUpdate.add(flightOrder);
            } else if (commonOrder.getOrderType() == OrderTypeEnum.HOTEL) {
                DdHotelOrder hotelOrder = new DdHotelOrder();
                hotelOrder.setId(commonOrder.getId());
                hotelOrder.setOrderNo(commonOrder.getOrderNo());
                hotelOrder.setInitiationStatus(commonOrder.getInitiationStatus());
                hotelOrder.setExpenseReportNo(expenseReportNo);
                hotelOrder.setRequestId(commonOrder.getRequestId());
                hotelOrder.setInitiationRemark(commonOrder.getInitiationRemark());
                hotelOrder.setInitiationDate(LocalDate.now());
                hotelOrdersToUpdate.add(hotelOrder);
            } else if (commonOrder.getOrderType() == OrderTypeEnum.VEHICLE) {
                DdVehicleOrder vehicleOrder = new DdVehicleOrder();
                vehicleOrder.setId(commonOrder.getId());
                vehicleOrder.setOrderNo(commonOrder.getOrderNo());
                vehicleOrder.setInitiationStatus(commonOrder.getInitiationStatus());
                vehicleOrder.setExpenseReportNo(expenseReportNo);
                vehicleOrder.setRequestId(commonOrder.getRequestId());
                vehicleOrder.setInitiationRemark(commonOrder.getInitiationRemark());
                vehicleOrder.setInitiationDate(LocalDate.now());
                vehicleOrdersToUpdate.add(vehicleOrder);
            }
        }

        if (!flightOrdersToUpdate.isEmpty()) {
            ddOrderMapper.batchUpdateFlightTicketOrder(flightOrdersToUpdate);
        }
        if (!hotelOrdersToUpdate.isEmpty()) {
            ddOrderMapper.batchUpdateHotelOrder(hotelOrdersToUpdate);
        }
        if (!vehicleOrdersToUpdate.isEmpty()) {
            ddOrderMapper.batchUpdateVehicleOrder(vehicleOrdersToUpdate);
        }
    }

    /**
     * 生成Excel文件并上传到OA流程附件
     *
     * @param orders      订单列表
     * @param oaAccountVO OA账号信息
     */
    private StringBuilder generateAndUploadExcelFiles(List<DdCommonOrder> orders, OaAccountVO oaAccountVO, String token) {
        StringBuilder fileIdBuilder = new StringBuilder();
        try {
            // 按订单类型分组
            Map<OrderTypeEnum, List<DdCommonOrder>> ordersByType = orders.stream()
                    .collect(Collectors.groupingBy(DdCommonOrder::getOrderType));

            List<InnerDoc> innerDocList = new ArrayList<>();

            // 为每种订单类型生成Excel文件
            for (Map.Entry<OrderTypeEnum, List<DdCommonOrder>> entry : ordersByType.entrySet()) {
                OrderTypeEnum orderType = entry.getKey();
                List<DdCommonOrder> orderList = entry.getValue();
                if (orderList == null || orderList.isEmpty()) {
                    continue;
                }
                String fileName = "【" + orderList.get(0).getAccountingPeriod() + "】滴滴" + orderType.getName() + "费用.xlsx";
                byte[] excelData = generateExcelByOrderType(orderType, orderList);
                if (excelData == null) {
                    continue;
                }

                InnerDoc innerDoc = new InnerDoc();
                innerDoc.setFileName(fileName);
                innerDoc.setBody(excelData);
                innerDocList.add(innerDoc);
            }

            // 上传文件到OA
            oaUtil.uploadFiles(innerDocList, oaAccountVO, token, fileIdBuilder);

        } catch (Exception e) {
            log.error("生成Excel文件并上传失败", e);
            return null;
        }
        return fileIdBuilder;
    }

    /**
     * 根据订单类型生成Excel数据
     *
     * @param orderType 订单类型
     * @param orders    订单列表
     * @return Excel字节数组
     */
    private byte[] generateExcelByOrderType(OrderTypeEnum orderType, List<DdCommonOrder> orders) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            switch (orderType) {
                case FLIGHT_TICKET:
                    // 直接使用DdCommonOrder数据转换
                    List<DdFlightTicketOrderExcelVO> flightExcelData = convertToFlightExcelVO(orders);
                    EasyExcel.write(outputStream, DdFlightTicketOrderExcelVO.class)
                            .sheet("机票订单")
                            .doWrite(flightExcelData);
                    break;
                case HOTEL:
                    // 直接使用DdCommonOrder数据转换
                    List<DdHotelOrderExcelVO> hotelExcelData = convertToHotelExcelVO(orders);
                    EasyExcel.write(outputStream, DdHotelOrderExcelVO.class)
                            .sheet("酒店订单")
                            .doWrite(hotelExcelData);
                    break;
                case VEHICLE:
                    // 直接使用DdCommonOrder数据转换
                    List<DdVehicleOrderExcelVO> vehicleExcelData = convertToVehicleExcelVO(orders);
                    EasyExcel.write(outputStream, DdVehicleOrderExcelVO.class)
                            .sheet("用车订单")
                            .doWrite(vehicleExcelData);
                    break;
                default:
                    throw new BusinessException("不支持的订单类型: " + orderType);
            }
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("生成Excel文件失败", e);
            return null;
        }
    }

    /**
     * 转换为机票订单Excel VO
     */
    private List<DdFlightTicketOrderExcelVO> convertToFlightExcelVO(List<DdCommonOrder> orders) {
        return orders.stream().filter(order -> order.getBmyysb() != null || order.getXmysb() != null)
                .map(order -> {
                    DdFlightTicketOrderExcelVO vo = new DdFlightTicketOrderExcelVO();
                    BeanUtil.copyProperties(order, vo);
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 转换为酒店订单Excel VO
     */
    private List<DdHotelOrderExcelVO> convertToHotelExcelVO(List<DdCommonOrder> orders) {
        return orders.stream().filter(order -> order.getBmyysb() != null || order.getXmysb() != null)
                .map(order -> {
                    DdHotelOrderExcelVO vo = new DdHotelOrderExcelVO();
                    BeanUtil.copyProperties(order, vo);
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 转换为用车订单Excel VO
     */
    private List<DdVehicleOrderExcelVO> convertToVehicleExcelVO(List<DdCommonOrder> orders) {
        return orders.stream().filter(order -> order.getBmyysb() != null || order.getXmysb() != null)
                .map(order -> {
                    DdVehicleOrderExcelVO vo = new DdVehicleOrderExcelVO();
                    BeanUtil.copyProperties(order, vo);
                    return vo;
                }).collect(Collectors.toList());
    }

}