package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.util.SpringContextHolder;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.common.constant.EntitySign;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.entity.dto.AutoBringConditionDTO;
import com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO;
import com.gok.pboot.pms.cost.enums.TaskCategoryEnum;
import com.gok.pboot.pms.cost.mapper.CostPersonnelInformationMapper;
import com.gok.pboot.pms.cost.service.ICostDeliverTaskService;
import com.gok.pboot.pms.cost.service.ICostTaskCategoryManagementService;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.OperatingRecord;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.dto.ProjectTaskeDto;
import com.gok.pboot.pms.entity.dto.ProjectTaskeUserDto;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.IProjectDataService;
import com.gok.pboot.pms.service.IProjectStakeholderMemberService;
import com.gok.pboot.pms.service.IProjectTaskeService;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目任务服务实现类
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class ProjectTaskeServiceImpl extends ServiceImpl<ProjectTaskeMapper,ProjectTaske> implements IProjectTaskeService {

    private final ICostTaskCategoryManagementService costTaskCategoryManagementService;

    private final ProjectTaskeMapper projectTaskeMapper;

    private final ProjectTaskeUserMapper projectTaskeUserMapper;

    private final ProjectInfoMapper projectInfoMapper;

    private final DailyPaperEntryMapper dailyPaperEntryMapper;

    private final TomorrowPlanPaperEntryMapper tomorrowPlanPaperEntryMapper;

    private final OperatingRecordMapper operatingRecordMapper;

    private final ProjectStakeholderMemberMapper projectStakeholderMemberMapper;

    private final RosterMapper rosterMapper;

    private final EntityOptionMapper entityOptionMapper;

    private final IProjectDataService projectDataService;

    private final IProjectStakeholderMemberService projectStakeholderMemberService;
    @Resource
    private RemoteRoleService remoteRoleService;
    @Resource
    private RemoteSendMsgService remoteSendMsgService;
    @Resource
    private RemoteMailService remoteMailService;
    @Resource
    private RemoteOutService remoteOutService;

    private final ProjectScopeHandle projectScopeHandle;

    private final TaskMapper taskMapper;

    private final TaskUserMapper taskUserMapper;

    private final ProjectTaskMapper projectTaskMapper;

    private final ProjectTaskUserMapper projectTaskUserMapper;

    private final PrivilegeMapper privilegeMapper;

    private final CostPersonnelInformationMapper costPersonnelInformationMapper;


    public static final String PARAM_PROJECT_ID = "projectId";
    public static final String PARAM_USER_ID = "userId";
    public static final String GANTT_SPLIT = "split";
    public static final String PARAM_APPLICATION = "Application";
    public static final String PARAM_FILTER_PERMISSION = "permission";
    private static final String ZZ = "在职";
    private static final String LZ = "离职";
    private static final String IS_LEADER = "isLeader";
    private static final String IS_ALL = "isAll";
    private static final String IS_OPERATIONS_ASSISTANT = "isOperationsAssistant";
    private static final List<String> miniLeaderRoles = ImmutableList.of("ADD_PERSONNEL", "PROJECT_QUEST_EDIT", "PROJECT_QUEST_END", "PROJECT_QUEST_RESTART", "PROJECT_QUEST_OPERATION_RECORD");


    @Value("${project.task.leftUrl}")
    private String projectUrl;

    @Value("${project.task.taskTabUrl}")
    private String taskTabUrl;

    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;


    private final BcpLoggerUtils bcpLoggerUtils;

    @Override
    public List<ProjectTaskeVo> getByProjectId(Long projectId) {
        List<ProjectTaske> projectTaskes = projectTaskeMapper.findListByProjectId(projectId);
        if (CollUtil.isNotEmpty(projectTaskes)) {
            return BeanUtil.copyToList(projectTaskes, ProjectTaskeVo.class);
        }
        return new ArrayList<>();
    }

    @Override
    public ProjectOverviewVo findOverview(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        Long projectId = (Long) filter.get(PARAM_PROJECT_ID);

        ProjectOverviewVo projectOverviewVo = new ProjectOverviewVo();
        projectOverviewVo.setProjectId(projectId);

        // 设置是否内部项目
        projectOverviewVo.setInternalFlag(projectInfoMapper.isInternalProjectById(projectId));

        // 获取项目状态
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (Optional.ofNullable(projectInfo).isPresent()) {
            if (CharSequenceUtil.isNotBlank(projectInfo.getProjectStatus())) {
                projectOverviewVo.setProjectStatus(Integer.valueOf(projectInfo.getProjectStatus()));
                projectOverviewVo.setProjectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, projectOverviewVo.getProjectStatus()));
            }
            projectOverviewVo.setCtime(projectInfo.getCtime());
        }

        // 获取项目下的任务数以及总人天数
        // 根据是否铁三角获取可查看的任务列表
        List<ProjectTaske> taskes;
        Long userId = SecurityUtils.getUser().getId();
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        List<Long> userIdList = dataScope.getUserIdList();
        if (CollUtil.isEmpty(userIdList)) {
            userIdList.add(-1L);
        }
        filter.put(PARAM_USER_ID, userId);
        if (isLeader(projectId, userIdList)) {
            filter.put(IS_LEADER, 1);
        }
        if (Boolean.TRUE.equals(dataScope.getIsAll())) {
            filter.put(IS_ALL, 1);
        }
        //taskes = projectTaskeMapper.findListItem(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter, userIdList).getRecords();
        //总人天数 显示所有的任务合计数
        taskes = projectTaskeMapper.findListByProjectId(projectId);
        if (CollUtil.isNotEmpty(taskes)) {
            List<Long> taskIds = taskes.stream().map(ProjectTaske::getId).collect(Collectors.toList());
            List<DailyPaperEntry> dailyPaperEntries = dailyPaperEntryMapper.findPassedByTaskIds(taskIds);
            BigDecimal totalManDays = BigDecimal.valueOf(0);
            for (DailyPaperEntry dailyPaperEntry : dailyPaperEntries) {
                totalManDays = totalManDays.add(dailyPaperEntry.getNormalHours()).add(dailyPaperEntry.getAddedHours());
            }
            projectOverviewVo.setTotalTaskNumber(taskIds.size());
            projectOverviewVo.setTotalManDays(formatHours(totalManDays));
        } else {
            projectOverviewVo.setTotalTaskNumber(0);
            projectOverviewVo.setTotalManDays(String.valueOf(0));
        }
        // 获取项目的预估总人天
        ProjectDataVO projectDataVO = projectDataService.findById(projectId);
        if (Optional.ofNullable(projectDataVO).isPresent()) {
            projectOverviewVo.setEstimatedTotalManDays(projectDataVO.getEstimatedTotalManDays());
        }

        return projectOverviewVo;
    }

    @Override
    public Page<ProjectTaskeVo> findList(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        Long projectId = (Long) filter.get(PARAM_PROJECT_ID);
        if (!Optional.ofNullable(projectId).isPresent() || !projectInfoMapper.isExist(projectId)) {
            return new Page<>();
        }

        Page<ProjectTaske> projectTaskePage;
        Long userId = SecurityUtils.getUser().getId();
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        List<Long> userIdList = dataScope.getUserIdList();
        if (CollUtil.isEmpty(userIdList)) {
            userIdList.add(-1L);
        }
        filter.put(PARAM_USER_ID, userId);
        if (isLeader(projectId, userIdList)) {
            filter.put(IS_LEADER, 1);
        }
        if (Boolean.TRUE.equals(dataScope.getIsAll())) {
            filter.put(IS_ALL, 1);
        }
        if (this.isOperationsAssistant(projectId, userIdList)) {
            filter.put(IS_OPERATIONS_ASSISTANT, 1);
        }
        projectTaskePage = projectTaskeMapper.findListItem(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter, userIdList);

        // 构造分页对象
        List<ProjectTaske> records = projectTaskePage.getRecords();
        Page<ProjectTaskeVo> projectTaskeVoPage = new Page<>();
        projectTaskeVoPage.setTotal(projectTaskePage.getTotal());
        projectTaskeVoPage.setCurrent(projectTaskePage.getCurrent());
        projectTaskeVoPage.setSize(projectTaskePage.getSize());
        if (CollUtil.isEmpty(records)) {
            return projectTaskeVoPage;
        }

        // 获取任务总工时、正常工时、加班工时（工作日、休息日、节假日）
        List<Long> taskIds = records.stream().map(ProjectTaske::getId).collect(Collectors.toList());
        List<DailyPaperEntry> dailyPaperEntries = dailyPaperEntryMapper.findByTaskIds(taskIds);
        List<Long> existsAttachmentHourTaskIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(dailyPaperEntries)) {
            existsAttachmentHourTaskIds = ObjectUtils.defaultIfNull(dailyPaperEntries
                    .stream()
                    .filter(d -> Arrays.asList(ApprovalStatusEnum.DSH.getValue(), ApprovalStatusEnum.YTG.getValue()).contains(d.getApprovalStatus()))
                    .map(DailyPaperEntry::getTaskId)
                    .distinct()
                    .collect(Collectors.toList()), ImmutableList.of());
        }
        List<DailyPaperEntry> passedDailyPaperEntries = dailyPaperEntryMapper.findPassedByTaskIds(taskIds);
        Map<Long, DailyPaperHourVO> dailyPaperEntryMap = new HashMap<>();
        passedDailyPaperEntries.forEach(d -> {
            Long taskId = d.getTaskId();
            DailyPaperHourVO hours = dailyPaperEntryMap.get(taskId);
            if (Optional.ofNullable(hours).isPresent()) {
                addHours(hours, d);
            } else {
                fillHours(hours = new DailyPaperHourVO(), d);
            }
            dailyPaperEntryMap.put(taskId, hours);
        });

        // 获取待审核工时
        List<DailyPaperEntry> dshDailyPaperEntries = dailyPaperEntryMapper.findDSHByTaskIds(taskIds);
        Map<Long, BigDecimal> dshDailyPaperEntryMap = new HashMap<>();
        dshDailyPaperEntries.forEach(d -> {
            Long taskId = d.getTaskId();
            BigDecimal decimal = dshDailyPaperEntryMap.get(d.getTaskId());
            if (Optional.ofNullable(decimal).isPresent()) {
                decimal = decimal.add(d.getNormalHours()).add(d.getAddedHours());
                dshDailyPaperEntryMap.put(taskId, decimal);
            } else {
                BigDecimal normalHours = Optional.ofNullable(d.getNormalHours()).orElse(BigDecimal.ZERO);
                BigDecimal addedHours = Optional.ofNullable(d.getAddedHours()).orElse(BigDecimal.ZERO);
                decimal = normalHours.add(addedHours);
                dshDailyPaperEntryMap.put(taskId, decimal);
            }
        });

        // 获取无效工时
        List<DailyPaperEntry> invalidDailyPaperEntries = dailyPaperEntryMapper.findInvalidByTaskIds(taskIds);
        Map<Long, BigDecimal> invalidDailyPaperEntryMap = new HashMap<>();
        invalidDailyPaperEntries.forEach(d -> {
            Long taskId = d.getTaskId();
            BigDecimal totalValidHours = invalidDailyPaperEntryMap.get(taskId);
            if (Optional.ofNullable(totalValidHours).isPresent()) {
                totalValidHours = totalValidHours.add(d.getNormalHours()).add(d.getAddedHours());
                invalidDailyPaperEntryMap.put(taskId, totalValidHours);
            } else {
                BigDecimal normalHours = Optional.ofNullable(d.getNormalHours()).orElse(BigDecimal.ZERO);
                BigDecimal addedHours = Optional.ofNullable(d.getAddedHours()).orElse(BigDecimal.ZERO);
                totalValidHours = normalHours.add(addedHours);
                invalidDailyPaperEntryMap.put(taskId, totalValidHours);
            }
        });

        // 转化vo、设置枚举txt、设置工时、设置当日任务状态、设置计划起止时间跨度
        List<ProjectTaskeVo> projectTaskeVos = BeanUtil.copyToList(records, ProjectTaskeVo.class);
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        projectTaskeVos.forEach(projectTaskeVo -> {
            if (projectTaskeVo.getKind() != null) {
                projectTaskeVo.setKindTxt(costTaskCategoryMap.getOrDefault(projectTaskeVo.getKind(), new CostTaskCategoryManagement()).getTaskCategoryName());
            }
            projectTaskeVo.setPermanentFlagTxt(EnumUtils.getNameByValue(YesOrNoEnum.class, projectTaskeVo.getPermanentFlag()));
            DailyPaperHourVO hours = dailyPaperEntryMap.get(projectTaskeVo.getId());
            if (Optional.ofNullable(hours).isPresent()) {
                BigDecimal normalHours = hours.getNormalHours();
                BigDecimal addedHours = hours.getAddedHours();
                projectTaskeVo.setNormalHour(formatHours(normalHours));
                projectTaskeVo.setAddedHour(formatHours(addedHours));
                projectTaskeVo.setWorkOvertimeHours(formatHours(hours.getWorkOvertimeHours()));
                projectTaskeVo.setRestOvertimeHours(formatHours(hours.getRestOvertimeHours()));
                projectTaskeVo.setHolidayOvertimeHours(formatHours(hours.getHolidayOvertimeHours()));
                projectTaskeVo.setTotalHour(formatHours(normalHours.add(addedHours)));
            } else {
                projectTaskeVo.setNormalHour("0");
                projectTaskeVo.setAddedHour("0");
                projectTaskeVo.setWorkOvertimeHours("0");
                projectTaskeVo.setRestOvertimeHours("0");
                projectTaskeVo.setHolidayOvertimeHours("0");
                projectTaskeVo.setTotalHour("0");
            }
            BigDecimal dshBigDecimal = dshDailyPaperEntryMap.get(projectTaskeVo.getId());
            if (Optional.ofNullable(dshBigDecimal).isPresent()) {
                projectTaskeVo.setDshHour(formatHours(dshBigDecimal));
            } else {
                projectTaskeVo.setDshHour("0");
            }
            BigDecimal invalidBigDecimal = invalidDailyPaperEntryMap.get(projectTaskeVo.getId());
            if (Optional.ofNullable(invalidBigDecimal).isPresent()) {
                projectTaskeVo.setInvalidHour(formatHours(invalidBigDecimal));
            } else {
                projectTaskeVo.setInvalidHour("0");
            }
            setTaskStateAndExpectDuration(projectTaskeVo);
        });
        // 判断任务是否挂靠工时
        // 设置是否显示删除按钮
        List<Long> cantDelTaskIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskIds)) {
            cantDelTaskIds = ObjectUtils.defaultIfNull(dailyPaperEntryMapper.findCantDelTaskIds(taskIds), ImmutableList.of());
        }
        for (ProjectTaskeVo projectTaskeVo : projectTaskeVos) {
            if (existsAttachmentHourTaskIds.contains(projectTaskeVo.getId())) {
                projectTaskeVo.setIsAttachmentHour(YesOrNoEnum.YES.getValue());
            } else {
                projectTaskeVo.setIsAttachmentHour(YesOrNoEnum.NO.getValue());
            }
            if (cantDelTaskIds.contains(projectTaskeVo.getId())) {
                projectTaskeVo.setShowDelButtonFlag(YesOrNoEnum.NO.getValue());
            } else {
                projectTaskeVo.setShowDelButtonFlag(YesOrNoEnum.YES.getValue());
            }
        }

        // 配置任务负责人列表和任务参与人列表
        setTaskLeadersAndMembers(projectTaskeVos);
        // 配置按钮权限
        setButtonAuthorities(projectId, projectTaskeVos, Long.valueOf(request.getHeader(PARAM_APPLICATION)), String.valueOf(filter.get(PARAM_FILTER_PERMISSION)));

        projectTaskeVoPage.setRecords(projectTaskeVos);

        return projectTaskeVoPage;
    }

    @Override
    public Page<MyTaskVo> findMyTaskList(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        filter.put(PARAM_USER_ID, SecurityUtils.getUser().getId());
        Page<MyTaskVo> myTaskVoPage = projectTaskeMapper.findMyTask(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        List<MyTaskVo> records = myTaskVoPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            // 获取任务总工时、正常工时、加班工时
            List<Long> taskIds = records.stream().map(MyTaskVo::getId).collect(Collectors.toList());
            List<DailyPaperEntry> dailyPaperEntries = dailyPaperEntryMapper.findByTaskIds(taskIds);
            // 待审核和已通过的任务id列表
            List<Long> existsAttachmentHourTaskIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(dailyPaperEntries)) {
                existsAttachmentHourTaskIds = dailyPaperEntries.stream()
                        .filter(d -> Arrays.asList(ApprovalStatusEnum.DSH.getValue(), ApprovalStatusEnum.YTG.getValue())
                                .contains(d.getApprovalStatus()))
                        .map(DailyPaperEntry::getTaskId).distinct().collect(Collectors.toList());
            }
            List<DailyPaperEntry> passedDailyPaperEntries = dailyPaperEntryMapper.findPassedByTaskIds(taskIds);
            Map<Long, DailyPaperHourVO> dailyPaperEntryMap = new HashMap<>();
            passedDailyPaperEntries.forEach(d -> {
                Long taskId = d.getTaskId();
                DailyPaperHourVO hours = dailyPaperEntryMap.get(taskId);
                if (Optional.ofNullable(hours).isPresent()) {
                    addHours(hours, d);
                } else {
                    hours = new DailyPaperHourVO();
                    fillHours(hours, d);
                }
                dailyPaperEntryMap.put(taskId, hours);
            });
            records.forEach(t -> {
                DailyPaperHourVO hours = dailyPaperEntryMap.get(t.getId());
                if (Optional.ofNullable(hours).isPresent()) {
                    t.setNormalHour(formatHours(hours.getNormalHours()));
                    t.setAddedHour(formatHours(hours.getAddedHours()));
                    t.setWorkOvertimeHours(formatHours(hours.getWorkOvertimeHours()));
                    t.setRestOvertimeHours(formatHours(hours.getRestOvertimeHours()));
                    t.setHolidayOvertimeHours(formatHours(hours.getHolidayOvertimeHours()));
                    t.setTotalHour(formatHours(hours.getTotalHours()));
                } else {
                    t.setNormalHour("0");
                    t.setAddedHour("0");
                    t.setWorkOvertimeHours("0");
                    t.setRestOvertimeHours("0");
                    t.setHolidayOvertimeHours("0");
                    t.setTotalHour("0");
                }
                setTaskStateAndExpectDuration(t);
            });
            // 判断任务是否挂靠工时
            if (CollUtil.isNotEmpty(existsAttachmentHourTaskIds)) {
                for (MyTaskVo myTaskVo : records) {
                    if (existsAttachmentHourTaskIds.contains(myTaskVo.getId())) {
                        myTaskVo.setIsAttachmentHour(YesOrNoEnum.YES.getValue());
                    } else {
                        myTaskVo.setIsAttachmentHour(YesOrNoEnum.NO.getValue());
                    }
                }
            }
            // 设置是否显示删除按钮
            List<Long> cantDelTaskIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(taskIds)) {
                cantDelTaskIds = ObjectUtils.defaultIfNull(dailyPaperEntryMapper.findCantDelTaskIds(taskIds), ImmutableList.of());
            }
            for (MyTaskVo myTaskVo : records) {
                if (cantDelTaskIds.contains(myTaskVo.getId())) {
                    myTaskVo.setShowDelButtonFlag(YesOrNoEnum.NO.getValue());
                } else {
                    myTaskVo.setShowDelButtonFlag(YesOrNoEnum.YES.getValue());
                }
            }
        }

        return myTaskVoPage;
    }

    /**
     * 增加任务工时
     *
     * @param hours      目标任务工时
     * @param paperEntry 要添加的工时
     */
    private void addHours(DailyPaperHourVO hours, DailyPaperEntry paperEntry) {
        BigDecimal normalHours = Optional.ofNullable(paperEntry.getNormalHours()).orElse(BigDecimal.ZERO);
        BigDecimal addedHours = Optional.ofNullable(paperEntry.getAddedHours()).orElse(BigDecimal.ZERO);
        hours.setNormalHours(hours.getNormalHours().add(normalHours));
        hours.setAddedHours(hours.getAddedHours().add(addedHours));
        hours.setWorkOvertimeHours(hours.getWorkOvertimeHours().add(Optional.ofNullable(paperEntry.getWorkOvertimeHours()).orElse(BigDecimal.ZERO)));
        hours.setRestOvertimeHours(hours.getRestOvertimeHours().add(Optional.ofNullable(paperEntry.getRestOvertimeHours()).orElse(BigDecimal.ZERO)));
        hours.setHolidayOvertimeHours(hours.getHolidayOvertimeHours().add(Optional.ofNullable(paperEntry.getHolidayOvertimeHours()).orElse(BigDecimal.ZERO)));
        hours.setTotalHours(hours.getTotalHours().add(normalHours).add(addedHours));
    }

    /**
     * 填充任务工时
     *
     * @param hours      目标任务工时
     * @param paperEntry 要添加的工时
     */
    private void fillHours(DailyPaperHourVO hours, DailyPaperEntry paperEntry) {
        BigDecimal normalHours = Optional.ofNullable(paperEntry.getNormalHours()).orElse(BigDecimal.ZERO);
        BigDecimal addedHours = Optional.ofNullable(paperEntry.getAddedHours()).orElse(BigDecimal.ZERO);
        hours.setNormalHours(normalHours);
        hours.setAddedHours(addedHours);
        hours.setWorkOvertimeHours(Optional.ofNullable(paperEntry.getWorkOvertimeHours()).orElse(BigDecimal.ZERO));
        hours.setRestOvertimeHours(Optional.ofNullable(paperEntry.getRestOvertimeHours()).orElse(BigDecimal.ZERO));
        hours.setHolidayOvertimeHours(Optional.ofNullable(paperEntry.getHolidayOvertimeHours()).orElse(BigDecimal.ZERO));
        hours.setTotalHours(normalHours.add(addedHours));
    }

    @Override
    public Page<ProjectTaskeGanttVo> findGanttList(PageRequest pageRequest, HttpServletRequest request) {
        Page<ProjectTaskeGanttVo> projectTaskeGanttVoPage = new Page<>();
        projectTaskeGanttVoPage.setCurrent(pageRequest.getPageNumber());
        projectTaskeGanttVoPage.setSize(pageRequest.getPageSize());

        Page<ProjectTaskeVo> voPage = findList(pageRequest, request);
        List<ProjectTaskeVo> records = voPage.getRecords();
        projectTaskeGanttVoPage.setTotal(voPage.getTotal());
        projectTaskeGanttVoPage.setPages(voPage.getPages());
        // 为空时直接返回
        if (CollUtil.isEmpty(records)) {
            return projectTaskeGanttVoPage;
        }
        List<ProjectTaskeGanttVo> ganttVos = new ArrayList<>();
        records.forEach(projectTaskeVo -> setTaskProcess(projectTaskeVo, ganttVos));
        projectTaskeGanttVoPage.setRecords(ganttVos);

        return projectTaskeGanttVoPage;
    }

    @Override
    public ProjectTaskeVo findOne(Long id) {
        ProjectTaske projectTaske = projectTaskeMapper.selectById(id);
        if (!Optional.ofNullable(projectTaske).isPresent()) {
            throw new ValidationException("任务不存在");
        }

        // 转化vo、设置枚举txt
        ProjectTaskeVo projectTaskeVo = BeanUtil.copyProperties(projectTaske, ProjectTaskeVo.class);
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        if (projectTaskeVo.getKind()!=null) {
            projectTaskeVo.setKindTxt(costTaskCategoryMap.getOrDefault(projectTaskeVo.getKind(), new CostTaskCategoryManagement()).getTaskCategoryName());
        }
        projectTaskeVo.setPermanentFlagTxt(EnumUtils.getNameByValue(YesOrNoEnum.class, projectTaskeVo.getPermanentFlag()));
        setTaskStateAndExpectDuration(projectTaskeVo);

        // 配置任务负责人列表和任务参与人列表
        Long taskId = projectTaskeVo.getId();
        List<ProjectTaskeUser> taskeUsers = projectTaskeUserMapper.findListByTaskId(taskId, null);
        if (CollUtil.isNotEmpty(taskeUsers)) {
            List<ProjectTaskeUserVo> taskeUserVos = BeanUtil.copyToList(taskeUsers, ProjectTaskeUserVo.class);
            projectTaskeVo.setLeaders(taskeUserVos.stream().filter(taskeUser -> ProjectTaskRoleEnum.LEADER.getValue().equals(taskeUser.getTaskRole())).collect(Collectors.toList()));
            projectTaskeVo.setMembers(taskeUserVos.stream().filter(taskeUser -> ProjectTaskRoleEnum.MEMBER.getValue().equals(taskeUser.getTaskRole())).collect(Collectors.toList()));
        }

        return projectTaskeVo;
    }

    @Override
    public Page<OperatingRecordPageVO> findOperatingRecordPage(Long id, PageRequest pageRequest) {
        return operatingRecordMapper.findOperatingRecordPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), id);
    }

    @Override
    public LocalDate findFinalDailyDate(Long id) {
        return dailyPaperEntryMapper.findFinalDailyDateByTaskId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(ProjectTaskeDto request) {
        if (!Optional.ofNullable(projectInfoMapper.selectById(request.getProjectId())).isPresent()) {
            throw new ValidationException("项目不存在");
        }
        // 设置默认字段值，防止误填
        request.setId(null);
        request.setState(ProjectTaskStateEnum.NORMAL.getValue());
        if (YesOrNoEnum.YES.getValue().equals(request.getPermanentFlag())) {
            request.setExpectStartDate(null);
            request.setExpectEndDate(null);
        } else {
            validExceptDate(request.getProjectId(), request.getExpectStartDate(), request.getExpectEndDate());
        }
        request.setStartDate(null);
        request.setEndDate(null);

        ProjectTaske projectTaske = BaseBuildEntityUtil.buildSave(ProjectTaske.of(request));
        projectTaskeMapper.insert(projectTaske);
        Long taskId = projectTaske.getId();

        // 保存任务-人员关系
        List<ProjectTaskeUserDto> leaderNames = request.getLeaderNames();
        if (CollUtil.isNotEmpty(leaderNames)) {
            leaderNames.forEach(l -> {
                l.setTaskId(taskId);
                l.setTaskRole(ProjectTaskRoleEnum.LEADER.getValue());
            });
            resetMember(projectTaske.getId(), ProjectTaskRoleEnum.LEADER, leaderNames, false);
        }
        List<ProjectTaskeUserDto> memberNames = request.getMemberNames();
        if (CollUtil.isNotEmpty(memberNames)) {
            memberNames.forEach(m -> {
                m.setTaskId(taskId);
                m.setTaskRole(ProjectTaskRoleEnum.MEMBER.getValue());
            });
            resetMember(projectTaske.getId(), ProjectTaskRoleEnum.MEMBER, memberNames, false);
        }

        // 创建新任务时通知任务负责人(过滤任务创建人)
        leaderNames = leaderNames.stream().filter(l -> !SecurityUtils.getUser().getId().equals(l.getUserId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(leaderNames)) {
            Map<Long, String> userIdToNameMap = leaderNames.stream().collect(Collectors.toMap(ProjectTaskeUserDto::getUserId, ProjectTaskeUserDto::getUserName));
            sendCreateTaskMsg(userIdToNameMap, projectTaske);
        }
        //新建任务【任务名称】
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.NEW_TASK,
                request.getTitle());
        return taskId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(ProjectTaskeDto request) {
        ProjectTaske taske = getById(request.getId());
        if (taske == null) {
            throw new ValidationException("任务不存在，无法修改~");
        }
        if (EnumUtils.valueEquals(taske.getType(), ProjectTaskeTypeEnum.COST_TASK)) {
            throw new ValidationException("工单任务不允许直接修改，请修改对应工单~");
        }
        Long taskId = request.getId();
        // 设置默认字段，防止误填
        if (EnumUtils.valueEquals(request.getPermanentFlag(), YesOrNoEnum.YES)) {
            request.setExpectStartDate(null);
            request.setExpectEndDate(null);
        } else {
            validExceptDate(request.getProjectId(), request.getExpectStartDate(), request.getExpectEndDate());
        }
        ProjectTaske projectTaske = BaseBuildEntityUtil.buildUpdate(ProjectTaske.of(request));
        projectTaske.setState(Arrays.asList(TodayTaskStateEnum.NORMAL.getValue(), TodayTaskStateEnum.DELAY.getValue()).contains(projectTaske.getState()) ? 0 : 1);

        // 更新任务-人员关系
        List<ProjectTaskeUserDto> leaderNames = request.getLeaderNames();
        if (CollUtil.isNotEmpty(leaderNames)) {
            leaderNames.forEach(l -> {
                l.setTaskId(taskId);
                l.setTaskRole(ProjectTaskRoleEnum.LEADER.getValue());
            });
            resetMember(taskId, ProjectTaskRoleEnum.LEADER, leaderNames, true);
        }
        List<ProjectTaskeUserDto> memberNames = request.getMemberNames();
        if (CollUtil.isNotEmpty(memberNames)) {
            memberNames.forEach(m -> {
                m.setTaskId(taskId);
                m.setTaskRole(ProjectTaskRoleEnum.MEMBER.getValue());
            });
            resetMember(taskId, ProjectTaskRoleEnum.MEMBER, memberNames, true);
        }
        resetMemberNames(projectTaske);
        projectTaskeMapper.updateById(projectTaske);

        // 更新已有关联日报任务名称
        dailyPaperEntryMapper.updateTaskNameByTaskId(request.getId(), request.getTitle());
        tomorrowPlanPaperEntryMapper.updateTaskNameByTaskId(request.getId(), request.getTitle());

        return taskId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        if (projectTaskeMapper.isExistById(id) == 0) {
            throw new ValidationException("任务不存在，无法删除");
        }
        // 判断是否有工时挂靠
        if (Boolean.TRUE.equals(dailyPaperEntryMapper.isExistsAttachedHours(id))) {
            throw new ValidationException("任务存在工时挂靠，无法删除");
        }
        if (Boolean.TRUE.equals(dailyPaperEntryMapper.isExistsUnauditedByTaskIdForDel(id))) {
            throw new ValidationException("当前任务存在未审核工时，请审核完成后才可结束任务");
        }
        ProjectTaske projectTaske = projectTaskeMapper.selectById(id);
        projectTaskeMapper.logicDeleteById(id);
        projectTaskeUserMapper.logicDeleteByTaskId(id);
        if (Optional.ofNullable(projectTaske).isPresent()) {
            //删除【任务名称】
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.DELETE_TASK,
                    projectTaske.getTitle());
        }
        return Boolean.TRUE;
    }

    @Override
    public Page<ProjectTaskeUserVo> getMembers(Long id, PageRequest pageRequest) {
        Page<ProjectTaskeUser> taskeUserPage = projectTaskeUserMapper.findPageByTaskId(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), id, ProjectTaskRoleEnum.MEMBER.getValue());
        List<ProjectTaskeUser> taskeUsers = taskeUserPage.getRecords();
        List<ProjectTaskeUserVo> projectTaskeUserVos = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskeUsers)) {
            projectTaskeUserVos = BeanUtil.copyToList(taskeUsers, ProjectTaskeUserVo.class);
            List<Long> memberIds = projectTaskeUserVos.stream().map(ProjectTaskeUserVo::getUserId).distinct().collect(Collectors.toList());
            Map<Long, Roster> userIdMap = Optional.ofNullable(rosterMapper.findUserIdMap(memberIds)).orElse(new HashMap<>());
            projectTaskeUserVos.forEach(r -> {
                Roster roster = userIdMap.get(r.getUserId());
                if (Optional.ofNullable(roster).isPresent()) {
                    r.setEmployeeStatus(roster.getEmployeeStatus());
                    if (Objects.equals(EmployeeStatusEnum.RESIGN.getValue(), roster.getEmployeeStatus())) {
                        r.setEmployeeStatusTxt(LZ);
                    } else {
                        r.setEmployeeStatusTxt(ZZ);
                    }
                }
            });
        }
        Page<ProjectTaskeUserVo> page = new Page<>();
        page.setRecords(projectTaskeUserVos);
        page.setCurrent(taskeUserPage.getCurrent());
        page.setSize(taskeUserPage.getSize());
        page.setTotal(taskeUserPage.getTotal());

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delMember(Long id) {
        ProjectTaskeUser member = projectTaskeUserMapper.selectById(id);
        if (!Optional.ofNullable(member).isPresent()) {
            throw new ValidationException("任务成员不存在，无法删除");
        }
        projectTaskeUserMapper.deleteById(id);
        ProjectTaske projectTaske = projectTaskeMapper.selectById(member.getTaskId());
        resetMemberNames(projectTaske);
        projectTaskeMapper.updateById(projectTaske);

        insertOperatingRecord(
                ProjectTaskRoleEnum.LEADER.getValue().equals(member.getTaskRole()) ? OperationTypeEnum.YCFZR.getValue() : OperationTypeEnum.YC.getValue(),
                member.getUserName(),
                member.getTaskId());

        //添加/删除參与人【名单】
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.EDITING_PARTICIPANTS,
                "删除", member.getUserName());

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long finish(Long id, LocalDate finishDate) {
        // 结束日期非空判断
        if (!Optional.ofNullable(finishDate).isPresent()) {
            throw new ValidationException("结束日期不能为空");
        }
        // 判断改任务是否还有未审核日报
        if (Boolean.TRUE.equals(dailyPaperEntryMapper.isExistsUnauditedByTaskId(id))) {
            throw new ValidationException("当前任务存在未审核工时，请审核完成后才可结束任务");
        }

        LocalDate finalDate = dailyPaperEntryMapper.findFinalDailyDateByTaskId(id);
        if (Optional.ofNullable(finalDate).isPresent() && finalDate.isAfter(finishDate)) {
            throw new ValidationException("结束日期不得早于最后一个填写工时的日期");
        }

        projectTaskeMapper.finishById(id, finishDate);

        // 保存操作记录--结束任务
        insertOperatingRecord(OperationTypeEnum.ZTBG.getValue(), ProjectTaskStateEnum.FINISHED.getName(), id);
        ProjectTaske projectTaske = projectTaskeMapper.selectById(id);
        if (Optional.ofNullable(projectTaske).isPresent()) {
            //结束【任务名称】
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.END_TASK,
                    projectTaske.getTitle());
        }

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long restart(Long id) {
        projectTaskeMapper.restartById(id);

        // 保存操作记录--重启任务
        insertOperatingRecord(OperationTypeEnum.ZTBG.getValue(), ProjectTaskStateEnum.NORMAL.getName(), id);

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addMember(Long id, List<ProjectTaskeUserDto> members) {
        ProjectTaske projectTaske = projectTaskeMapper.selectById(id);
        if (!Optional.ofNullable(projectTaske).isPresent()) {
            throw new ValidationException("任务不存在");
        }
        List<String> memberName = new ArrayList<>();
        // 全量重置任务参与人
        if (CollUtil.isNotEmpty(members)) {
            members.forEach(m -> {
                m.setTaskId(id);
                m.setTaskRole(ProjectTaskRoleEnum.MEMBER.getValue());
                memberName.add(m.getUserName());
            });
            resetMember(id, ProjectTaskRoleEnum.MEMBER, members, true);
            projectTaske.setMemberNames(members.stream().map(ProjectTaskeUserDto::getUserName).collect(Collectors.joining(StrUtil.COMMA)));
        } else {
            resetMember(id, ProjectTaskRoleEnum.MEMBER, members, true);
            projectTaske.setMemberNames(CharSequenceUtil.EMPTY);
        }
        BaseBuildEntityUtil.buildUpdate(projectTaske);
        resetMemberNames(projectTaske);
        projectTaskeMapper.updateById(projectTaske);
        //添加/删除參与人【名单】
        if (CollUtil.isNotEmpty(memberName)) {
            String result = memberName.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.EDITING_PARTICIPANTS,
                    "添加", result);
        }

        return id;
    }

    private void resetMemberNames(ProjectTaske projectTaske) {
        List<ProjectTaskeUser> memberNames = projectTaskeUserMapper.findListByTaskId(projectTaske.getId(), ProjectTaskRoleEnum.MEMBER.getValue());
        List<ProjectTaskeUser> leaderNames = projectTaskeUserMapper.findListByTaskId(projectTaske.getId(), ProjectTaskRoleEnum.LEADER.getValue());
        if (CollUtil.isNotEmpty(memberNames)) {
            projectTaske.setMemberNames(memberNames.stream().map(ProjectTaskeUser::getUserName).collect(Collectors.joining(",")));
        } else {
            projectTaske.setMemberNames(CharSequenceUtil.EMPTY);
        }
        if (CollUtil.isNotEmpty(leaderNames)) {
            projectTaske.setLeaderNames(leaderNames.stream().map(ProjectTaskeUser::getUserName).collect(Collectors.joining(",")));
        } else {
            projectTaske.setLeaderNames(CharSequenceUtil.EMPTY);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncCreateDefaultTask() {
        // 全量查询所有项目
        LambdaQueryWrapper<ProjectInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectInfo::getInitTaskFlag, YesOrNoEnum.NO.getValue());
        List<ProjectInfo> projectInfos = projectInfoMapper.selectList(queryWrapper);
        // 根据项目id查询未创建默认任务的项目
        if (CollUtil.isNotEmpty(projectInfos)) {
            List<Long> managerProjectIds = new ArrayList<>();
            List<Long> deliveryProjectIds = new ArrayList<>();
            List<Long> projectIds = projectInfos.stream().map(ProjectInfo::getId).collect(Collectors.toList());
            List<Long> specialProjectIds = entityOptionMapper.findIdsBySign(EntitySign.SPECIAL_PROJECT);
            // 区分特殊项目
            if (CollUtil.isNotEmpty(specialProjectIds)) {
                projectInfos.forEach(p -> {
                    if (specialProjectIds.contains(p.getId()) || NumberUtils.LONG_ONE.equals(p.getIsNotInternalProject())) {
                        managerProjectIds.add(p.getId());
                    } else {
                        deliveryProjectIds.add(p.getId());
                    }
                });
            } else {
                deliveryProjectIds.addAll(projectIds);
            }
            List<ProjectTaske> projectTaskes = new ArrayList<>();
            List<ProjectTaskeUser> projectTaskeUsers = new ArrayList<>();
            // 构建管理型项目
            if (CollUtil.isNotEmpty(managerProjectIds)) {
                Map<Long, ProjectInfo> projectInfoMap = projectInfos.stream().collect(Collectors.toMap(ProjectInfo::getId, Function.identity(), (a, b) -> b));
                for (Long pId : managerProjectIds) {
                    buildManageProjectTask(projectTaskes, projectTaskeUsers, projectInfoMap, pId);
                }
            }
            // 构建项目交付型项目
            if (CollUtil.isNotEmpty(deliveryProjectIds)) {
                Map<Long, ProjectInfo> projectInfoMap = projectInfos.stream().collect(Collectors.toMap(ProjectInfo::getId, Function.identity(), (a, b) -> b));
                for (Long pId : deliveryProjectIds) {
                    buildDeliverProjectTask(projectTaskes, projectTaskeUsers, projectInfoMap, pId);
                }
            }
            if (CollUtil.isNotEmpty(projectTaskes)) {
                projectTaskeMapper.batchSave(projectTaskes);
            }
            if (CollUtil.isNotEmpty(projectTaskeUsers)) {
                projectTaskeUsers = projectTaskeUsers.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(u -> u.getUserId() + ";" + u.getTaskId()))), ArrayList::new));
                List<Long> userIds = projectTaskeUsers.stream().map(ProjectTaskeUser::getUserId).distinct().collect(Collectors.toList());
                Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(userIds);
                List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
                Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));
                projectTaskeUsers.forEach(member -> {
                    Roster roster = userIdMap.get(member.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        member.setPosition(roster.getJob());
                        member.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                    }
                });
                projectTaskeUserMapper.batchSave(projectTaskeUsers);
            }
            projectInfoMapper.updateInitTaskFlag(projectIds, YesOrNoEnum.YES.getValue());
        }

        return Boolean.TRUE;
    }

    @Override
    public void deadLineNotify() {
        // 获取的即将结束任务列表，并设置定时任务
        List<DeadLineNotifyVO> deadLineNotifyVoS = new ArrayList<>();
        List<ProjectTaske> projectTasks;
        LocalDate currentDate = LocalDate.now();
//        UserPmsDTO userPmsDTO = new UserPmsDTO();
//        List<SysUserOutVO> sysUserOutVOList;
//        Map<Long, String> userIdToNameMap;

        projectTasks = projectTaskeMapper.selectList(null);
        projectTasks = projectTasks.stream().filter(p -> YesOrNoEnum.NO.getValue().equals(p.getPermanentFlag())).collect(Collectors.toList());
        Map<Long, List<ProjectTaskeUser>> taskIdToLeaderMap = new HashMap<>(projectTasks.size());
        if (CollUtil.isNotEmpty(projectTasks)) {
            List<ProjectTaskeUser> leaders = projectTaskeUserMapper.findLeadersByTaskIds(projectTasks.stream().map(ProjectTaske::getId).distinct().collect(Collectors.toList()));
            for (ProjectTaskeUser leader : leaders) {
                Long taskId = leader.getTaskId();
                List<ProjectTaskeUser> projectTaskeUsers = taskIdToLeaderMap.get(taskId);
                if (Optional.ofNullable(projectTaskeUsers).isPresent()) {
                    projectTaskeUsers.add(leader);
                } else {
                    projectTaskeUsers = new ArrayList<>();
                    projectTaskeUsers.add(leader);
                    taskIdToLeaderMap.put(taskId, projectTaskeUsers);
                }
            }
        }
        projectTasks.forEach(projectTask -> {
            LocalDate expectEndDate = projectTask.getExpectEndDate();
            if (ProjectTaskStateEnum.NORMAL.getValue().equals(projectTask.getState()) && expectEndDate != null) {
                if (expectEndDate.equals(currentDate)
                        || (expectEndDate.isAfter(currentDate.minusDays(1)) && expectEndDate.minusDays(3).isBefore(currentDate))) {
                    String projectName;
                    DeadLineNotifyVO vo;
                    String title = projectTask.getTitle();
                    Long projectId = projectTask.getProjectId();
                    List<ProjectTaskeUser> projectTaskeUsers = taskIdToLeaderMap.get(projectTask.getId());
                    if (CollUtil.isNotEmpty(projectTaskeUsers)) {
                        for (ProjectTaskeUser leader : projectTaskeUsers) {
                            projectName = projectInfoMapper.selectById(projectId).getItemName();
                            vo = DeadLineNotifyVO.of(projectName, title, leader.getUserId(), leader.getUserName());
                            deadLineNotifyVoS.add(vo);
                        }
                    }
                }
            }
        });

//        sysUserOutVOList = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack().orElse(ImmutableList.of());
//        userIdToNameMap = sysUserOutVOList.stream()
//                .filter(u -> Optional.ofNullable(u).isPresent() && Optional.ofNullable(u.getUsername()).isPresent())
//                .collect(Collectors.toMap(SysUserOutVO::getUserId, SysUserOutVO::getUsername,(k1, k2) -> k1));
        // 获取用户id-name map
        Map<Long, String> userIdToNameMap = rosterMapper.selectList(null)
                .stream().collect(Collectors.toMap(Roster::getId, Roster::getAliasName));
        // 发送企业微信和门户通知（通过中台接口）
        for (DeadLineNotifyVO deadLineNotifyVo : deadLineNotifyVoS) {
            sendMsg(userIdToNameMap, deadLineNotifyVo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoFinish() {
        LambdaQueryWrapper<ProjectInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjectInfo::getProjectStatus, Arrays.asList(ProjectStatusEnum.SJZZ.getValue(), ProjectStatusEnum.JX.getValue(), ProjectStatusEnum.YCZZ.getValue()))
                .eq(ProjectInfo::getAutoFinishFlag, YesOrNoEnum.NO.getValue());
        List<ProjectInfo> projectInfos = projectInfoMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(projectInfos)) {
            List<Long> projectIds = projectInfos.stream().map(ProjectInfo::getId).collect(Collectors.toList());
            List<ProjectTaske> taskes = projectTaskeMapper.findUnfinishedByProjectIds(projectIds);
            if (CollUtil.isNotEmpty(taskes)) {
                List<Long> taskIds = taskes.stream().map(ProjectTaske::getId).collect(Collectors.toList());
                List<DailyPaperEntry> pes = dailyPaperEntryMapper.findByTaskIds(taskIds);
                Map<Long, DailyPaperEntry> collect = new HashMap<>();
                if (CollUtil.isNotEmpty(pes)) {
                    collect = pes.stream().collect(Collectors.toMap(DailyPaperEntry::getTaskId, pe -> pe, (pe1, pe2) -> {
                        if (pe1.getSubmissionDate().isAfter(pe2.getSubmissionDate())) {
                            return pe1;
                        } else {
                            return pe2;
                        }
                    }));
                    List<Long> wsh = pes.stream()
                            .filter(pe -> ApprovalStatusEnum.DSH.getValue().equals(pe.getApprovalStatus()))
                            .map(DailyPaperEntry::getTaskId).distinct()
                            .collect(Collectors.toList());
                    taskes = taskes.stream().filter(t -> CollUtil.isEmpty(wsh) || !wsh.contains(t.getId())).collect(Collectors.toList());
                }
                if (CollUtil.isNotEmpty(taskes)) {
                    for (ProjectTaske taske : taskes) {
                        DailyPaperEntry dailyPaperEntry = collect.get(taske.getId());
                        if (Optional.ofNullable(dailyPaperEntry).isPresent()) {
                            taske.setEndDate(dailyPaperEntry.getSubmissionDate());
                        } else {
                            taske.setEndDate(LocalDate.now());
                        }
                        taske.setState(ProjectTaskStateEnum.FINISHED.getValue());
                        BaseBuildEntityUtil.buildUpdate(taske);
                    }
                    projectTaskeMapper.batchUpdate(taskes);
                }
            }

            // 更新自动结束任务标识
            projectInfoMapper.updateAutoFinishFag(projectIds, YesOrNoEnum.YES.getValue());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncOldTask() {
        syncMemberDeptName();

        syncTasks();

        syncProjectTasks();

        syncStakeholderMember();
    }

    @Override
    public void syncOldTaskFix() {
        List<ProjectTaske> allTask = projectTaskeMapper.selectList(null);

        allTask.forEach(t -> {
            ImmutableList<Long> taskId = ImmutableList.of(t.getId());
            List<ProjectTaskeUser> leaders = projectTaskeUserMapper.findByTaskIdsAndTaskRole(taskId, ProjectTaskRoleEnum.LEADER.getValue());
            List<ProjectTaskeUser> members = projectTaskeUserMapper.findByTaskIdsAndTaskRole(taskId, ProjectTaskRoleEnum.MEMBER.getValue());
            boolean flag = false;

            if (!leaders.isEmpty()) {
                String leaderNames = leaders.stream().map(ProjectTaskeUser::getUserName).collect(Collectors.joining(","));

                t.setLeaderNames(leaderNames);
                flag = true;
            }
            if (!members.isEmpty()) {
                String memberNames = members.stream().map(ProjectTaskeUser::getUserName).collect(Collectors.joining(","));

                t.setMemberNames(memberNames);
                flag = true;
            }
            if (flag) {
                projectTaskeMapper.updateById(t);
            }
        });
    }

    /**
     * 同步参与人部门
     */
    private void syncMemberDeptName() {
        Map<Long, Roster> rosterMap = rosterMapper.findUserIdMap(null);
        List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));

        List<ProjectStakeholderMember> members = projectStakeholderMemberMapper.findToSyncDeptName();
        if (CollUtil.isNotEmpty(members)) {
            members.forEach(m -> {
                if (m.getMemberId() != null) {
                    Roster roster = rosterMap.get(m.getMemberId());
                    if (Optional.ofNullable(roster).isPresent() && roster.getDeptId() != null) {
                        m.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                    }
                } else {
                    members.remove(m);
                }
            });
            if (CollUtil.isNotEmpty(members)) {
                members.forEach(projectStakeholderMemberMapper::updateById);
            }
        }
    }

    /**
     * 同步项目干系人
     */
    private void syncStakeholderMember() {
        Map<Long, Roster> rosterMap = rosterMapper.findUserIdMap(null);
        List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));

        List<ProjectStakeholderMember> members = taskMapper.findToSyncMember();
        if (CollUtil.isNotEmpty(members)) {
            members.forEach(m -> {
                if (m.getMemberId() != null) {
                    Roster roster = rosterMap.get(m.getMemberId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        m.setPosition(roster.getJob());
                        if (roster.getDeptId() != null) {
                            m.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(m);
                    projectStakeholderMemberMapper.insert(m);
                }
            });
        }

        List<ProjectStakeholderMember> members2 = projectTaskMapper.findToSyncMember();
        if (CollUtil.isNotEmpty(members2)) {
            members2.forEach(m -> {
                if (m.getMemberId() != null) {
                    Roster roster = rosterMap.get(m.getMemberId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        m.setPosition(roster.getJob());
                        if (roster.getDeptId() != null) {
                            m.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(m);
                    projectStakeholderMemberMapper.insert(m);
                }
            });
        }

        List<ProjectStakeholderMember> members3 = projectInfoMapper.findToSyncMember();
        if (CollUtil.isNotEmpty(members3)) {
            members3.forEach(m -> {
                if (m.getMemberId() != null) {
                    Roster roster = rosterMap.get(m.getMemberId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        m.setPosition(roster.getJob());
                        if (roster.getDeptId() != null) {
                            m.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(m);
                    projectStakeholderMemberMapper.insert(m);
                }
            });
        }
        List<ProjectStakeholderMember> members4 = projectInfoMapper.findToSyncMember2();
        if (CollUtil.isNotEmpty(members4)) {
            members4.forEach(m -> {
                if (m.getMemberId() != null) {
                    Roster roster = rosterMap.get(m.getMemberId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        m.setPosition(roster.getJob());
                        if (roster.getDeptId() != null) {
                            m.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(m);
                    projectStakeholderMemberMapper.insert(m);
                }
            });
        }
        List<ProjectStakeholderMember> members5 = projectInfoMapper.findToSyncMember3();
        if (CollUtil.isNotEmpty(members5)) {
            members5.forEach(m -> {
                if (m.getMemberId() != null) {
                    Roster roster = rosterMap.get(m.getMemberId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        m.setPosition(roster.getJob());
                        if (roster.getDeptId() != null) {
                            m.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(m);
                    projectStakeholderMemberMapper.insert(m);
                }
            });
        }

        List<ProjectStakeholderMember> members6 = privilegeMapper.findToSyncMember();
        if (CollUtil.isNotEmpty(members6)) {
            members6.forEach(m -> {
                if (m.getMemberId() != null) {
                    Roster roster = rosterMap.get(m.getMemberId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        m.setPosition(roster.getJob());
                        if (roster.getDeptId() != null) {
                            m.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(m);
                    projectStakeholderMemberMapper.insert(m);
                }
            });
        }
    }

    /**
     * 同步项目任务
     */
    private void syncProjectTasks() {
        List<ProjectTask> allProjectTasks = projectTaskMapper.findAll();
        List<ProjectTask> gl = new ArrayList<>();
        List<ProjectTask> jf = new ArrayList<>();
        // 0交付 1管理
        for (ProjectTask task : allProjectTasks) {
            Long projectId = task.getProjectId();
            ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
            if (projectInfo != null) {
                if (projectInfo.getIsNotInternalProject() == null || projectInfo.getIsNotInternalProject() != 1) {
                    jf.add(task);
                } else {
                    gl.add(task);
                }
            }
        }
        Map<Long, Roster> rosterMap = rosterMapper.findUserIdMap(null);
        List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));
        if (CollUtil.isNotEmpty(jf)) {

            jf.forEach(t -> {
                String leaderNames = CharSequenceUtil.EMPTY;
                List<ProjectTaskeUser> leaders = new ArrayList<>();
                if (Optional.ofNullable(t.getManagerUserId()).isPresent()) {
                    leaderNames = t.getManagerUserName();
                    ProjectTaskeUser leader = new ProjectTaskeUser();
                    leader.setUserId(t.getManagerUserId());
                    leader.setUserName(t.getManagerUserName());
                    leader.setTaskRole(0);
                    leaders.add(leader);
                }
                String memberNames = CharSequenceUtil.EMPTY;
                List<ProjectTaskUser> taskUsers = projectTaskUserMapper.findByTaskId(t.getId());
                if (CollUtil.isNotEmpty(taskUsers)) {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    taskUsers.forEach(taskUser -> stringJoiner.add(taskUser.getUserName()));
                    memberNames = stringJoiner.toString();
                }

                ProjectTaske sq = new ProjectTaske();
                sq.setProjectId(t.getProjectId());
                sq.setTitle(t.getTitle());
                sq.setKind(0);
                if (t.getState() == 2) {
                    sq.setState(1);
                } else {
                    sq.setState(0);
                }
                sq.setPermanentFlag(0);
                sq.setExpectStartDate(t.getExpectedStartTime().toLocalDate());
                sq.setExpectEndDate(t.getExpectedEndTime().toLocalDate());
                if (Optional.ofNullable(t.getActualStartTime()).isPresent()) {
                    sq.setStartDate(t.getActualStartTime().toLocalDate());
                }
                if (Optional.ofNullable(t.getActualEndTime()).isPresent()) {
                    sq.setEndDate(t.getActualEndTime().toLocalDate());
                }
                sq.setLeaderNames(leaderNames);
                sq.setMemberNames(memberNames);
                sq.setCreator(t.getCreator());
                sq.setCreatorId(t.getCreatorId());
                sq.setModifier(t.getModifier());
                sq.setModifierId(t.getModifierId());
                sq.setCtime(t.getCtime());
                sq.setMtime(t.getMtime());
                sq.setDelFlag(0);
                BaseBuildEntityUtil.buildInsert(sq);
                projectTaskeMapper.insert(sq);
                taskUsers.forEach(tu -> {
                    ProjectTaskeUser squ = new ProjectTaskeUser();
                    squ.setTaskId(sq.getId());
                    squ.setUserId(tu.getUserId());
                    squ.setUserName(tu.getUserName());
                    squ.setTaskRole(1);
                    Roster roster = rosterMap.get(tu.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        squ.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            squ.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(squ);
                    squ.setCreator(tu.getCreator());
                    squ.setCreatorId(tu.getCreatorId());
                    squ.setModifier(tu.getModifier());
                    squ.setModifierId(tu.getModifierId());
                    squ.setCtime(tu.getCtime());
                    squ.setMtime(tu.getMtime());
                    squ.setDelFlag(0);
                    projectTaskeUserMapper.insert(squ);
                });
                leaders.forEach(l -> {
                    l.setTaskId(sq.getId());
                    Roster roster = rosterMap.get(l.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        l.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            l.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(l);
                    projectTaskeUserMapper.insert(l);
                });

                ProjectTaske sh = new ProjectTaske();
                sh.setProjectId(t.getProjectId());
                sh.setTitle(t.getTitle());
                sh.setKind(1);
                if (t.getState() == 2) {
                    sh.setState(1);
                } else {
                    sh.setState(0);
                }
                sh.setPermanentFlag(0);
                sh.setExpectStartDate(t.getExpectedStartTime().toLocalDate());
                sh.setExpectEndDate(t.getExpectedEndTime().toLocalDate());
                if (Optional.ofNullable(t.getActualStartTime()).isPresent()) {
                    sh.setStartDate(t.getActualStartTime().toLocalDate());
                }
                if (Optional.ofNullable(t.getActualEndTime()).isPresent()) {
                    sh.setEndDate(t.getActualEndTime().toLocalDate());
                }
                sh.setLeaderNames(leaderNames);
                sh.setMemberNames(memberNames);
                sh.setCreator(t.getCreator());
                sh.setCreatorId(t.getCreatorId());
                sh.setModifier(t.getModifier());
                sh.setModifierId(t.getModifierId());
                sh.setCtime(t.getCtime());
                sh.setMtime(t.getMtime());
                sh.setDelFlag(0);
                BaseBuildEntityUtil.buildInsert(sh);
                projectTaskeMapper.insert(sh);
                taskUsers.forEach(tu -> {
                    ProjectTaskeUser shu = new ProjectTaskeUser();
                    shu.setTaskId(sh.getId());
                    shu.setUserId(tu.getUserId());
                    shu.setUserName(tu.getUserName());
                    shu.setTaskRole(1);
                    Roster roster = rosterMap.get(tu.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        shu.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            shu.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(shu);
                    shu.setCreator(tu.getCreator());
                    shu.setCreatorId(tu.getCreatorId());
                    shu.setModifier(tu.getModifier());
                    shu.setModifierId(tu.getModifierId());
                    shu.setCtime(tu.getCtime());
                    shu.setMtime(tu.getMtime());
                    shu.setDelFlag(0);
                    projectTaskeUserMapper.insert(shu);
                });
                leaders.forEach(l -> {
                    l.setTaskId(sh.getId());
                    Roster roster = rosterMap.get(l.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        l.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            l.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(l);
                    projectTaskeUserMapper.insert(l);
                });

                List<DailyPaperEntry> pes = dailyPaperEntryMapper.findByTaskIds(Collections.singletonList(t.getId()));
                if (CollUtil.isNotEmpty(pes)) {
                    pes.forEach(pe -> {
                        if (pe.getWorkType() == null) {
                            pe.setTaskId(sh.getId());
                        } else {
                            if (pe.getWorkType() == 0) {
                                pe.setTaskId(sq.getId());
                            } else {
                                pe.setTaskId(sh.getId());
                            }
                        }

                        dailyPaperEntryMapper.updateById(pe);
                    });
                }
            });
        }
        if (CollUtil.isNotEmpty(gl)) {

            gl.forEach(t -> {
                String leaderNames = CharSequenceUtil.EMPTY;
                List<ProjectTaskeUser> leaders = new ArrayList<>();
                if (Optional.ofNullable(t.getManagerUserId()).isPresent()) {
                    leaderNames = t.getManagerUserName();
                    ProjectTaskeUser leader = new ProjectTaskeUser();
                    leader.setUserId(t.getManagerUserId());
                    leader.setUserName(t.getManagerUserName());
                    leader.setTaskRole(0);
                    leaders.add(leader);
                }
                String memberNames = CharSequenceUtil.EMPTY;
                List<ProjectTaskUser> taskUsers = projectTaskUserMapper.findByTaskId(t.getId());
                if (CollUtil.isNotEmpty(taskUsers)) {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    taskUsers.forEach(taskUser -> stringJoiner.add(taskUser.getUserName()));
                    memberNames = stringJoiner.toString();
                }

                ProjectTaske sq = new ProjectTaske();
                sq.setProjectId(t.getProjectId());
                sq.setTitle(t.getTitle());
                sq.setKind(null);
                if (t.getState() == 2) {
                    sq.setState(1);
                } else {
                    sq.setState(0);
                }
                sq.setPermanentFlag(0);
                sq.setExpectStartDate(t.getExpectedStartTime().toLocalDate());
                sq.setExpectEndDate(t.getExpectedEndTime().toLocalDate());
                if (Optional.ofNullable(t.getActualStartTime()).isPresent()) {
                    sq.setStartDate(t.getActualStartTime().toLocalDate());
                }
                if (Optional.ofNullable(t.getActualEndTime()).isPresent()) {
                    sq.setEndDate(t.getActualEndTime().toLocalDate());
                }
                sq.setLeaderNames(leaderNames);
                sq.setMemberNames(memberNames);
                sq.setCreator(t.getCreator());
                sq.setCreatorId(t.getCreatorId());
                sq.setModifier(t.getModifier());
                sq.setModifierId(t.getModifierId());
                sq.setCtime(t.getCtime());
                sq.setMtime(t.getMtime());
                sq.setDelFlag(0);
                BaseBuildEntityUtil.buildInsert(sq);
                projectTaskeMapper.insert(sq);
                taskUsers.forEach(tu -> {
                    ProjectTaskeUser squ = new ProjectTaskeUser();
                    squ.setTaskId(sq.getId());
                    squ.setUserId(tu.getUserId());
                    squ.setUserName(tu.getUserName());
                    squ.setTaskRole(1);
                    Roster roster = rosterMap.get(tu.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        squ.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            squ.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(squ);
                    squ.setCreator(tu.getCreator());
                    squ.setCreatorId(tu.getCreatorId());
                    squ.setModifier(tu.getModifier());
                    squ.setModifierId(tu.getModifierId());
                    squ.setCtime(tu.getCtime());
                    squ.setMtime(tu.getMtime());
                    squ.setDelFlag(0);
                    projectTaskeUserMapper.insert(squ);
                });
                leaders.forEach(l -> {
                    l.setTaskId(sq.getId());
                    Roster roster = rosterMap.get(l.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        l.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            l.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(l);
                    projectTaskeUserMapper.insert(l);
                });

                List<DailyPaperEntry> pes = dailyPaperEntryMapper.findByTaskIds(Collections.singletonList(t.getId()));
                if (CollUtil.isNotEmpty(pes)) {
                    pes.forEach(pe -> {
                        pe.setTaskId(sq.getId());
                        dailyPaperEntryMapper.updateById(pe);
                    });
                }
            });
        }
    }

    /**
     * 同步子任务
     */
    private void syncTasks() {
        List<Task> allTask = taskMapper.findAll();
        List<Task> gl = new ArrayList<>();
        List<Task> jf = new ArrayList<>();
        // 0交付 1管理
        for (Task task : allTask) {
            Long projectId = task.getProjectId();
            ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
            if (projectInfo != null) {
                if (projectInfo.getIsNotInternalProject() == null || projectInfo.getIsNotInternalProject() != 1) {
                    jf.add(task);
                } else {
                    gl.add(task);
                }
            }
        }
        Map<Long, Roster> rosterMap = rosterMapper.findUserIdMap(null);
        List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));
        if (CollUtil.isNotEmpty(jf)) {

            jf.forEach(t -> {
                Long projectId = t.getProjectId();
                ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
                String leaderNames = CharSequenceUtil.EMPTY;
                List<ProjectTaskeUser> leaders = new ArrayList<>();
                if (Objects.equals(projectInfo.getProjectStatus(), "0") || Objects.equals(projectInfo.getProjectStatus(), "1")) {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    if (Optional.ofNullable(projectInfo.getSalesmanUserId()).isPresent()) {
                        stringJoiner.add(projectInfo.getProjectSalesperson());
                        ProjectTaskeUser leader = new ProjectTaskeUser();
                        leader.setUserId(projectInfo.getSalesmanUserId());
                        leader.setUserName(projectInfo.getProjectSalesperson());
                        leader.setTaskRole(0);
                        leaders.add(leader);
                    }
                    if (Optional.ofNullable(projectInfo.getPreSaleUserId()).isPresent()) {
                        stringJoiner.add(projectInfo.getPreSaleUserName());
                        ProjectTaskeUser leader = new ProjectTaskeUser();
                        leader.setUserId(projectInfo.getPreSaleUserId());
                        leader.setUserName(projectInfo.getPreSaleUserName());
                        leader.setTaskRole(0);
                        leaders.add(leader);
                    }
                    leaderNames = stringJoiner.toString();
                } else {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    if (Optional.ofNullable(projectInfo.getManagerUserId()).isPresent()) {
                        stringJoiner.add(projectInfo.getManagerUserName());
                        ProjectTaskeUser leader = new ProjectTaskeUser();
                        leader.setUserId(projectInfo.getManagerUserId());
                        leader.setUserName(projectInfo.getManagerUserName());
                        leaderNames = stringJoiner.toString();
                        leader.setTaskRole(0);
                        leaders.add(leader);
                    }
                }
                String memberNames = CharSequenceUtil.EMPTY;
                List<TaskUser> taskUsers = taskUserMapper.findByTaskId(t.getId());
                if (CollUtil.isNotEmpty(taskUsers)) {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    taskUsers.forEach(taskUser -> stringJoiner.add(taskUser.getUserName()));
                    memberNames = stringJoiner.toString();
                }

                ProjectTaske sq = new ProjectTaske();
                sq.setProjectId(t.getProjectId());
                sq.setTitle(t.getTaskName());
                sq.setKind(0);
                sq.setState(t.getTaskStatus());
                sq.setPermanentFlag(1);
                sq.setStartDate(dailyPaperEntryMapper.findFirstSqByTaskId(t.getId()));
                if (t.getTaskStatus() == 1 && Optional.ofNullable(t.getMtime()).isPresent()) {
                    sq.setEndDate(t.getMtime().toLocalDateTime().toLocalDate());
                }
                sq.setLeaderNames(leaderNames);
                sq.setMemberNames(memberNames);
                sq.setCreator(t.getCreator());
                sq.setCreatorId(t.getCreatorId());
                sq.setModifier(t.getModifier());
                sq.setModifierId(t.getModifierId());
                sq.setCtime(t.getCtime());
                sq.setMtime(t.getMtime());
                sq.setDelFlag(0);
                BaseBuildEntityUtil.buildInsert(sq);
                projectTaskeMapper.insert(sq);
                taskUsers.forEach(tu -> {
                    ProjectTaskeUser squ = new ProjectTaskeUser();
                    squ.setTaskId(sq.getId());
                    squ.setUserId(tu.getUserId());
                    squ.setUserName(tu.getUserName());
                    squ.setTaskRole(1);
                    Roster roster = rosterMap.get(tu.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        squ.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            squ.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(squ);
                    squ.setCreator(tu.getCreator());
                    squ.setCreatorId(tu.getCreatorId());
                    squ.setModifier(tu.getModifier());
                    squ.setModifierId(tu.getModifierId());
                    squ.setCtime(tu.getCtime());
                    squ.setMtime(tu.getMtime());
                    squ.setDelFlag(0);
                    projectTaskeUserMapper.insert(squ);
                });
                leaders.forEach(l -> {
                    l.setTaskId(sq.getId());
                    Roster roster = rosterMap.get(l.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        l.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            l.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(l);
                    projectTaskeUserMapper.insert(l);
                });

                ProjectTaske sh = new ProjectTaske();
                sh.setProjectId(t.getProjectId());
                sh.setTitle(t.getTaskName());
                sh.setKind(1);
                sh.setState(t.getTaskStatus());
                sh.setPermanentFlag(1);
                sh.setStartDate(dailyPaperEntryMapper.findFirstShByTaskId(t.getId()));
                if (t.getTaskStatus() == 1 && Optional.ofNullable(t.getMtime()).isPresent()) {
                    sh.setEndDate(t.getMtime().toLocalDateTime().toLocalDate());
                }
                sh.setLeaderNames(leaderNames);
                sh.setMemberNames(memberNames);
                sh.setCreator(t.getCreator());
                sh.setCreatorId(t.getCreatorId());
                sh.setModifier(t.getModifier());
                sh.setModifierId(t.getModifierId());
                sh.setCtime(t.getCtime());
                sh.setMtime(t.getMtime());
                sh.setDelFlag(0);
                BaseBuildEntityUtil.buildInsert(sh);
                projectTaskeMapper.insert(sh);
                taskUsers.forEach(tu -> {
                    ProjectTaskeUser shu = new ProjectTaskeUser();
                    shu.setTaskId(sh.getId());
                    shu.setUserId(tu.getUserId());
                    shu.setUserName(tu.getUserName());
                    shu.setTaskRole(1);
                    Roster roster = rosterMap.get(tu.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        shu.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            shu.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(shu);
                    shu.setCreator(tu.getCreator());
                    shu.setCreatorId(tu.getCreatorId());
                    shu.setModifier(tu.getModifier());
                    shu.setModifierId(tu.getModifierId());
                    shu.setCtime(tu.getCtime());
                    shu.setMtime(tu.getMtime());
                    shu.setDelFlag(0);
                    projectTaskeUserMapper.insert(shu);
                });
                leaders.forEach(l -> {
                    l.setTaskId(sh.getId());
                    Roster roster = rosterMap.get(l.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        l.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            l.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(l);
                    projectTaskeUserMapper.insert(l);
                });

                List<DailyPaperEntry> pes = dailyPaperEntryMapper.findByTaskIds(Collections.singletonList(t.getId()));
                if (CollUtil.isNotEmpty(pes)) {
                    pes.forEach(pe -> {
                        if (pe.getWorkType() == null) {
                            pe.setTaskId(sh.getId());
                        } else {
                            if (pe.getWorkType() == 0) {
                                pe.setTaskId(sq.getId());
                            } else {
                                pe.setTaskId(sh.getId());
                            }
                        }

                        dailyPaperEntryMapper.updateById(pe);
                    });
                }
            });
        }
        if (CollUtil.isNotEmpty(gl)) {

            gl.forEach(t -> {
                Long projectId = t.getProjectId();
                ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
                String leaderNames = CharSequenceUtil.EMPTY;
                List<ProjectTaskeUser> leaders = new ArrayList<>();
                if (Objects.equals(projectInfo.getProjectStatus(), "0") || Objects.equals(projectInfo.getProjectStatus(), "1")) {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    if (Optional.ofNullable(projectInfo.getSalesmanUserId()).isPresent()) {
                        stringJoiner.add(projectInfo.getProjectSalesperson());
                        ProjectTaskeUser leader = new ProjectTaskeUser();
                        leader.setUserId(projectInfo.getSalesmanUserId());
                        leader.setUserName(projectInfo.getProjectSalesperson());
                        leader.setTaskRole(0);
                        leaders.add(leader);
                    }
                    if (Optional.ofNullable(projectInfo.getPreSaleUserId()).isPresent()) {
                        stringJoiner.add(projectInfo.getPreSaleUserName());
                        ProjectTaskeUser leader = new ProjectTaskeUser();
                        leader.setUserId(projectInfo.getPreSaleUserId());
                        leader.setUserName(projectInfo.getPreSaleUserName());
                        leader.setTaskRole(0);
                        leaders.add(leader);
                    }
                    leaderNames = stringJoiner.toString();
                } else {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    if (Optional.ofNullable(projectInfo.getManagerUserId()).isPresent()) {
                        stringJoiner.add(projectInfo.getManagerUserName());
                        ProjectTaskeUser leader = new ProjectTaskeUser();
                        leader.setUserId(projectInfo.getManagerUserId());
                        leader.setUserName(projectInfo.getManagerUserName());
                        leaderNames = stringJoiner.toString();
                        leader.setTaskRole(0);
                        leaders.add(leader);
                    }
                }
                String memberNames = CharSequenceUtil.EMPTY;
                List<TaskUser> taskUsers = taskUserMapper.findByTaskId(t.getId());
                if (CollUtil.isNotEmpty(taskUsers)) {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    taskUsers.forEach(taskUser -> stringJoiner.add(taskUser.getUserName()));
                    memberNames = stringJoiner.toString();
                }

                ProjectTaske sq = new ProjectTaske();
                sq.setProjectId(t.getProjectId());
                sq.setTitle(t.getTaskName());
                sq.setKind(null);
                sq.setState(t.getTaskStatus());
                sq.setPermanentFlag(1);
                sq.setStartDate(dailyPaperEntryMapper.findFirstDailyDateByTaskId(t.getId()));
                if (t.getTaskStatus() == 1 && Optional.ofNullable(t.getMtime()).isPresent()) {
                    sq.setEndDate(t.getMtime().toLocalDateTime().toLocalDate());
                }
                sq.setLeaderNames(leaderNames);
                sq.setMemberNames(memberNames);
                sq.setCreator(t.getCreator());
                sq.setCreatorId(t.getCreatorId());
                sq.setModifier(t.getModifier());
                sq.setModifierId(t.getModifierId());
                sq.setCtime(t.getCtime());
                sq.setMtime(t.getMtime());
                sq.setDelFlag(0);
                BaseBuildEntityUtil.buildInsert(sq);
                projectTaskeMapper.insert(sq);
                taskUsers.forEach(tu -> {
                    ProjectTaskeUser squ = new ProjectTaskeUser();
                    squ.setTaskId(sq.getId());
                    squ.setUserId(tu.getUserId());
                    squ.setUserName(tu.getUserName());
                    squ.setTaskRole(1);
                    Roster roster = rosterMap.get(tu.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        squ.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            squ.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(squ);
                    squ.setCreator(tu.getCreator());
                    squ.setCreatorId(tu.getCreatorId());
                    squ.setModifier(tu.getModifier());
                    squ.setModifierId(tu.getModifierId());
                    squ.setCtime(tu.getCtime());
                    squ.setMtime(tu.getMtime());
                    squ.setDelFlag(0);
                    projectTaskeUserMapper.insert(squ);
                });
                leaders.forEach(l -> {
                    l.setTaskId(sq.getId());
                    Roster roster = rosterMap.get(l.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        l.setPosition(roster.getJob());
                        if (Optional.ofNullable(roster.getDeptId()).isPresent()) {
                            l.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        }
                    }
                    BaseBuildEntityUtil.buildInsert(l);
                    projectTaskeUserMapper.insert(l);
                });

                List<DailyPaperEntry> pes = dailyPaperEntryMapper.findByTaskIds(Collections.singletonList(t.getId()));
                if (CollUtil.isNotEmpty(pes)) {
                    pes.forEach(pe -> {
                        pe.setTaskId(sq.getId());
                        dailyPaperEntryMapper.updateById(pe);
                    });
                }
            });
        }
    }

    /**
     * 构建管理项目所需任务
     *
     * @param projectTaskes     任务列表
     * @param projectTaskeUsers 任务-用户列表
     * @param projectInfoMap    项目id-项目 map
     * @param pId               项目id
     */
    private static void buildManageProjectTask(List<ProjectTaske> projectTaskes, List<ProjectTaskeUser> projectTaskeUsers, Map<Long, ProjectInfo> projectInfoMap, Long pId) {
        ProjectInfo projectInfo = projectInfoMap.get(pId);
        Long managerUserId = projectInfo.getManagerUserId();
        String managerUserName = projectInfo.getManagerUserName();
        Long preSaleUserId = projectInfo.getPreSaleUserId();
        String preSaleUserName = projectInfo.getPreSaleUserName();
        Long salesmanUserId = projectInfo.getSalesmanUserId();
        String projectSalesperson = projectInfo.getProjectSalesperson();
        StringJoiner leaderNames = new StringJoiner(StrUtil.COMMA);

        ProjectTaske projectTaske = new ProjectTaske();
        projectTaske.setProjectId(pId);
        projectTaske.setTitle("默认任务");
        projectTaske.setKind(null);
        projectTaske.setState(ProjectTaskStateEnum.NORMAL.getValue());
        projectTaske.setPermanentFlag(YesOrNoEnum.YES.getValue());
        timedTaskBuildSave(projectTaske);

        buildDefaultTaskAndLeaders(projectTaskeUsers, managerUserId, managerUserName, leaderNames, projectTaske);
        buildDefaultTaskAndLeaders(projectTaskeUsers, preSaleUserId, preSaleUserName, leaderNames, projectTaske);
        buildDefaultTaskAndLeaders(projectTaskeUsers, salesmanUserId, projectSalesperson, leaderNames, projectTaske);
        if (CharSequenceUtil.isNotBlank(leaderNames.toString())) {
            projectTaske.setLeaderNames(Arrays.stream(leaderNames.toString().split(",")).distinct().collect(Collectors.joining(",")));
        }
        projectTaskes.add(projectTaske);
    }

    /**
     * 构建交付项目所需任务
     *
     * @param projectTaskes     任务列表
     * @param projectTaskeUsers 任务-用户列表
     * @param projectInfoMap    项目id-项目 map
     * @param pId               项目id
     */
    private static void buildDeliverProjectTask(List<ProjectTaske> projectTaskes, List<ProjectTaskeUser> projectTaskeUsers, Map<Long, ProjectInfo> projectInfoMap, Long pId) {
        ProjectInfo projectInfo = projectInfoMap.get(pId);
        Long managerUserId = projectInfo.getManagerUserId();
        String managerUserName = projectInfo.getManagerUserName();
        Long preSaleUserId = projectInfo.getPreSaleUserId();
        String preSaleUserName = projectInfo.getPreSaleUserName();
        Long salesmanUserId = projectInfo.getSalesmanUserId();
        String projectSalesperson = projectInfo.getProjectSalesperson();
        StringJoiner leaderNames = new StringJoiner(StrUtil.COMMA);
        StringJoiner leaderNames2 = new StringJoiner(StrUtil.COMMA);

        ProjectTaske projectTaske = new ProjectTaske();
        projectTaske.setProjectId(pId);
        projectTaske.setTitle("默认售前支撑任务");
        projectTaske.setKind(ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue());
        projectTaske.setState(ProjectTaskStateEnum.NORMAL.getValue());
        projectTaske.setPermanentFlag(YesOrNoEnum.YES.getValue());
        timedTaskBuildSave(projectTaske);

        ProjectTaske projectTaske2 = new ProjectTaske();
        projectTaske2.setProjectId(pId);
        projectTaske2.setTitle("默认售后交付任务");
        projectTaske2.setKind(ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue());
        projectTaske2.setState(ProjectTaskStateEnum.NORMAL.getValue());
        projectTaske2.setPermanentFlag(YesOrNoEnum.YES.getValue());
        timedTaskBuildSave(projectTaske2);

        buildDefaultTaskAndLeaders(projectTaskeUsers, managerUserId, managerUserName, leaderNames, projectTaske);
        buildDefaultTaskAndLeaders(projectTaskeUsers, preSaleUserId, preSaleUserName, leaderNames, projectTaske);
        buildDefaultTaskAndLeaders(projectTaskeUsers, salesmanUserId, projectSalesperson, leaderNames, projectTaske);
        buildDefaultTaskAndLeaders(projectTaskeUsers, managerUserId, managerUserName, leaderNames2, projectTaske2);
        buildDefaultTaskAndLeaders(projectTaskeUsers, preSaleUserId, preSaleUserName, leaderNames2, projectTaske2);
        buildDefaultTaskAndLeaders(projectTaskeUsers, salesmanUserId, projectSalesperson, leaderNames2, projectTaske2);
        if (CharSequenceUtil.isNotBlank(leaderNames.toString())) {
            projectTaske.setLeaderNames(Arrays.stream(leaderNames.toString().split(",")).distinct().collect(Collectors.joining(",")));
        }
        if (CharSequenceUtil.isNotBlank(leaderNames2.toString())) {
            projectTaske2.setLeaderNames(Arrays.stream(leaderNames2.toString().split(",")).distinct().collect(Collectors.joining(",")));
        }
        projectTaskes.add(projectTaske);
        projectTaskes.add(projectTaske2);
    }

    /**
     * 构建默认任务及负责人
     *
     * @param projectTaskeUsers 任务-用户列表
     * @param managerUserId     铁三角id
     * @param managerUserName   铁三角名字
     * @param leaderNames       负责人名字
     * @param projectTaske      任务
     */
    private static void buildDefaultTaskAndLeaders(List<ProjectTaskeUser> projectTaskeUsers, Long managerUserId, String managerUserName, StringJoiner leaderNames, ProjectTaske projectTaske) {
        if (Optional.ofNullable(managerUserId).isPresent() && CharSequenceUtil.isNotBlank(managerUserName)) {
            leaderNames.add(managerUserName);

            ProjectTaskeUser projectTaskeUser = new ProjectTaskeUser();
            projectTaskeUser.setTaskId(projectTaske.getId());
            projectTaskeUser.setUserId(managerUserId);
            projectTaskeUser.setUserName(managerUserName);
            projectTaskeUser.setTaskRole(ProjectTaskRoleEnum.LEADER.getValue());
            timedTaskBuildSave(projectTaskeUser);
            projectTaskeUsers.add(projectTaskeUser);
        }
    }

    /**
     * 定时任务构建实体类-保存
     *
     * @param entity 实体类
     */
    private static <T extends BeanEntity<Long>> void timedTaskBuildSave(T entity) {
        entity.setId(IdWorker.getId());
        entity.setCreator("定时任务");
        entity.setCreatorId(null);
        entity.setCtime(new Timestamp(System.currentTimeMillis()));
        entity.setDelFlag(BaseConstants.NO);
    }

    /**
     * 同步任务成员到项目
     *
     * @param projectId 项目id
     * @param members   任务成员
     */
    private void syncMemberToProject(Long projectId, List<ProjectTaskeUser> members) {
        if (projectId == null || CollUtil.isEmpty(members)) {
            return;
        }
        List<Long> existsUserIds = new ArrayList<>();
        List<Long> newUserIds = new ArrayList<>();
        List<Long> addUserIds = null;
        Map<Long, ProjectTaskeUser> newUserMap = new HashMap<>();
        Map<Long, Roster> rosterMap;
        List<ProjectStakeholderMemberVO> projectMembers = projectStakeholderMemberMapper.getMembersByProjectId(projectId);
        // 设置已存在、新添加干系人map
        if (CollUtil.isNotEmpty(projectMembers)) {
            existsUserIds = projectMembers.stream().map(ProjectStakeholderMemberVO::getMemberId).distinct().collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(members)) {
            newUserIds = members.stream().map(ProjectTaskeUser::getUserId).distinct().collect(Collectors.toList());
            newUserMap = members.stream().collect(Collectors.toMap(ProjectTaskeUser::getUserId, p -> p, (a, b) -> b));
        }
        if (CollUtil.isNotEmpty(existsUserIds) && CollUtil.isNotEmpty(newUserIds)) {
            addUserIds = (List<Long>) CollUtil.subtract(newUserIds, existsUserIds);
        } else if (CollUtil.isNotEmpty(newUserIds)) {
            addUserIds = newUserIds;
        }
        if (CollUtil.isNotEmpty(addUserIds)) {
            // 获取新增干系人相关信息
            rosterMap = rosterMapper.findUserIdMap(addUserIds);
            List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
            Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));

            assert addUserIds != null;
            List<ProjectStakeholderMember> requests = new ArrayList<>();
            for (Long addUserId : addUserIds) {
                ProjectTaskeUser userDto = newUserMap.get(addUserId);
                Roster roster = rosterMap.get(addUserId);
                // 组装项目干系人
                if (Optional.ofNullable(userDto).isPresent() && Optional.ofNullable(roster).isPresent()) {
                    ProjectStakeholderMember request = new ProjectStakeholderMember();
                    request.setProjectId(projectId);
                    request.setMemberId(addUserId);
                    request.setMemberName(userDto.getUserName());
                    request.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                    request.setPosition(roster.getJob());
                    request.setRoleType(RoleTypeEnum.PROJECT_MEMBER.getValue());
                    request.setSyncOaType(YesOrNoEnum.NO.getValue());
                    BaseBuildEntityUtil.buildSave(request);
                    requests.add(request);
                }
            }
            if (CollUtil.isNotEmpty(requests)) {
                projectStakeholderMemberMapper.batchSync(requests);
            }
        }

        // 成员同步OA
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                projectStakeholderMemberService.asyncOa(projectId);
            }
        });
    }

    /**
     * 设置任务状态、起止时间跨度
     *
     * @param projectTaskeVo 项目任务vo
     */
    private void setTaskStateAndExpectDuration(ProjectTaskeVo projectTaskeVo) {
        LocalDate endDate = projectTaskeVo.getEndDate();
        LocalDate expectEndDate = projectTaskeVo.getExpectEndDate();
        // 判断是否是长期任务，长期任务无计划期限
        if (YesOrNoEnum.YES.getValue().equals(projectTaskeVo.getPermanentFlag())) {
            projectTaskeVo.setState(ProjectTaskStateEnum.NORMAL.getValue().equals(projectTaskeVo.getState()) ? TodayTaskStateEnum.NORMAL.getValue() : TodayTaskStateEnum.FINISHED.getValue());
        } else {
            if (ProjectTaskStateEnum.NORMAL.getValue().equals(projectTaskeVo.getState())) {
                projectTaskeVo.setState(LocalDate.now().isAfter(expectEndDate) ? TodayTaskStateEnum.DELAY.getValue() : TodayTaskStateEnum.NORMAL.getValue());
            } else {
                projectTaskeVo.setState(endDate.isAfter(expectEndDate) ? TodayTaskStateEnum.DELAY_FINISHED.getValue() : TodayTaskStateEnum.FINISHED.getValue());
            }
        }
        projectTaskeVo.setStateTxt(EnumUtils.getNameByValue(TodayTaskStateEnum.class, projectTaskeVo.getState()));
    }

    /**
     * 设置任务状态、起止时间跨度
     *
     * @param projectTaskeVo 我的任务vo
     */
    private void setTaskStateAndExpectDuration(MyTaskVo projectTaskeVo) {
        LocalDate endDate = projectTaskeVo.getEndDate();
        LocalDate expectEndDate = projectTaskeVo.getExpectEndDate();
        // 判断是否是长期任务，长期任务无计划期限
        if (YesOrNoEnum.YES.getValue().equals(projectTaskeVo.getPermanentFlag())) {
            projectTaskeVo.setState(ProjectTaskStateEnum.NORMAL.getValue().equals(projectTaskeVo.getState()) ? TodayTaskStateEnum.NORMAL.getValue() : TodayTaskStateEnum.FINISHED.getValue());
        } else {
            if (ProjectTaskStateEnum.NORMAL.getValue().equals(projectTaskeVo.getState())) {
                projectTaskeVo.setState(LocalDate.now().isAfter(expectEndDate) ? TodayTaskStateEnum.DELAY.getValue() : TodayTaskStateEnum.NORMAL.getValue());
            } else {
                projectTaskeVo.setState(endDate.isAfter(expectEndDate) ? TodayTaskStateEnum.DELAY_FINISHED.getValue() : TodayTaskStateEnum.FINISHED.getValue());
            }
            projectTaskeVo.setExpectDate(LocalDateTimeUtil.format(projectTaskeVo.getExpectStartDate(), DatePattern.NORM_DATE_PATTERN)
                    + "-"
                    + LocalDateTimeUtil.format(projectTaskeVo.getExpectEndDate(), DatePattern.NORM_DATE_PATTERN));
        }
        projectTaskeVo.setStateTxt(EnumUtils.getNameByValue(TodayTaskStateEnum.class, projectTaskeVo.getState()));
    }

    /**
     * 设置任务进度
     *
     * @param projectTaskeVo 项目任务vo
     * @param ganttVos       甘特图任务列表
     */
    private void setTaskProcess(ProjectTaskeVo projectTaskeVo, List<ProjectTaskeGanttVo> ganttVos) {
        ProjectTaskeGanttVo ganttVo = new ProjectTaskeGanttVo();
        BeanUtil.copyProperties(projectTaskeVo, ganttVo);
        ganttVo.setRender(GANTT_SPLIT);
        ganttVos.add(ganttVo);

        setGanttChildTask(projectTaskeVo, ganttVos, ganttVo);
    }

    /**
     * 设置甘特图子任务
     *
     * @param projectTaskeVo 项目任务vo
     * @param ganttVos       甘特图vo列表
     * @param parentGanttVo  甘特图vo
     */
    private void setGanttChildTask(ProjectTaskeVo projectTaskeVo, List<ProjectTaskeGanttVo> ganttVos, ProjectTaskeGanttVo parentGanttVo) {
        if (Optional.ofNullable(projectTaskeVo.getStartDate()).isPresent()) {
            ProjectTaskeGanttVo ganttVo = new ProjectTaskeGanttVo();
            BeanUtil.copyProperties(projectTaskeVo, ganttVo);
            ganttVo.setExpectStartDate(projectTaskeVo.getStartDate());
            if (Optional.ofNullable(projectTaskeVo.getEndDate()).isPresent()) {
                ganttVo.setExpectEndDate(projectTaskeVo.getEndDate());
            } else {
                ganttVo.setExpectEndDate(Optional.ofNullable(dailyPaperEntryMapper.findFinalPassedDailyDateByTaskId(projectTaskeVo.getId())).orElse(ganttVo.getExpectStartDate()));
            }
            if (!"0".equals(ganttVo.getTotalHour())) {
                ganttVo.setProgress(new BigDecimal(ganttVo.getNormalHour()).divide(new BigDecimal(ganttVo.getTotalHour()), 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString());
            }
            ganttVo.setId(null);
            ganttVo.setParentId(parentGanttVo.getId());
            ganttVos.add(ganttVo);
        }
    }

    /**
     * 设置任务按钮权限
     *
     * @param projectId       项目id
     * @param projectTaskeVos 项目任务vo列表
     * @param clientId        客户端id
     */
    private void setButtonAuthorities(Long projectId, List<ProjectTaskeVo> projectTaskeVos, Long clientId, String permission) {
        if (CollUtil.isEmpty(projectTaskeVos)) {
            return;
        }
        Long userId = SecurityUtils.getUser().getId();
        List<String> projectAuthorities = new ArrayList<>();

        // 判断是铁三角、项目操作助理、项目成员中哪一种
        List<Integer> roleTypes = isIronTriangle(projectId, userId);
        if (CollUtil.isNotEmpty(roleTypes)) {
            roleTypes.forEach(r -> projectAuthorities.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, r)));
        }
        if (isOperationsAssistant(projectId, userId)) {
            projectAuthorities.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue()));
        }
        // 项目成员默认存在
        projectAuthorities.add(EnumUtils.getNameByValue(BusinessRoleCodeEnum.class, RoleTypeEnum.PROJECT_MEMBER.getValue()));

        // 判断是否是任务负责人
        Map<Long, Integer> leaderFlagMap = projectTaskeVos.stream().collect(Collectors.toMap(ProjectTaskeVo::getId, t -> YesOrNoEnum.NO.getValue()));
        List<ProjectTaskeUser> leadersMap = projectTaskeUserMapper.findLeadersByTaskIds(leaderFlagMap.keySet());
        Map<Long, List<Long>> leaderIdsMap = new HashMap<>(projectTaskeVos.size());
        leadersMap.forEach(l -> {
            List<Long> ids = leaderIdsMap.get(l.getTaskId());
            if (!Optional.ofNullable(ids).isPresent()) {
                ids = new ArrayList<>();
                ids.add(l.getUserId());
                leaderIdsMap.put(l.getTaskId(), ids);
            } else {
                ids.add(l.getUserId());
            }
        });
        leaderIdsMap.forEach((k, v) -> {
            if (CollUtil.isNotEmpty(v) && v.contains(userId)) {
                leaderFlagMap.put(k, YesOrNoEnum.YES.getValue());
            }
        });

        // 获取权限
        List<SysMenuVo> projectRoles = Optional
                .ofNullable(remoteRoleService.getMenuAuthListByRoleListAndClientId(clientId, projectAuthorities, "1", permission).getData())
                .orElse(new ArrayList<>());
        List<SysMenuVo> leaderRoles = Optional
                .ofNullable(remoteRoleService.getMenuAuthListByRoleListAndClientId(clientId, Collections.singletonList(BusinessRoleCodeEnum.TASK_LEADER.getName()), "1", permission).getData())
                .orElse(new ArrayList<>());
        List<SysMenuVo> unionRoles = (List<SysMenuVo>) CollUtil.union(projectRoles, leaderRoles);
        projectRoles = projectRoles.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(SysMenuVo::getPermission))), ArrayList::new));
        for (String miniLeaderRole : miniLeaderRoles) {
            SysMenuVo sysMenuVo = new SysMenuVo();
            sysMenuVo.setPermission(miniLeaderRole);
            leaderRoles.add(sysMenuVo);
        }
        leaderRoles = leaderRoles.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(SysMenuVo::getPermission))), ArrayList::new));
        unionRoles = unionRoles.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(SysMenuVo::getPermission))), ArrayList::new));

        // 设置权限
        List<SysMenuVo> finalUnionRoles = unionRoles;
        List<SysMenuVo> finalMiniLeaderRoles = (List<SysMenuVo>) CollUtil.union(leaderRoles.stream().filter(l -> !miniLeaderRoles.contains(l.getPermission())).collect(Collectors.toList()), projectRoles);
        List<SysMenuVo> finalProjectRoles = projectRoles;
        AtomicBoolean isLeader = new AtomicBoolean(false);
        Collection<Integer> values = leaderFlagMap.values();
        if (CollUtil.isNotEmpty(values)) {
            values.stream().filter(i -> YesOrNoEnum.YES.getValue().equals(i)).findAny().ifPresent(i -> isLeader.set(true));
        }
        projectTaskeVos.forEach(t -> {
            Integer leaderFlag = leaderFlagMap.get(t.getId());
            if (CollUtil.isNotEmpty(projectAuthorities) && (Optional.ofNullable(leaderFlag).isPresent() && leaderFlag.equals(YesOrNoEnum.YES.getValue()))) {
                t.setButtonAuthorities(finalUnionRoles);
            } else if (Optional.ofNullable(leaderFlag).isPresent() && leaderFlag.equals(YesOrNoEnum.YES.getValue())) {
                t.setButtonAuthorities(finalUnionRoles);
            } else {
                if (isLeader.get()) {
                    t.setButtonAuthorities(finalMiniLeaderRoles);
                } else {
                    t.setButtonAuthorities(finalProjectRoles);
                }
            }
        });

        // 获取普通角色权限
        List<SysMenuVo> sysMenuVos = new ArrayList<>();
        com.gok.components.common.util.R<List<SysMenuVo>> funcAuthListByIdAndAppId = remoteOutService.getFuncAuthListByIdAndAppId(SecurityUtils.getUser().getId(), clientId);
        if (Optional.ofNullable(funcAuthListByIdAndAppId).isPresent()) {
            List<SysMenuVo> allSysMenuVos = funcAuthListByIdAndAppId.getData();
            if (CollUtil.isNotEmpty(allSysMenuVos)) {
                Optional<SysMenuVo> any = allSysMenuVos.stream().filter(s -> permission.equals(s.getPermission())).findAny();
                if (any.isPresent()) {
                    SysMenuVo sysMenuVo = any.get();
                    sysMenuVos = allSysMenuVos.stream().filter(s -> sysMenuVo.getMenuId().equals(s.getParentId())).collect(Collectors.toList());
                }
            }
        }
        if (CollUtil.isNotEmpty(sysMenuVos)) {
            for (ProjectTaskeVo t : projectTaskeVos) {
                if (CollUtil.isNotEmpty(t.getButtonAuthorities())) {
                    t.getButtonAuthorities().addAll(sysMenuVos);
                } else {
                    t.setButtonAuthorities(sysMenuVos);
                }
                if (CollUtil.isNotEmpty(t.getButtonAuthorities())) {
                    t.setButtonAuthorities(t.getButtonAuthorities().stream().collect(Collectors.collectingAndThen(
                                    Collectors.toCollection(() -> new TreeSet<>(
                                            Comparator.comparing(SysMenuVo::getPermission))), ArrayList::new))
                            .stream().sorted(Comparator.comparing(SysMenuVo::getSortOrder)).collect(Collectors.toList()));
                }
            }
        }
    }

    /**
     * 添加操作记录对象包装
     *
     * @param operationType 操作类型
     * @param operationInfo 操作信息（操作了哪些用户）
     * @param taskId        操作任务id
     */
    private void insertOperatingRecord(Integer operationType, String operationInfo, Long taskId) {
        OperatingRecord operatingRecord = new OperatingRecord();
        operatingRecord.setOperationType(operationType);
        operatingRecord.setOperationInfo(StringUtils.isBlank(operationInfo) ? "无" : operationInfo);
        operatingRecord.setTaskId(taskId);
        PigxUser user = SecurityUtils.getUser();
        operatingRecord.setOperator(user.getName());
        BaseBuildEntityUtil.buildInsert(operatingRecord);
        operatingRecordMapper.insert(operatingRecord);
    }

    /**
     * 格式化小数
     *
     * @param bigDecimal 十进制数
     * @return {@link String}
     */
    private String formatHours(BigDecimal bigDecimal) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        int newScale = 2;
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        if (Optional.ofNullable(bigDecimal).isPresent()) {
            bigDecimal = bigDecimal.divide(BigDecimal.valueOf(7), newScale, roundingMode).stripTrailingZeros();
            return new BigDecimal(DecimalFormatUtil.setAndValidate(bigDecimal.toString(), newScale, roundingMode, decimalFormat)).stripTrailingZeros().toPlainString();
        }
        return "0";
    }

    /**
     * 全量重置任务成员
     *
     * @param id       任务id（可不填）
     * @param members  任务成员（任务负责人、任务参与人）
     * @param taskRole 成员类型（负责人或参与人，为空则默认全部）
     */
    private void resetMember(Long id, ProjectTaskRoleEnum taskRole, List<ProjectTaskeUserDto> members, Boolean delFlag) {
        Map<Long, String> existsUserMap = new HashMap<>();
        Map<Long, String> newUserMap = new HashMap<>();
        Map<Long, String> addUserMap;
        Map<Long, String> delUserMap;
        List<ProjectTaskeUserDto> newMembers = members;

        if (Boolean.TRUE.equals(delFlag)) {
            List<ProjectTaskeUser> taskeUsers = projectTaskeUserMapper.findListByTaskId(id, taskRole.getValue());
            if (CollUtil.isNotEmpty(taskeUsers)) {
                existsUserMap = taskeUsers
                        .stream()
                        .collect(Collectors.toMap(ProjectTaskeUser::getUserId, ProjectTaskeUser::getUserName, (a, b) -> b));
            }

            List<Long> existsUserIds = taskeUsers.stream().map(ProjectTaskeUser::getUserId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(existsUserIds) && CollUtil.isNotEmpty(members)) {
                members = members.stream().filter(m -> !existsUserIds.contains(m.getUserId())).collect(Collectors.toList());
            }
        }

        if (CollUtil.isNotEmpty(newMembers)) {
            newUserMap = newMembers
                    .stream()
                    .collect(Collectors.toMap(ProjectTaskeUserDto::getUserId, ProjectTaskeUserDto::getUserName, (a, b) -> b));

            // 重新添加任务-用户数据
            if (CollUtil.isNotEmpty(members)) {
                List<ProjectTaskeUser> taskeUsers = BeanUtil.copyToList(members, ProjectTaskeUser.class);
                taskeUsers.forEach(BaseBuildEntityUtil::buildSave);
                List<Long> userIds = members.stream().map(ProjectTaskeUserDto::getUserId).distinct().collect(Collectors.toList());
                Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(userIds);
                List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
                Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));
                taskeUsers.forEach(member -> {
                    Roster roster = userIdMap.get(member.getUserId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        member.setPosition(roster.getJob());
                        member.setDeptName(projectStakeholderMemberService.buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                    }
                });
                projectTaskeUserMapper.batchSave(taskeUsers);
            }
        }

        Collection<Long> noChangeUsers = CollUtil.intersection(existsUserMap.keySet(), newUserMap.keySet());
        Map<Long, String> finalNewUserMap = newUserMap;
        addUserMap = CollUtil.subtract(newUserMap.keySet(), noChangeUsers)
                .stream()
                .collect(Collectors.toMap(userId -> userId, finalNewUserMap::get, (a, b) -> b));
        Map<Long, String> finalExistsUserMap = existsUserMap;
        delUserMap = CollUtil.subtract(existsUserMap.keySet(), newUserMap.keySet())
                .stream()
                .collect(Collectors.toMap(userId -> userId, finalExistsUserMap::get, (a, b) -> b));
        if (ProjectTaskRoleEnum.MEMBER.equals(taskRole)) {
            delUserMap = ImmutableMap.of();
        }

        // 删除被移除的人员
        if (CollUtil.isNotEmpty(delUserMap.keySet()) && ProjectTaskRoleEnum.LEADER.equals(taskRole)) {
            projectTaskeUserMapper.batchDelByUserIds(id, new ArrayList<>(delUserMap.keySet()), taskRole.getValue());
        }

        // 同步项目成员
        if (ProjectTaskRoleEnum.MEMBER.getValue().equals(taskRole.getValue()) || ProjectTaskRoleEnum.LEADER.getValue().equals(taskRole.getValue())) {
            List<ProjectTaskeUser> taskUsers = projectTaskeUserMapper.findListByTaskId(id, taskRole.getValue());
            // 过滤铁三角
            ProjectInfo projectInfo = projectInfoMapper.selectById(projectTaskeMapper.selectById(id).getProjectId());
            List<Long> ironTriangle = new ArrayList<>();
            if (Optional.ofNullable(projectInfo.getSalesmanUserId()).isPresent()) {
                ironTriangle.add(projectInfo.getSalesmanUserId());
            }
            if (Optional.ofNullable(projectInfo.getPreSaleUserId()).isPresent()) {
                ironTriangle.add(projectInfo.getPreSaleUserId());
            }
            if (Optional.ofNullable(projectInfo.getManagerUserId()).isPresent()) {
                ironTriangle.add(projectInfo.getManagerUserId());
            }
            if (CollUtil.isNotEmpty(taskUsers)) {
                taskUsers = taskUsers.stream().filter(t -> !ironTriangle.contains(t.getUserId())).collect(Collectors.toList());
            }
            syncMemberToProject(projectTaskeMapper.selectById(id).getProjectId(), taskUsers);
        }


        // 保存操作记录--人员相关
        saveOperationLog(id, taskRole, delFlag, addUserMap, delUserMap);

        //添加/移除
        if (CollUtil.isNotEmpty(addUserMap)) {
            Collection<String> values = addUserMap.values();
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.EDITING_TASKS,
                    "添加", values);
        }
        if (CollUtil.isNotEmpty(delUserMap)) {
            Collection<String> values = delUserMap.values();
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.EDITING_TASKS,
                    "移除", values);
        }

    }

    /**
     * 保存操作记录
     *
     */
    private void saveOperationLog(Long id, ProjectTaskRoleEnum taskRole, Boolean delFlag, Map<Long, String> addUserMap, Map<Long, String> delUserMap) {
        if (MapUtil.isNotEmpty(addUserMap)) {
            insertOperatingRecord(
                    ProjectTaskRoleEnum.LEADER.getValue().equals(taskRole.getValue()) ? OperationTypeEnum.TJFZR.getValue() : OperationTypeEnum.TJ.getValue(),
                    String.join(StrUtil.COMMA, addUserMap.values()),
                    id
            );
        }
        if (Boolean.TRUE.equals(delFlag) && MapUtil.isNotEmpty(delUserMap)) {
            insertOperatingRecord(
                    ProjectTaskRoleEnum.LEADER.getValue().equals(taskRole.getValue()) ? OperationTypeEnum.YCFZR.getValue() : OperationTypeEnum.YC.getValue(),
                    String.join(StrUtil.COMMA, delUserMap.values()),
                    id
            );
        }
    }

    /**
     * 设置任务负责人列表和参与人列表
     *
     * @param projectTaskeVos 项目任务Vo
     */
    private void setTaskLeadersAndMembers(List<ProjectTaskeVo> projectTaskeVos) {
        // 获取任务id列表
        List<Long> taskIds = projectTaskeVos.stream().map(ProjectTaskeVo::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskIds)) {
            // 获取任务-用户数据
            List<ProjectTaskeUser> taskeUsers = projectTaskeUserMapper.findListByTaskIds(taskIds);
            if (CollUtil.isNotEmpty(taskeUsers)) {
                // 任务-用户数据转任务-用户Vo
                Map<Long, List<ProjectTaskeUserVo>> taskeUsersMap = new HashMap<>();
                taskeUsers.forEach(taskeUser -> {
                    Long taskId = taskeUser.getTaskId();
                    List<ProjectTaskeUserVo> taskeUserList = taskeUsersMap.get(taskId);
                    if (Optional.ofNullable(taskeUserList).isPresent()) {
                        taskeUserList.add(BeanUtil.copyProperties(taskeUser, ProjectTaskeUserVo.class));
                    } else {
                        taskeUserList = new ArrayList<>();
                        taskeUserList.add(BeanUtil.copyProperties(taskeUser, ProjectTaskeUserVo.class));
                        taskeUsersMap.put(taskId, taskeUserList);
                    }
                });
                // 设置任务负责人列表和参与人列表
                projectTaskeVos.forEach(projectTaskeVo -> {
                    List<ProjectTaskeUserVo> taskeUserList = taskeUsersMap.get(projectTaskeVo.getId());
                    if (CollUtil.isNotEmpty(taskeUserList)) {
                        projectTaskeVo.setLeaders(taskeUserList.stream().filter(taskeUser -> ProjectTaskRoleEnum.LEADER.getValue().equals(taskeUser.getTaskRole())).collect(Collectors.toList()));
                        projectTaskeVo.setMembers(taskeUserList.stream().filter(taskeUser -> ProjectTaskRoleEnum.MEMBER.getValue().equals(taskeUser.getTaskRole())).collect(Collectors.toList()));
                    }
                });
            }
        }
    }

    /**
     * 判断是否是铁三角
     *
     * @param projectId 项目id
     * @param userId    用户id
     * @return {@link List}<{@link Integer}>
     */
    @Override
    public List<Integer> isIronTriangle(Long projectId, Long userId) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        // 信息为空返回空数组
        if (ObjectUtils.isEmpty(projectInfo) || null == userId) {
            return new ArrayList<>();
        }
        List<Integer> roleTypes = new ArrayList<>();
        if (userId.equals(projectInfo.getSalesmanUserId())) {
            roleTypes.add(RoleTypeEnum.PROJECT_SALES_MANAGER.getValue());
        }
        if (userId.equals(projectInfo.getPreSaleUserId())) {
            roleTypes.add(RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue());
        }
        if (userId.equals(projectInfo.getManagerUserId())) {
            roleTypes.add(RoleTypeEnum.PROJECT_MANAGER.getValue());
        }
        if (userId.equals(projectInfo.getBusinessManagerId())) {
            roleTypes.add(RoleTypeEnum.BUSINESS_MEMBER.getValue());
        }
        return roleTypes;
    }

    @Override
    public List<Integer> isIronTriangle(Long projectId, List<Long> userIds) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        List<Integer> roleTypes = new ArrayList<>();
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        if (projectInfo.getSalesmanUserId() != null && userIds.contains(projectInfo.getSalesmanUserId())) {
            roleTypes.add(RoleTypeEnum.PROJECT_SALES_MANAGER.getValue());
        }
        if (projectInfo.getPreSaleUserId() != null && userIds.contains(projectInfo.getPreSaleUserId())) {
            roleTypes.add(RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue());
        }
        if (projectInfo.getManagerUserId() != null && userIds.contains(projectInfo.getManagerUserId())) {
            roleTypes.add(RoleTypeEnum.PROJECT_MANAGER.getValue());
        }
        if (projectInfo.getBusinessManagerId() != null && userIds.contains(projectInfo.getBusinessManagerId())) {
            roleTypes.add(RoleTypeEnum.BUSINESS_MEMBER.getValue());
        }
        return roleTypes;
    }

    /**
     * 判断是否是操作助理
     *
     * @param projectId 项目id
     * @param userId    用户id
     * @return boolean
     */
    @Override
    public boolean isOperationsAssistant(Long projectId, Long userId) {
        List<ProjectStakeholderMemberVO> members = projectStakeholderMemberMapper.getMembersByProjectId(projectId);
        if (!Optional.ofNullable(members).isPresent()) {
            return false;
        }
        List<Long> assistantIds = members.stream()
                .filter(m -> RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue().equals(m.getRoleType()))
                .map(ProjectStakeholderMemberVO::getMemberId).distinct()
                .collect(Collectors.toList());
        return CollUtil.isNotEmpty(assistantIds) && assistantIds.contains(userId);
    }

    @Override
    public boolean isOperationsAssistant(Long projectId, List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return false;
        }
        List<ProjectStakeholderMemberVO> members = projectStakeholderMemberMapper.getMembersByProjectId(projectId);
        if (!Optional.ofNullable(members).isPresent()) {
            return false;
        }
        List<Long> assistantIds = members.stream()
                .filter(m -> RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue().equals(m.getRoleType()))
                .map(ProjectStakeholderMemberVO::getMemberId).distinct()
                .collect(Collectors.toList());
        return CollUtil.isNotEmpty(assistantIds) && userIds.stream().anyMatch(assistantIds::contains);
    }

    @Override
    public boolean isMember(Long projectId, List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return false;
        }
        // 获取项目干系人成员
        List<ProjectStakeholderMemberVO> members = projectStakeholderMemberMapper.getMembersByProjectId(projectId);
        if (!Optional.ofNullable(members).isPresent()) {
            return false;
        }
        List<Long> memberIds = members.stream()
                .filter(m -> RoleTypeEnum.PROJECT_MEMBER.getValue().equals(m.getRoleType()))
                .map(ProjectStakeholderMemberVO::getMemberId).distinct()
                .collect(Collectors.toList());

        // 通过人力外包获取项目人员列表
        AutoBringConditionDTO autoBringConditionDTO = new AutoBringConditionDTO();
        autoBringConditionDTO.setProjectId(projectId);
        List<CostPersonnelInformationVO> personAutoBringInfo = costPersonnelInformationMapper.getPersonAutoBringInfo(autoBringConditionDTO);
        if (CollUtil.isNotEmpty(personAutoBringInfo)) {
            List<Long> personUserIds = personAutoBringInfo.stream()
                    .map(CostPersonnelInformationVO::getUserId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            memberIds.addAll(personUserIds);
        }

        return CollUtil.isNotEmpty(memberIds) && userIds.stream().anyMatch(memberIds::contains);
    }

    /**
     * 判断是否是任务负责人
     *
     * @param userIds   用户id
     * @param projectId 项目id
     * @return boolean
     */
    @Override
    public boolean isLeader(Long projectId, List<Long> userIds) {
        List<ProjectTaske> tasks = projectTaskeMapper.findListByProjectId(projectId);
        if (CollUtil.isEmpty(tasks)) {
            return false;
        }
        List<ProjectTaskeUser> taskeUsers = projectTaskeUserMapper
                .findByTaskIdsAndTaskRole(tasks.stream().map(ProjectTaske::getId).distinct().collect(Collectors.toList()), ProjectTaskRoleEnum.LEADER.getValue());
        return CollUtil.isNotEmpty(taskeUsers)
                && taskeUsers.stream().map(ProjectTaskeUser::getUserId).distinct().collect(Collectors.toList()).stream().anyMatch(userIds::contains);
    }

    private void validExceptDate(Long projectId, LocalDate expectStartDate, LocalDate expectEndDate) {
        if (!Optional.ofNullable(expectStartDate).isPresent()) {
            throw new ValidationException("计划开始时间不能为空");
        }
        if (!Optional.ofNullable(expectEndDate).isPresent()) {
            throw new ValidationException("计划结束时间不能为空");
        }
    }

    /**
     * 发送企业微信通知（任务创建通知负责人）
     *
     * @param userIdToNameMap 用户姓名映射
     * @param projectTaske    项目任务
     */
    private void sendCreateTaskMsg(Map<Long, String> userIdToNameMap, ProjectTaske projectTaske) {
        if (CollUtil.isEmpty(userIdToNameMap) || !Optional.ofNullable(projectTaske).isPresent()) {
            return;
        }
        List<BcpMessageTargetDTO> targetList = new ArrayList<>(userIdToNameMap.size());
        userIdToNameMap.forEach((userId, userName) -> targetList.add(new BcpMessageTargetDTO(String.valueOf(userId), Optional.ofNullable(userName).orElse("defaultName"), null)));
        // 设置传输信息
        MailModel mailModel;
        WeComModel weComModel = new WeComModel();
        weComModel.setSource(SourceEnum.PROJECT.getValue());
        weComModel.setType(MsgTypeEnum.NOTICE_MSG.getValue());
        weComModel.setTitle("新任务提醒");
        String projectName = null;
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectTaske.getProjectId());
        if (Optional.ofNullable(projectInfo).isPresent()) {
            projectName = projectInfo.getItemName();
        }
        log.info("projectUrl:::" + projectUrl);
        log.info("taskTabUrl:::" + taskTabUrl);
        log.info("redirectPrefix:::" + redirectPrefix);
        String taskTab = projectUrl + taskTabUrl;
        String taskTaskUrl = redirectPrefix +
                Base64.encode(String.format(taskTab, projectTaske.getProjectId()), Charsets.UTF_8);
        String directTemplate = "\n<a href=\"%s\">查看详情</a>";
        String directContent = "您将负责任务【" + projectName + "】-【" + projectTaske.getTitle() + "】，请注意关注~";
        String directLinkContent = String.format(directTemplate, taskTaskUrl);
        weComModel.setContent(directContent + directLinkContent);
        weComModel.setRedirectUrl("");
        weComModel.setSenderId(10000L);
        weComModel.setSender("admin");
        weComModel.setTargetType(TargetTypeEnum.USERS.getValue());
        weComModel.setTargetList(targetList);
        // 发送企微消息
        remoteSendMsgService.sendMsg(weComModel);
        // 发送门户消息
        mailModel = MailModel.from(WeComModel.to(weComModel));
        mailModel.setRedirectUrl(taskTaskUrl);
        mailModel.setContent(directContent);
        remoteMailService.sendMsg(mailModel);
    }

    /**
     * 发送企业微信和门户通知
     *
     * @param userIdToNameMap  用户姓名映射
     * @param deadLineNotifyVo 传输信息
     */
    private void sendMsg(Map<Long, String> userIdToNameMap, DeadLineNotifyVO deadLineNotifyVo) {
        PigxUser user = SecurityUtils.getUser();
        Long userId = deadLineNotifyVo.getManagerUserId();
        String userName = userIdToNameMap.get(userId);
        if (userId == null || userName == null) {
            return;
        }
        List<BcpMessageTargetDTO> targetList = new ArrayList<>(1);
        targetList.add(new BcpMessageTargetDTO(String.valueOf(userId), Optional.ofNullable(userName).orElse("defaultName"), null));
        // 设置传输信息
        WeComModel weComModel = new WeComModel();
        MailModel mailModel;
        String content = deadLineNotifyVo.getInfo();
        weComModel.setSource(SourceEnum.PROJECT.getValue());
        weComModel.setType(MsgTypeEnum.WARN_MSG.getValue());
        weComModel.setTitle("任务即将超期发送通知");
        weComModel.setContent(content + "\n<a href=\"" + projectUrl + "\">查看详情</a>");
        weComModel.setRedirectUrl(
                redirectPrefix + Base64.encode(projectUrl + "/view-businesses/statistical-query/personnel-panel", Charsets.UTF_8)
        );
        if (user == null) {
            weComModel.setSenderId(10000L);
            weComModel.setSender("admin");
        } else {
            weComModel.setSenderId(user.getId());
            weComModel.setSender(user.getName());
        }
        weComModel.setTargetType(TargetTypeEnum.USERS.getValue());
        weComModel.setTargetList(targetList);
        mailModel = MailModel.from(WeComModel.to(weComModel));
        mailModel.setContent(content);
        // 发送企微消息和门户消息
        try {
            remoteSendMsgService.sendMsg(weComModel);
            remoteMailService.sendMsg(mailModel);
        } catch (Exception e) {
            log.info("中台企业微信/门户消息推送失败: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncRemoveProjectTask(CostDeliverTask costDeliverTask) {
        ProjectTaske projectTaske = lambdaQuery()
                .eq(ProjectTaske::getRelateId, costDeliverTask.getId())
                .one();
        if (projectTaske == null) {
            return;
        }
        deleteById(projectTaske.getId());
    }
    /**
     * 批量将工单映射为项目任务并自动创建或更新
     *
     * @param costDeliverTaskList 成本交付任务(工单)列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSyncProjectTask(List<CostDeliverTask> costDeliverTaskList) {
        if (CollUtil.isEmpty(costDeliverTaskList)) {
            return;
        }

        List<Long> taskIds = CollStreamUtil.toList(costDeliverTaskList, CostDeliverTask::getId);
        Map<Long,Long> taskeMap = lambdaQuery().in(ProjectTaske::getRelateId, taskIds)
                .eq(ProjectTaske::getType, ProjectTaskeTypeEnum.COST_TASK.getValue())
                .list().stream()
                .collect(Collectors.toMap(ProjectTaske::getRelateId,ProjectTaske::getId));

        List<ProjectTaske> addList = new ArrayList<>();
        List<ProjectTaske> editList = new ArrayList<>();
        List<ProjectTaskeUser> addTaskeUserList = new ArrayList<>();

        ICostDeliverTaskService deliverTaskService = SpringContextHolder.getBean(ICostDeliverTaskService.class);
        // 获取任务审核人
        Map<Long, Set<Pair<Long,String>>> reviewersMap = deliverTaskService.getReviewersByTaskIds(taskIds);
        CostDeliverTask firstTask = costDeliverTaskList.stream().findFirst().orElse(new CostDeliverTask());
        // 是否项目成本任务
        boolean isProjectCost = EnumUtils.valueEquals(firstTask.getTaskCategory(), TaskCategoryEnum.PROJECT_COST);
        //  为项目成本任务则获取项目组成员
        Set<Pair<Long,String>> projectMembers = isProjectCost
                        ? projectStakeholderMemberMapper.getMembersByProjectId(firstTask.getProjectId())
                        .stream().map(m -> Pair.of(m.getMemberId(), m.getMemberName()))
                        .collect(Collectors.toSet())
                        : Collections.emptySet();
        costDeliverTaskList.forEach(costDeliverTask -> {
            // 任务参与人
            Set<Pair<Long, String>> members = isProjectCost ? projectMembers :
                    Collections.singleton(Pair.of(costDeliverTask.getManagerId(), costDeliverTask.getManagerName()));
            // 获取任务负责人
            Set<Pair<Long, String>> reviewerPairList = reviewersMap.getOrDefault(costDeliverTask.getId(), Collections.emptySet());
            Long taskId = costDeliverTask.getId();
            ProjectTaske projectTaske = new ProjectTaske()
                    .setProjectId(costDeliverTask.getProjectId())
                    .setTitle(costDeliverTask.getTaskName())
                    .setKind(costDeliverTask.getTaskCategory())
                    .setState(ProjectTaskStateEnum.NORMAL.getValue())
                    .setPermanentFlag(EnumUtils.valueEquals(costDeliverTask.getTaskType(), ProjectTaskKindEnum.PRE_SALES_SUPPORT)
                            || isProjectCost ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue())
                    .setType(ProjectTaskeTypeEnum.COST_TASK.getValue())
                    .setExpectStartDate(costDeliverTask.getStartDate())
                    .setExpectEndDate(costDeliverTask.getEndDate())
                    .setRelateId(taskId);

            // 添加任务负责人
            projectTaske.setLeaderNames(reviewerPairList.stream().map(Pair::getRight)
                    .collect(Collectors.joining(StrPool.COMMA)));
            // 添加任务参与人
            projectTaske.setMemberNames(members.stream().map(Pair::getRight)
                    .collect(Collectors.joining(StrPool.COMMA)));

            if (taskeMap.containsKey(taskId)) {
                projectTaske.setId(taskeMap.get(taskId));
                editList.add(BaseBuildEntityUtil.buildUpdate(projectTaske));
            } else {
                addList.add(BaseBuildEntityUtil.buildInsert(projectTaske));
            }

            // 添加任务参与人
            for (Pair<Long,String> member : members) {
                Long userId = member.getLeft();
                String userName = member.getRight();
                if (userId == null|| userName == null) {
                    log.warn("项目id：{}，任务参与人参数缺失:{}",projectTaske.getProjectId(), member.toString());
                    continue;
                }
                addTaskeUserList.add(BaseBuildEntityUtil.buildInsert(new ProjectTaskeUser()
                        .setTaskRole(ProjectTaskRoleEnum.MEMBER.getValue())
                        .setTaskId(projectTaske.getId())
                        .setUserId(userId)
                        .setUserName(userName)));
            }
            // 添加任务负责人
            for (Pair<Long, String> reviewerPair : reviewerPairList) {
                Long userId = reviewerPair.getLeft();
                String userName = reviewerPair.getRight();
                if (userId == null || userName == null) {
                    log.warn("项目id：{}，任务负责人参数缺失:{}", projectTaske.getProjectId(), reviewerPair.toString());
                    continue;
                }
                addTaskeUserList.add(BaseBuildEntityUtil.buildInsert(new ProjectTaskeUser()
                        .setTaskRole(ProjectTaskRoleEnum.LEADER.getValue())
                        .setTaskId(projectTaske.getId())
                        .setUserId(userId)
                        .setUserName(userName)));
            }
        });
        // 批量保存或更新项目任务
        saveBatch(addList);
        updateBatchById(editList);

        // 批量处理项目任务用户
        if (CollUtil.isNotEmpty(editList)) {
            projectTaskeUserMapper.batchDeleteByTaskIds(CollStreamUtil.toList(editList, ProjectTaske::getId));
        }
        projectTaskeUserMapper.batchSave(addTaskeUserList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void finishByCostTaskIds(Collection<Long> costTaskIds) {
        if (CollUtil.isEmpty(costTaskIds)) {
            return;
        }
        List<ProjectTaske> projectTaskes = lambdaQuery()
                .in(ProjectTaske::getRelateId, costTaskIds)
                .eq(ProjectTaske::getType, ProjectTaskeTypeEnum.COST_TASK.getValue())
                .list();

        projectTaskes.forEach(projectTaske -> {
            projectTaske.setState(ProjectTaskStateEnum.FINISHED.getValue());
            projectTaske.setEndDate(LocalDate.now());
            BaseBuildEntityUtil.buildUpdate(projectTaske);
        });
        updateBatchById(projectTaskes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startByCostTaskIds(Collection<Long> costTaskIds) {
        if (CollUtil.isEmpty(costTaskIds)) {
            return;
        }
        List<ProjectTaske> projectTaskes = lambdaQuery()
                .in(ProjectTaske::getRelateId, costTaskIds)
                .eq(ProjectTaske::getType, ProjectTaskeTypeEnum.COST_TASK.getValue())
                .list();
        projectTaskes.forEach(projectTaske -> {
            projectTaske.setState(ProjectTaskStateEnum.NORMAL.getValue());
            projectTaske.setEndDate(null);
            BaseBuildEntityUtil.buildUpdate(projectTaske);
        });
        updateBatchById(projectTaskes);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void syncProjectCostTaskMember(Long projectId) {
        ICostDeliverTaskService deliverTaskService = SpringContextHolder.getBean(ICostDeliverTaskService.class);
        List<CostDeliverTask> costDeliverTaskList = deliverTaskService.lambdaQuery()
                .eq(CostDeliverTask::getProjectId, projectId)
                .eq(CostDeliverTask::getTaskCategory, TaskCategoryEnum.PROJECT_COST.getValue())
                .list();

        if (CollUtil.isEmpty(costDeliverTaskList)) {
            return;
        }
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);

        List<Long> costTaskIds = CollStreamUtil.toList(costDeliverTaskList, CostDeliverTask::getId);

        List<ProjectTaske> projectTaskeList = lambdaQuery()
                .in(ProjectTaske::getRelateId, costTaskIds)
                .eq(ProjectTaske::getType, ProjectTaskeTypeEnum.COST_TASK.getValue())
                .list();

        List<ProjectStakeholderMemberVO> members = projectStakeholderMemberMapper.getMembersByProjectId(projectId);

        List<ProjectTaskeUser> addTaskeUserList = new ArrayList<>();
        for (ProjectTaske projectTaske : projectTaskeList) {
            for (ProjectStakeholderMemberVO member : members) {
                addTaskeUserList.add(BaseBuildEntityUtil.buildInsert(
                        new ProjectTaskeUser().setTaskId(projectTaske.getId())
                                .setTaskRole(ProjectTaskRoleEnum.MEMBER.getValue())
                                .setUserId(member.getMemberId())
                                .setUserName(member.getMemberName())));
            }
            addTaskeUserList.add(BaseBuildEntityUtil.buildInsert(
                    new ProjectTaskeUser().setTaskId(projectTaske.getId())
                            .setTaskRole(ProjectTaskRoleEnum.LEADER.getValue())
                            .setUserId(projectInfo.getManagerUserId())
                            .setUserName(projectInfo.getManagerUserName())));
        }
        projectTaskeUserMapper.batchDeleteByTaskIds(CollStreamUtil.toList(projectTaskeList, ProjectTaske::getId));
        projectTaskeUserMapper.batchSave(addTaskeUserList);
    }

}
