package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 工单日报工时条目
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_task_daily_paper_entry")
public class CostTaskDailyPaperEntry extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 日报ID
     */
    private Long dailyPaperId;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     * @see ApprovalStatusEnum#getValue()
     */
    private Integer approvalStatus;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;


    /**
     * 实际人工成本
     */
    private String actualLaborCost;

    /**
     * 描述
     */
    private String description;

    /**
     * 审核不通过原因
     */
    private String approvalReason;

    /**
     * 提交人ID
     */
    private Long userId;

    /**
     * 提交人姓名
     */
    private String userRealName;

    /**
     * 提交人所属部门ID
     */
    private Long userDeptId;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer workType;

    /**
     * 旧任务标识
     */
    private Integer oldTaskFlag;


    /**
     * 解密
     *
     * @return {@link CostTaskDailyPaperEntry }
     */
    public CostTaskDailyPaperEntry decrypt() {
        this.actualLaborCost = AESEncryptor.justDecrypt(this.actualLaborCost);
        return this;
    }

    /**
     * 加密
     *
     * @return {@link CostTaskDailyPaperEntry }
     */
    public CostTaskDailyPaperEntry encrypt() {
        this.actualLaborCost = AESEncryptor.justEncrypt(this.actualLaborCost);
        return this;
    }
} 