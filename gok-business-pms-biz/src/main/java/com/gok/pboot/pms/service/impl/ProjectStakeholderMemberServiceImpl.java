package com.gok.pboot.pms.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.components.common.util.SpringContextHolder;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberBatchDTO;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberDTO;
import com.gok.pboot.pms.entity.vo.ProjectInfoMemberVO;
import com.gok.pboot.pms.entity.vo.ProjectInfoVO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO;
import com.gok.pboot.pms.entity.vo.UserJobDeptVo;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectStakeholderMemberMapper;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.gok.pboot.pms.service.IProjectStakeholderMemberService;
import com.gok.pboot.pms.service.IProjectTaskeService;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ProjectStakeholderMemberServiceImpl extends ServiceImpl<ProjectStakeholderMemberMapper, ProjectStakeholderMember> implements IProjectStakeholderMemberService {

    private final ProjectStakeholderMemberMapper mapper;

    private final IProjectInfoService service;

    private final RosterMapper rosterMapper;

    private final ProjectInfoMapper projectInfoMapper;
    @Resource
    private  RemoteOutService remoteOutService;

    private final DbApiUtil dbApiUtil;

    private final BcpLoggerUtils bcpLoggerUtils;

    private final OaUtil oaUtil;

    private static final String PROJECT_ID_NOT_NULL = "项目id不能为空";

    @Override
    public ApiResult<List<ProjectStakeholderMemberVO>> getMemberByProjectId(Long projectId, Integer roleType) {
        return ApiResult.success(Optional.ofNullable(mapper.getMemberByProjectId(projectId, roleType)).orElse(new ArrayList<>()));
    }

    @Override
    public ApiResult<Page<ProjectStakeholderMemberVO>> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        List<ProjectStakeholderMember> dataList = mapper.findList(filter);
        Map<Long, List<ProjectStakeholderMember>> userIdAndMembersMap;
        Page<Long> userIdsPaged;
        Map<Long, Roster> idAndRosterMap;

        if (dataList.isEmpty()) {
            return ApiResult.success(null);
        }
        Long projectId = (Long) filter.get("projectId");
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo.getIsNotInternalProject() == 1) {
            dataList =
                    dataList.stream()
                            .filter(member ->
                                    !EnumUtils.valueEquals(member.getRoleType(), RoleTypeEnum.PROJECT_SALES_MANAGER) &&
                                            !EnumUtils.valueEquals(member.getRoleType(), RoleTypeEnum.PROJECT_PRE_SALES_MANAGER)
                            )
                            .collect(Collectors.toList());
        } else {
            dataList =
                    dataList.stream()
                            .filter(member ->
                                    !EnumUtils.valueEquals(member.getRoleType(), RoleTypeEnum.BUSINESS_MEMBER)
                            )
                            .collect(Collectors.toList());
        }
        dataList = dataList.stream()
                .peek(d -> d.setSortOrder(RoleTypeEnum.getSortOrderByValue(d.getRoleType())))
                .sorted(Comparator.comparing(ProjectStakeholderMember::getSortOrder)).collect(Collectors.toList());
        userIdAndMembersMap = dataList.stream()
                .collect(Collectors.groupingBy(
                        ProjectStakeholderMember::getMemberId, LinkedHashMap::new, Collectors.toList()
                ));
        userIdsPaged = PageUtils.page(ImmutableList.copyOf(userIdAndMembersMap.keySet()), pageRequest);
        idAndRosterMap = rosterMapper.findUserIdMap(userIdsPaged.getRecords());


        return ApiResult.success(PageUtils.mapTo(userIdsPaged, uId -> {
            List<ProjectStakeholderMember> members = userIdAndMembersMap.get(uId);
            return getVo(members, projectInfo, idAndRosterMap);
        }));
    }

    @Nullable
    private static ProjectStakeholderMemberVO getVo(List<ProjectStakeholderMember> members, ProjectInfo projectInfo, Map<Long, Roster> idAndRosterMap) {
        StringJoiner joiner = new StringJoiner("、");
        return members.stream()
                .sorted((m1, m2) -> {
                    // 根据角色枚举值排序，以便合并多种角色
                    Integer m1SortOrder = m1.getSortOrder() == null ? 999 : m1.getSortOrder();
                    Integer m2SortOrder = m2.getSortOrder() == null ? 999 : m2.getSortOrder();
                    int roleComp = m1SortOrder -m2SortOrder;
                    Timestamp m1Ctime = m1.getCtime();
                    Timestamp m2Ctime = m2.getCtime();
                    boolean canCompCtime = m1Ctime != null && m2Ctime != null;

                    if (roleComp == 0) {
                        return canCompCtime ? m1Ctime.compareTo(m2Ctime) : 0;
                    } else {
                        return roleComp;
                    }
                })
                .peek(m -> {
                    // 合并铁三角名称
                    Integer roleType = m.getRoleType();
                    Long isNotInternalProject = projectInfo.getIsNotInternalProject();
                    if (isNotInternalProject == 2 && EnumUtils.valueOrEquals(roleType,
                            RoleTypeEnum.PROJECT_SALES_MANAGER,
                            RoleTypeEnum.PROJECT_PRE_SALES_MANAGER,
                            RoleTypeEnum.PROJECT_MANAGER)) {
                        joiner.add(EnumUtils.getNameByValue(RoleTypeEnum.class, m.getRoleType()));
                    } else if (isNotInternalProject == 1 && EnumUtils.valueOrEquals(roleType,
                            RoleTypeEnum.PROJECT_MANAGER,
                            RoleTypeEnum.BUSINESS_MEMBER)) {
                        joiner.add(EnumUtils.getNameByValue(RoleTypeEnum.class, m.getRoleType()));
                    }
                })
                .skip(members.size() - 1)
                .map(m -> {
                    // 取最后一个元素后，映射为vo对象，补全铁三角名称字段（如果需要）
                    Roster roster = idAndRosterMap.get(m.getMemberId());
                    ProjectStakeholderMemberVO vo =
                            ProjectStakeholderMemberVO.of(m, roster == null ? null : roster.getEmployeeStatus());

                    if (joiner.length() > 0) {
                        vo.setRoleTypeTxt(joiner.toString());
                        vo.setSyncOaTypeTxt(StrPool.SLASH);
                    }

                    return vo;
                })
                .findFirst()
                .orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(ProjectStakeholderMemberBatchDTO dto) {
        if (!Optional.ofNullable(dto.getProjectId()).isPresent()) {
            throw new ValidationException(PROJECT_ID_NOT_NULL);
        }
        if (!Optional.ofNullable(dto.getRoleType()).isPresent()) {
            throw new ValidationException("成员角色不能为空");
        }
        List<ProjectStakeholderMemberDTO> members = dto.getMembers();
        // 过滤铁三角
        ProjectInfo projectInfo = projectInfoMapper.selectById(dto.getProjectId());
        List<Long> ironTriangle = new ArrayList<>();
        if (Optional.ofNullable(projectInfo.getSalesmanUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getSalesmanUserId());
        }
        if (Optional.ofNullable(projectInfo.getPreSaleUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getPreSaleUserId());
        }
        if (Optional.ofNullable(projectInfo.getManagerUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getManagerUserId());
        }
        if (CollUtil.isNotEmpty(members)) {
            members = members.stream().filter(m -> !ironTriangle.contains(m.getMemberId())).collect(Collectors.toList());
        }

        // 过滤已存在的人员
        LambdaQueryWrapper<ProjectStakeholderMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectStakeholderMember::getProjectId, dto.getProjectId());
        List<ProjectStakeholderMember> exists = mapper.selectList(queryWrapper);
        if (RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue().equals(dto.getRoleType()) && CollUtil.isNotEmpty(members) && CollUtil.isNotEmpty(exists)) {
            List<Long> addUserIds = members.stream().map(ProjectStakeholderMemberDTO::getMemberId).collect(Collectors.toList());
            List<Long> needDelIds = exists.stream()
                    .filter(e -> addUserIds.contains(e.getMemberId()) && RoleTypeEnum.PROJECT_MEMBER.getValue().equals(e.getRoleType()))
                    .map(ProjectStakeholderMember::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(needDelIds)) {
                mapper.logicalDelByIds(needDelIds);
                exists = exists.stream().filter(e -> !needDelIds.contains(e.getId())).collect(Collectors.toList());
            }
        }
        if (CollUtil.isNotEmpty(members) && CollUtil.isNotEmpty(exists)) {
            List<Long> userIds = exists.stream().map(ProjectStakeholderMember::getMemberId).collect(Collectors.toList());
            members = members.stream().filter(m -> !userIds.contains(m.getMemberId())).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(members)) {
            List<Long> userIds = members.stream().map(ProjectStakeholderMemberDTO::getMemberId).distinct().collect(Collectors.toList());
            Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(userIds);
            Map<Long, SysDeptOutVO> deptMap = getDeptMap();
            for (ProjectStakeholderMemberDTO member : members) {
                Roster roster = userIdMap.get(member.getMemberId());
                if (Optional.ofNullable(roster).isPresent()) {
                    member.setPosition(roster.getJob());
                    member.setDeptName(buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                }
            }
            List<ProjectStakeholderMember> projectStakeholderMembers = new ArrayList<>(members.size());
            for (ProjectStakeholderMemberDTO member : members) {
                member.setProjectId(dto.getProjectId());
                member.setRoleType(dto.getRoleType());
                projectStakeholderMembers.add(ProjectStakeholderMember.saveOrUpdate(member));
            }
            mapper.batchSync(projectStakeholderMembers);
        }

        // 异步同步项目成员到OA
        IProjectStakeholderMemberService projectStakeholderMemberService = SpringContextHolder.getBean(IProjectStakeholderMemberService.class);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                projectStakeholderMemberService.asyncOa(dto.getProjectId());
            }
        });
        if (CollUtil.isNotEmpty(dto.getMembers())) {
            String result = dto.getMembers().stream()
                    .map(m -> m.getMemberName())
                    .collect(Collectors.joining(","));
            if (RoleTypeEnum.PROJECT_MEMBER.getValue().equals(dto.getRoleType())) {
                //新增项目成员
                bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.ADD_PROJECT_MEMBERS,
                        result);
            } else if (RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue().equals(dto.getRoleType())) {
                //新增项目操作助理
                bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.NEWLY_ADDED_WORK_ASSISTANT,
                        result);
            }

        }

        // 更新项目成本工单的工时任务成员
        IProjectTaskeService projectTaskeService = SpringContextHolder.getBean(IProjectTaskeService.class);
        projectTaskeService.syncProjectCostTaskMember(dto.getProjectId());
        return Boolean.TRUE;
    }

    @Override
    public String buildDeptName(Long deptId, Map<Long, SysDeptOutVO> deptMap, Boolean isFirst, String deptName) {
        if (Optional.ofNullable(deptId).isPresent()) {
            if (Boolean.TRUE.equals(isFirst)) {
                SysDeptOutVO sysDeptOutVO = deptMap.get(deptId);
                if (Optional.ofNullable(sysDeptOutVO).isPresent() && CharSequenceUtil.isNotBlank(sysDeptOutVO.getName())) {
                    deptName = buildDeptName(sysDeptOutVO.getParentId(), deptMap, Boolean.FALSE, sysDeptOutVO.getName());
                }
            } else {
                Optional<SysDeptOutVO> first = deptMap.values().stream().filter(d -> d.getDeptId().equals(deptId)).findFirst();
                if (first.isPresent()) {
                    SysDeptOutVO d = first.get();
                    if (CharSequenceUtil.isNotBlank(d.getName())) {
                        deptName = buildDeptName(d.getParentId(), deptMap, Boolean.FALSE, d.getName() + "/" + deptName);
                    }
                }
            }
        }

        return deptName;
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> delById(Long id) {
        if (id == null) {
            return ApiResult.failure("请选择删除数据");
        }
        ProjectStakeholderMember projectStakeholderMember = mapper.selectById(id);
        if (projectStakeholderMember == null) {
            return ApiResult.failure("数据不存在");
        }
        mapper.deleteById(id);

        if (Optional.ofNullable(projectStakeholderMember.getProjectId()).isPresent()) {
            IProjectStakeholderMemberService projectStakeholderMemberService = SpringContextHolder.getBean(IProjectStakeholderMemberService.class);
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    projectStakeholderMemberService.asyncOa(projectStakeholderMember.getProjectId());
                }
            });
        }
        //删除【姓名}
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_TASKS, LogContentEnum.DELETE_STAKEHOLDERS,
                projectStakeholderMember.getMemberName());
        return ApiResult.success("操作成功");
    }

    @Override
    public ProjectInfoMemberVO getIronTriangleByProjectId(Long projectId) {
        ProjectInfoMemberVO projectInfoMemberVo = new ProjectInfoMemberVO();
        projectInfoMemberVo.setProjectId(projectId);
        ProjectInfoVO data = service.getProjectInfoById(projectId);
        if (data != null) {
            // 销售经理
            projectInfoMemberVo.setPreSaleUserId(data.getPreSaleUserId());
            projectInfoMemberVo.setPreSaleUserName(Strings.nullToEmpty(data.getPreSaleUserName()));
            // 客户经理
            projectInfoMemberVo.setSalesmanUserId(data.getSalesmanUserId());
            projectInfoMemberVo.setProjectSalesperson(Strings.nullToEmpty(data.getProjectSalesperson()));
            //  项目经理
            projectInfoMemberVo.setManagerUserId(data.getManagerUserId());
            projectInfoMemberVo.setManagerUserName(Strings.nullToEmpty(data.getManagerUserName()));
            //  商务经理
            projectInfoMemberVo.setBusinessManagerName(Strings.nullToEmpty(data.getBusinessManager()));
            projectInfoMemberVo.setBusinessManagerId(String.valueOf(data.getBusinessManagerId()));
        }
        return projectInfoMemberVo;
    }

    @Override
    public ApiResult<UserJobDeptVo> getUserJobDeptByUserId(Long userId) {
        Roster roster = rosterMapper.selectById(userId);
        List<SysDeptOutVO> deptList;
        UserJobDeptVo result;
        Long deptId;

        if (roster == null) {
            return ApiResult.success(null);
        }
        deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        result = new UserJobDeptVo();
        result.setUserId(userId);
        deptId = roster.getDeptId();
        if (deptId != null) {
            result.setDeptId(deptId);
            deptList.stream().filter(d -> deptId.equals(d.getDeptId())).findFirst().ifPresent(dept -> {
                result.setDeptName(Strings.nullToEmpty(dept.getName()));
            });
        }
        result.setJobName(Strings.nullToEmpty(roster.getJob()));

        return ApiResult.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRoleType(ProjectStakeholderMemberDTO dto) {
        if (!Optional.ofNullable(dto.getId()).isPresent()) {
            throw new ValidationException("项目干系人id不能为空");
        }
        if (!Optional.ofNullable(dto.getRoleType()).isPresent() || !EnumUtils.existsEnumValue(dto.getRoleType(), RoleTypeEnum.class)) {
            throw new ValidationException("角色类型不合法");
        }
        //【人员姓名】角色修改为【修改后的角色】
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.MODIFY_ROLE,
                dto.getMemberName(), EnumUtils.getNameByValue(RoleTypeEnum.class, dto.getRoleType()));
        int count = mapper.updateRoleType(BaseBuildEntityUtil.buildUpdate(BeanUtil.copyProperties(dto, ProjectStakeholderMember.class)));
        if (count > 0) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRemark(ProjectStakeholderMemberDTO dto) {
        if (!Optional.ofNullable(dto.getId()).isPresent()) {
            throw new ValidationException("项目干系人id不能为空");
        }
        int count = 0;
        ProjectStakeholderMember projectStakeholderMember = BaseBuildEntityUtil.buildUpdate(BeanUtil.copyProperties(dto, ProjectStakeholderMember.class));
        BaseBuildEntityUtil.buildUpdate(projectStakeholderMember);
        count = mapper.updateRemark(projectStakeholderMember);
        //编辑备注
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.EDIT_NOTES,
                dto.getMemberName());
        if (count > 0) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncOa(Long projectId) {
        if (!Optional.ofNullable(projectId).isPresent()) {
            throw new ValidationException(PROJECT_ID_NOT_NULL);
        }
        Integer allCount;
        Integer successCount = 0;
        List<ProjectStakeholderMemberVO> members = mapper.getMembersByProjectId(projectId);
        List<Long> updateUserIds = new ArrayList<>();

        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        List<Long> ironTriangle = new ArrayList<>();
        if (Optional.ofNullable(projectInfo.getSalesmanUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getSalesmanUserId());
        }
        if (Optional.ofNullable(projectInfo.getPreSaleUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getPreSaleUserId());
        }
        if (Optional.ofNullable(projectInfo.getManagerUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getManagerUserId());
        }
        if (Optional.ofNullable(projectInfo.getBusinessManagerId()).isPresent()) {
            ironTriangle.add(projectInfo.getBusinessManagerId());
        }
        // 过滤铁三角
        if (CollUtil.isNotEmpty(ironTriangle) && CollUtil.isNotEmpty(members)) {
            members = members.stream().filter(m -> !ironTriangle.contains(m.getMemberId())).collect(Collectors.toList());
        }
        allCount = members.size();
        // 过滤无oa账号成员
        if (CollUtil.isNotEmpty(members)) {
            List<String> userIds = members.stream().map(ProjectStakeholderMemberVO::getMemberId).map(String::valueOf).collect(Collectors.toList());
            List<Long> existsOaUserIds = dbApiUtil.getExistsOaUserIds(userIds);
            updateUserIds = dbApiUtil.batchExistsOaUserId(userIds);
            if (CollUtil.isNotEmpty(existsOaUserIds)) {
                members = members.stream().filter(m -> existsOaUserIds.contains(m.getMemberId())).collect(Collectors.toList());
            } else {
                members = ImmutableList.of();
            }
        }

        String xmcy = "";
        if (CollUtil.isNotEmpty(updateUserIds)) {
            successCount = updateUserIds.size();
            xmcy = "" + updateUserIds.stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA)) + "";
        }

        //通过OA数据库修改项目成员无法更新权限，修改为接口更新
        JSONObject mainTable = new JSONObject();
        mainTable.put("xmcy", xmcy);
        oaUtil.doAction(String.valueOf(projectId), mainTable);

        List<Long> syncOaUserIds = members.stream().map(ProjectStakeholderMemberVO::getMemberId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(syncOaUserIds)) {
            mapper.updateSyncOaTypeByProjectId(projectId, syncOaUserIds);
        }

        //日志操作
        if (CollUtil.isNotEmpty(members)) {
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.SYNCHRONIZE_TO_OA,
                    successCount, allCount - successCount);
        }
        return String.format("同步完成, 成功%s个, 失败%s个", successCount, allCount - successCount);
    }

    /**
     * 异步同步OA
     *
     * @param projectId 项目id
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void asyncOa(Long projectId) {
        if (!Optional.ofNullable(projectId).isPresent()) {
            throw new ValidationException(PROJECT_ID_NOT_NULL);
        }
        List<ProjectStakeholderMemberVO> members = mapper.getMembersByProjectId(projectId);
        List<Long> updateUserIds = new ArrayList<>();

        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        List<Long> ironTriangle = new ArrayList<>();
        if (Optional.ofNullable(projectInfo.getSalesmanUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getSalesmanUserId());
        }
        if (Optional.ofNullable(projectInfo.getPreSaleUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getPreSaleUserId());
        }
        if (Optional.ofNullable(projectInfo.getManagerUserId()).isPresent()) {
            ironTriangle.add(projectInfo.getManagerUserId());
        }
        // 过滤铁三角
        if (CollUtil.isNotEmpty(ironTriangle) && CollUtil.isNotEmpty(members)) {
            members = members.stream().filter(m -> !ironTriangle.contains(m.getMemberId())).collect(Collectors.toList());
        }
        // 过滤无oa账号成员
        if (CollUtil.isNotEmpty(members)) {
            List<String> userIds = members.stream().map(ProjectStakeholderMemberVO::getMemberId).map(String::valueOf).collect(Collectors.toList());
            List<Long> existsOaUserIds = dbApiUtil.getExistsOaUserIds(userIds);
            updateUserIds = dbApiUtil.batchExistsOaUserId(userIds);
            if (CollUtil.isNotEmpty(existsOaUserIds)) {
                members = members.stream().filter(m -> existsOaUserIds.contains(m.getMemberId())).collect(Collectors.toList());
            } else {
                members = ImmutableList.of();
            }
        }

        String xmcy = "";
        if (CollUtil.isNotEmpty(updateUserIds)) {
            xmcy = "" + updateUserIds.stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA)) + "";
        }

        //通过OA数据库修改项目成员无法更新权限，修改为接口更新
        JSONObject mainTable = new JSONObject();
        mainTable.put("xmcy", xmcy);
        oaUtil.doAction(String.valueOf(projectId), mainTable);

        List<Long> syncOaUserIds = members.stream().map(ProjectStakeholderMemberVO::getMemberId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(syncOaUserIds)) {
            mapper.updateSyncOaTypeByProjectId(projectId, syncOaUserIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<ProjectStakeholderMemberBatchDTO> newMemberBatchList) {
        if (CollUtil.isEmpty(newMemberBatchList)) {
            return;
        }
        // 获取所有用户id集合
        Set<Long> userIds = getUserIds(newMemberBatchList);

        // 获取所有用户信息map
        Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(userIds);

        // 获取所有部门信息map
        Map<Long, SysDeptOutVO> deptMap = getDeptMap();

        List<ProjectStakeholderMember> projectStakeholderMemberList = new ArrayList<>();

        for (ProjectStakeholderMemberBatchDTO dto : newMemberBatchList) {
            // 项目id
            Long projectId = dto.getProjectId();
            if (!Optional.ofNullable(projectId).isPresent()) {
                throw new ValidationException(PROJECT_ID_NOT_NULL);
            }
            if (!Optional.ofNullable(dto.getRoleType()).isPresent()) {
                throw new ValidationException("成员角色不能为空");
            }
            List<ProjectStakeholderMemberDTO> memberList = dto.getMembers();

            // 遍历成员信息
            if (CollUtil.isNotEmpty(memberList)){
                for (ProjectStakeholderMemberDTO member : memberList) {
                    Roster roster = userIdMap.get(member.getMemberId());
                    if (Optional.ofNullable(roster).isPresent()) {
                        member.setPosition(roster.getJob());
                        member.setDeptName(buildDeptName(roster.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        member.setProjectId(dto.getProjectId());
                        member.setRoleType(dto.getRoleType());
                        projectStakeholderMemberList.add(ProjectStakeholderMember.saveOrUpdate(member));
                    }
                }
            }
        }

        // 保存项目成员
        if (CollUtil.isNotEmpty(projectStakeholderMemberList)) {
            mapper.batchSync(projectStakeholderMemberList);
        }
        IProjectStakeholderMemberService projectStakeholderMemberService = SpringContextHolder.getBean(IProjectStakeholderMemberService.class);
        for (ProjectStakeholderMemberBatchDTO dto : newMemberBatchList) {
            // 异步同步项目成员到OA
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    projectStakeholderMemberService.asyncOa(dto.getProjectId());
                }
            });
        }
    }

    /**
     * 通过项目 ID 映射获取成员 ID
     *
     * @return {@link Map }<{@link Long }, {@link Set }<{@link Long }>>
     */
    @Override
    public Map<Long, Set<Long>> getMemberIdsByProjectIdMap() {
        List<ProjectStakeholderMemberVO> projectStakeholderMemberVOList = baseMapper.getProjectStakeholderMemberVOList();
        return CollUtil.isNotEmpty(projectStakeholderMemberVOList)
                ? projectStakeholderMemberVOList.stream()
                .collect(Collectors.groupingBy(ProjectStakeholderMemberVO::getId,
                        Collectors.mapping(ProjectStakeholderMemberVO::getMemberId, Collectors.toSet())))
                : Collections.emptyMap();
    }

    @Override
    public List<ProjectStakeholderMemberVO> getList(JSONObject jsonObject) {
        List<ProjectStakeholderMember> dataList = mapper.findList(jsonObject);
        Long projectId = jsonObject.getLong("projectId");
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo == null || CollUtil.isEmpty(dataList)) {
            return ImmutableList.of();
        }
        Map<Long, List<ProjectStakeholderMember>> userIdAndMembersMap = dataList.stream()
                .collect(Collectors.groupingBy(
                        ProjectStakeholderMember::getMemberId, LinkedHashMap::new, Collectors.toList()
                ));

        Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(userIdAndMembersMap.keySet());
        List<ProjectStakeholderMemberVO> vos = new ArrayList<>();
        userIdAndMembersMap.forEach((id, members) ->
                vos.add(getVo(members, projectInfo, userIdMap)));
        return vos;
    }


    @NotNull
    private Map<Long, SysDeptOutVO> getDeptMap() {
        List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        return deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));
    }

    public Set<Long> getUserIds(List<ProjectStakeholderMemberBatchDTO> newMemberBatchList) {
        return newMemberBatchList.stream()
                .flatMap(dto -> dto.getMembers().stream())
                .map(ProjectStakeholderMemberDTO::getMemberId)
                .collect(Collectors.toSet());
    }
}
