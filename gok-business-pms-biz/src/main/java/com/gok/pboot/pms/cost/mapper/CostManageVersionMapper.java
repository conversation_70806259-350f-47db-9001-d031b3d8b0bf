package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVersionIInfoVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 成本管理版本记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostManageVersionMapper extends BaseMapper<CostManageVersion> {

    /**
     * 获取最新成本管理管理版本
     *
     * @param query 查询请求
     * @return
     */
    CostManageVersion getLatestCostManageEstimation(@Param(value = "query") CostManageVersionDTO query);

    /**
     * 获取历史记录版本
     *
     * @param page    页
     * @param request 查询请求
     * @return {@link Page }<{@link com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO }>
     */
    Page<CostManageVersionVO> findPage(@Param("page") Page<CostManageVersionVO> page, @Param("query") CostManageVersionDTO request);

    Page<CostManageTargetVersionIInfoVO> getCostManageTargetVersionInfo(@Param("page") Page<CostManageTargetVersionIInfoVO> page, @Param("projectId") Long projectId);

    /**
     * 根据id修改版本状态
     *
     * @param id            版本ID
     * @param versionStatus {@link com.gok.pboot.pms.cost.enums.VersionStatusEnum}
     */
    void updateVersionStatusById(@Param("id") Long id, @Param("versionStatus") Integer versionStatus);

    /**
     * 根据项目ID查询版本信息
     *
     * @param projectId 项目ID
     * @return
     */
    List<CostManageVersionVO> queryProjectVersionByProjectId(@Param("projectId") Long projectId, @Param("versionStatus") Integer versionStatus);


    /**
     * 获取最新未创建项目成本售前成本集合
     *
     * @return
     */
    List<CostManageVersion> getLatestPreSalesCost();

    /**
     * 获取最新未创建项目总成本信息集合
     *
     * @param projectIds
     * @return
     */
    List<CostManageVersion> getLatestTotalCost(@Param("projectIds") List<Long> projectIds);


    /**
     * 获取最新管理版本
     * 根据创建时间倒序获取
     *
     * @param query 查询请求
     * @return
     */
    CostManageVersion getLatestCostManageVersion(@Param(value = "query") CostManageVersionDTO query);


}
