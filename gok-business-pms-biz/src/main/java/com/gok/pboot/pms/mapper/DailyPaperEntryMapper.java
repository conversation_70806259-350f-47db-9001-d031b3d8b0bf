package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.pms.common.base.MapperHandler;
import com.gok.pboot.pms.cost.entity.vo.TaskDailyPaperDetailVO;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * - 日报条目Mapper -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 15:01
 */
@Mapper
public interface DailyPaperEntryMapper extends MapperHandler<DailyPaperEntry> {
    /**
     * ~ 根据日报ID获取日报条目 ~
     *
     * @param dailyPaperId 日报ID
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/8/24 9:55
     */
    List<DailyPaperEntry> findByDailyPaperId(Long dailyPaperId);

    /**
     * 根据日报ID列表查询日报条目信息
     *
     * @param dailyPaperIds  日报ID列表
     * @param approvalStatus 审核状态
     * @return 日报ID, 日报条目ID, 任务ID
     */
    List<Triple<Long, Long, Long>> findDailyPaperIdAndUserIdAndTaskIdTripleByDailyPaperIdsAndApprovalStatus(
            @Param("dailyPaperIds") Collection<Long> dailyPaperIds,
            @Param("approvalStatus") Integer approvalStatus
    );

    /**
     * ~ 根据日报ID列表和项目名称模糊查询 ~
     *
     * @param dailyPaperIds   日报ID列表
     * @param projectNameLike 项目名称
     * @param approvalStatus  审核状态 {@link com.gok.pboot.pms.enumeration.ApprovalStatusEnum}
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/11/1 15:47
     */
    List<DailyPaperEntry> findByDailyPaperIdsAndProjectNameLike(
            @Param("dailyPaperIds") List<Long> dailyPaperIds,
            @Param("projectNameLike") String projectNameLike,
            @Param("approvalStatus") Integer approvalStatus
    );

    /**
     * ~ 批量插入 ~
     *
     * @param poList 实体列表
     * <AUTHOR>
     * @date 2022/8/24 14:16
     */
    void batchSave(@Param("poList") List<DailyPaperEntry> poList);

    /**
     * ~ 根据日报ID删除 ~
     *
     * @param dailyPaperId 日报ID
     * <AUTHOR>
     * @date 2022/8/24 14:27
     */
    void deleteByDailyPaperId(Long dailyPaperId);

    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<DailyPaperEntry> list);

    /**
     * ~ 项目工时分摊表分页 ~
     *
     * @param allocationFindPageDTO
     * <AUTHOR>
     * @date 2022/8/25
     */
    //Page<AllocationFindPageVO> allocationFindPage2(Page<AllocationFindPageVO> page, @Param("allocationFindPageDTO") AllocationFindPageDTO allocationFindPageDTO, DataScope dataScope);
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = "userId")
    Page<AllocationFindPageVO> allocationFindPage(Page<AllocationFindPageVO> page, @Param("allocationFindPageDTO") AllocationFindPageDTO allocationFindPageDTO);

    /**
     * 项目工时分摊表
     *
     * @param allocationFindPageDTO 请求dto
     * @return {@link List}<{@link AllocationFindPageVO}>
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = "userId")
    List<AllocationFindPageVO> allocationFindAll(@Param("allocationFindPageDTO") AllocationFindPageDTO allocationFindPageDTO);

    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = "userId")
    List<AllocationFindPageVO> allocationFindPage(@Param("allocationFindPageDTO") AllocationFindPageDTO allocationFindPageDTO);

    /**
     * ~ 项目工时汇总分页 ~
     *
     * @param projectHourSumFindPageDTO
     * <AUTHOR>
     * @date 2022/8/25
     */
    Page<ProjectHourSumFindPageVO> projectHourSumFindPage(Page<ProjectHourSumFindPageVO> page, @Param("projectHourSumFindPageDTO") ProjectHourSumFindPageDTO projectHourSumFindPageDTO, @Param("filter") Map<String, Object> filter);

    /**
     * 2023.03.07 因为DataScope的sql拼接有问题，所以备份confirmHourSumFindPageVO这个方法
     */
    @BusinessDataScope
    Page<ConfirmHourSumFindPageVO> confirmHourSumFindPageVO2(Page<ConfirmHourSumFindPageVO> page, @Param("confirmHourSumFindPageDTO") ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO);

    Page<DailyReviewFindPageVO> dailyReviewFindPageVO(
            Page<DailyReviewFindPageVO> page,
            @Param("dailyReviewFindPageDTO") DailyReviewFindPageDTO dailyReviewFindPageDTO
    );

    Page<DailyProjectDetailsFindPageVO> dailyProjectDetailsFindPageVO(Page<DailyProjectDetailsFindPageVO> dailyProjectDetailsFindPageVOPage
            , @Param("dailyProjectDetailsFindPageDTO") DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO);

    Page<DailyProjectDetailsFindPageVO> dailyProjectDetailsFindPage2VO(Page<DailyProjectDetailsFindPageVO> dailyProjectDetailsFindPageVOPage
            , @Param("dailyProjectDetailsFindPageDTO") DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO);

    void updateApprovalStatusById(@Param("changeApprovalStatusDTO") ChangeApprovalStatusDTO changeApprovalStatusDTO);

    void batchApproval(@Param("ids") List<Long> ids, @Param("changeApprovalStatusDTO") ChangeApprovalStatusDTO changeApprovalStatusDTO);

    void batchApprovalStatusById(@Param("dtoList") List<ChangeApprovalStatusDTO> dtoList);

    /**
     * 根据用户ID查询目标用户需要审核的日报条目数量，排除指定的项目ID
     * （计数结果包含特殊项目，如果不想查询特殊项目，需要将项目ID显式指定，将其过滤掉）
     *
     * @param userId 用户ID
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2022/9/27 14:09
     */
    Integer countAuditsByAuditorUserId(
            @Param("userId") Long userId,
            @Nullable @Param("excludeProjectIds") List<Long> projectIds
    );

    /**
     * ~ 查询指定用户ID在对应项目ID列表中需要被审核的项目数 ~
     *
     * @param userIds    用户ID列表
     * @param projectIds 项目ID列表
     * @return java.lang.Integer
     * <AUTHOR>
     */
    Integer countAuditsByUserIdsAndProjectIds(
            @Param("userIds") List<Long> userIds,
            @Param("projectIds") List<Long> projectIds
    );

    /**
     * ~ 根据项目ID列表查询 ~
     *
     * @param projectIds 项目ID列表
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/10/13 15:06
     */
    List<DailyPaperEntry> findByProjectIds(List<Long> projectIds);


    //@BusinessDataScope(deptOrUser = "dept", scopeDeptNameList = "userDeptId")
    List<DailyPaperEntryVO> findByProjectIdsAndSubmissionDateBetween(
            @Param("deptIds") List<Long> deptIds,
            @Param("userIds") List<Long> userIds,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("approvalStatus") Integer approvalStatus,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * ~ 根据项目ID列表和提交时间范围查询条目（条目实体中只含有项目ID和审核状态字段） ~
     *
     * @param projectIds 项目ID列表
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     */
    List<DailyPaperEntry> findProjectIdAndApprovalStatusByProjectIdsAndSubmissionDateBetween(
            @Param("projectIds") List<Long> projectIds,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
     * ~ 查询用于导出的日报条目 ~
     *
     * @param projectIds 项目ID列表
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/11/28 16:04
     */
    List<UnReviewedDailyPaperEntryExcelVO> findForExport(
            @Param("projectIds") List<Long> projectIds,
            @Param("approvalStatus") Integer approvalStatus,
            @Param("dto") ProjectHourSumFindPageDTO dto
    );

    List<DailyPaperEntryExcelVO> queryAllAndWorkCode(@Param("filter") Map<String, Object> filter);

    /**
     * ~ 【工时统计】根据oaId列表、项目id列表、开始、结束日期查询“已通过”日报条目 ~
     *
     * @param userIds    oaId列表
     * @param projectIds 项目ID列表
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param taskId     任务名称，逻辑太乱，无法整成对象，需要重构
     * <AUTHOR>
     * @date 2022/10/17 15:12
     */
    Page<DailyPaperEntryVO> findByUserIdsAndProjectIdsAndDateRange(
            Page<DailyPaperEntryVO> page,
            @Param("userIds") List<Long> userIds,
            @Param("projectIds") Collection<Long> projectIds,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("taskId") List<String> taskId,
            @Param("deptIds") List<Long> deptIds,
            @Param("personnelStatus") Integer personnelStatus,
            @Param("dto") WorkHourStatisticsFindPageDTO dto
    );

    /**
     * ~ 【工时统计】根据oaId列表、项目id列表、开始、结束日期查询“已通过”日报条目 ~
     *
     * @param userIds    oaId列表
     * @param projectIds 项目ID列表
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param taskId     任务名称，逻辑太乱，无法整成对象，需要重构
     * <AUTHOR>
     * @date 2022/10/25 15:52
     */
    BigDecimal sumHoursByUserIdsAndProjectIdsAndDateRange(
            @Param("userIds") List<Long> userIds,
            @Param("projectIds") Collection<Long> projectIds,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("taskId") List<String> taskId,
            @Param("deptIds") List<Long> deptIds,
            @Param("personnelStatus") Integer personnelStatus,
            @Param("dto") WorkHourStatisticsFindPageDTO dto
    );

    /**
     * ~ 根据工时汇总页的审核状态查询项目ID ~
     *
     * @param approvalStatusTag 1=查询所有待审核，2=查询所有非“已完成”
     * @return java.util.List<java.lang.Long> 项目ID列表
     * <AUTHOR>
     */
    Set<Long> findProjectIdsByApprovalStatusTagForProjectHourSum(
            @Param("tag") Integer approvalStatusTag,
            @Param("dto") ProjectHourSumFindPageDTO dto
    );

    Set<Long> findProjectIdSetByApprovalStatusForProjectHourSum(
            @Param("approvalStatus") Integer approvalStatus,
            @Param("dto") ProjectHourSumFindPageDTO dto
    );

    /**
     * 分页查询 日报详情
     *
     * @param filter       查询条件
     * @param childUserIds 直属下级用户id
     * @param page         分页参数
     * @return {@link Page<DailyReviewProjectAuditPageVO>}
     */
    Page<DailyReviewProjectAuditPageVO> selectPaperView(@Param("filter") DailyReviewPageDTO filter, @Param("childUserIds") ArrayList<Long> childUserIds, Page page);

    List<DailyPaperEntry> findByDailyPaperIds(@Param("list") List<Long> dailyPaperIds, @Param("startDate") LocalDate startTime, @Param("endDate") LocalDate endTime);

    List<DailyReviewFindPageVO> findUnAuditDailyByTime(@Param("startDate") LocalDate startTime, @Param("endDate") LocalDate endTime);


    /**
     * 根据id查询日报信息
     *
     * @param id id
     * @return 日报信息
     */
    DailyPaperCommonDTO selectDailyPaperById(Long id);

    /**
     * 按时间范围和项目名称查询该用户的所有项目工时数据
     *
     * @param filter 查询条件
     * @param page   分页参数
     * @return {@link Page<PanelProjectSituationVO>}
     */
    Page<PanelProjectSituationVO> selectPageByUserId(@Param("filter") PanelRequestDTO filter, Page<Object> page);

    /**
     * 按时间范围和项目名称查询该用户的指定项目工时详情
     *
     * @param filter 查询条件
     * @return {@link Page<PanelProjectSituationDetailsVO>}
     */
    Page<PanelProjectSituationDetailsVO> selectPanelProjectSituationDetailsPage(@Param("filter") PanelRequestDTO filter, Page<Object> of);

    /**
     * 按条件查询个人工时饱和度分析数据
     *
     * @param filter 查询条件
     * @return {@link Page<PanelProjectSituationAnalysisVO>}
     */
    Page<PanelProjectSituationAnalysisVO> selectPageProjectSituationAnalysis(@Param("filter") PanelRequestDTO filter, Page<Object> page);

    /**
     * 按条件查询工时饱和度分析数据
     *
     * @param filter     查询条件
     * @param userIds    用户ID列表
     * @param projectIds 项目ID列表
     * @return {@link Page<PanelProjectSituationAnalysisVO>}
     */

    Page<SituationAnalysisVO> selectPageSituationAnalysis(
            @Param("filter") SaturationStatisticsDTO filter,
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds,
            Page<SituationAnalysisVO> page
    );

    /**
     * 根据用户ID列表和条件查询工时饱和度分析数据，额外根据部门ID分组
     *
     * @param filter  查询条件
     * @param userIds 用户ID列表
     * @return 数据列表
     */
    List<SituationAnalysisVO> selectSituationAnalysisMultiDept(
            @Param("filter") SaturationStatisticsDTO filter,
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * 按条件查询工时饱和度分析数据
     *
     * @param filter 查询条件
     * @return {@link Page<PanelProjectSituationAnalysisVO>}
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SituationAnalysisVO> selectPageSituationAnalysis(
            @Param("filter") SaturationStatisticsDTO filter,
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * 按条件查询工时饱和度分析数据
     *
     * @param filter     查询条件
     * @param userIds    用户ID列表
     * @param projectIds 项目ID列表
     * @return {@link Page<PanelProjectSituationAnalysisVO>}
     */

    List<SituationAnalysisDeptVO> selectListSituationAnalysis(
            @Param("filter") SaturationStatisticsDTO filter,
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * 查询日报对应日期的归档信息
     *
     * @param dailyPaperIds 日报条目id
     * @return 归档信息<日报条目id ， 填报日期 ， 归档状态>
     */
    List<DailyPaperEntryFilingDTO> selectFilingByDailyPaperIds(@Param("ids") Collection<Long> dailyPaperIds);

    /**
     * 按日期统计未审核数
     *
     * @param projectIds     项目id
     * @param projectUserIds 用户id
     * @return 未审核数
     */
    int countUnauditedNumber(@Param("projectIds") List<Long> projectIds, @Param("projectUserIds") List<Long> projectUserIds);

    /**
     * 统计个人面板-饱和度分析-总计
     *
     * @param filter 查询条件
     */
    PanelProjectSituationAnalysisVO selectProjectSituationAnalysis(@Param("filter") PanelRequestDTO filter);

    /**
     * ~ 项目工时汇总分页(V2) ~
     *
     * @param objectPage
     * @param projectHourSumFindPageDTO
     * @param showUnreviewed            ture待审核，flase已审核
     * @return
     */
    Page<ProjectHourSumFindPageVO> projectHourSumFindPageV2(Page objectPage, @Param("projectHourSumFindPageDTO") ProjectHourSumFindPageDTO projectHourSumFindPageDTO, @Param("showUnreviewed") Boolean showUnreviewed);


    /**
     * 获取用户有提交日报的特殊项目id
     *
     * @param filter               特殊项目id集合,用户id集合
     * @param allSpecialProjectIds 全部特殊项目
     * @param subUserIds           用户id
     * @return 特殊项目id
     */
    List<DailyReviewPageVO> specialProjectsContainsUserId(@Param("filter") DailyReviewPageDTO filter, @Param("specialProjectIds") List<Long> allSpecialProjectIds, @Param("userIds") List<Long> subUserIds);

    /**
     * 上级获取无审核权限的下级提交日报项目的人员
     *
     * @param filter          特殊项目id集合,用户id集合
     * @param childProjectIds 下级无审核权限项目
     * @return 特殊项目id
     */
    List<DailyReviewPageVO> notEditProjectUserId(@Param("filter") DailyReviewPageDTO filter, @Param("childProjectIds") List<Long> childProjectIds);

    /**
     * @create by yzs at 2023/5/11
     * @description:查询下级的日报详细
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    @InterceptorIgnore(tenantLine = "true")
    Page<DailyReviewProjectAuditPageVO> findBySubordinate(Page page, @Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * @create by yzs at 2023/5/11
     * @description:统计下级 工时
     * @param: dto
     * @return: com.gok.pboot.pms.entity.vo.SubordinatePaperEntryStaticVO
     */
    List<SubordinatePaperEntryStaticVO> findBySubordinateStatic(@Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * @create by yzs at 2023/5/12
     * @description:个人面板查询日报详情
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    @InterceptorIgnore(tenantLine = "true")
    Page<DailPanelPaperPageVO> findByPanel(Page page, @Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * 统计个人面板-日报详细-总计
     *
     * @param dto 查询条件 startTime，endTime，projectName
     * @return {@link PanelProjectSituationAnalysisVO}
     */
    PanelProjectSituationAnalysisVO panelSituationAnalysis(@Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * @create by yzs at 2023/5/15
     * @description: 工时饱和度审核工时总数
     * @param: dto
     * @return: com.gok.pboot.pms.common.base.R<com.gok.pboot.pms.entity.vo.PaneSaturationAnalysisVO>
     */
    BigDecimal saturationAnalysisBySubmissionDateRangeAndDeptIdsAndUserIdsAndProjectIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("deptIds") Collection<Long> deptIds,
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * @create by yzs at 2023/5/11
     * @description: 工时饱和度查询滞后日报和审核日报详情
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    @InterceptorIgnore(tenantLine = "true")
    Page<DailyReviewProjectAuditPageVO> findBySaturation(
            Page page,
            @Param("filter") DailyPaperAnalysisDTO dto,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * @create by yzs at 2023/5/15
     * @description: 工时饱和度审核工时总数+滞后提交人天总数的详细数据查看导出
     * @param: dto
     * @param: response
     * @return: void
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SaturationExportVO> exportBySaturation(
            @Param("filter") DailyPaperAnalysisDTO dto,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * 查询被退回的日报详细
     *
     * @param paperIds 日报id
     * @create by yzs at 2023/5/22
     * @return: java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    List<DailyPaperEntry> findReturnEntry(@Param("paperIds") List<Long> paperIds);


    /**
     * 工时饱和度统计非收入型项目工时
     *
     * @param saturationStatisticsDTO
     * @create by yzs at 2023/5/31
     * @return: java.util.List<com.gok.pboot.pms.entity.vo.SituationAnalysisDeptVO>
     */
    List<SituationAnalysisDeptVO> situationInSide(@Param("filter") SaturationStatisticsDTO saturationStatisticsDTO);


    List<DailyPaperEntry> findByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 通过任务id列表获取以通过的日报
     *
     * @param taskIds 任务id列表
     * @return {@link List}<{@link DailyPaperEntry}>
     */
    List<DailyPaperEntry> findPassedByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 根据任务ID列表、日期范围、审核状态查询
     *
     * @param taskIds        任务ID列表
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param approvalStatus 审核状态
     * @return 日报列表
     */
    List<DailyPaperEntry> findByTaskIdsAndDateRangeAndApprovalStatus(
            @Param("taskIds") Collection<Long> taskIds,
            @Nullable @Param("startDate") LocalDate startDate,
            @Nullable @Param("endDate") LocalDate endDate,
            @Nullable @Param("approvalStatus") Integer approvalStatus
    );

    /**
     * 查询数据
     *
     * @param filter 过滤条件
     * @return 数据列表
     */
    List<DailyPaperEntry> findList(@Param("filter") Map<String, Object> filter);

    Boolean existsByTaskIds(Long id);

    /**
     * 是否存在未审核日报不能删除
     *
     * @param taskId 任务id
     * @return {@link Boolean}
     */
    Boolean isExistsUnauditedByTaskIdForDel(@Param("taskId") Long taskId);

    /**
     * 是否存在挂靠工时
     *
     * @param taskId 任务id
     * @return {@link Boolean}
     */
    Boolean isExistsAttachedHours(@Param("taskId") Long taskId);

    /**
     * 寻找可以删除的任务id列表
     *
     * @param taskIds 任务id列表
     * @return {@link List}<{@link Long}>
     */
    List<Long> findCantDelTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 获取填报日报得用户id集合
     *
     * @return 用户ID集合
     */
    List<Long> findUserIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("approvalStatus") Integer approvalStatus,
            @Nullable @Param("deptIds") Collection<Long> deptIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * 通过任务id判断是否还存在未审核日报
     *
     * @param taskId 任务id
     * @return {@link Boolean}
     */
    Boolean isExistsUnauditedByTaskId(@Param("taskId") Long taskId);

    /**
     * 通过任务id获取最后一条日报填报日期
     *
     * @param taskId 任务id
     * @return {@link LocalDate}
     */
    LocalDate findFinalDailyDateByTaskId(@Param("taskId") Long taskId);

    /**
     * 通过任务id获取最后一条日报填报日期
     *
     * @param taskId 任务id
     * @return {@link LocalDate}
     */
    LocalDate findFinalPassedDailyDateByTaskId(@Param("taskId") Long taskId);

    /**
     * 通过任务id获取最早一条日报填报日期
     *
     * @param taskId 任务id
     * @return {@link LocalDate}
     */
    LocalDate findFirstDailyDateByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据用户id、日期范围和审核状态获取任务id集合
     *
     * @param userIds            用户ID集合
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param approvalStatusList 审核状态列表
     * @return 任务ID集合
     */
    List<Long> findTaskIdByUserIdsAndDateRangeAndApprovalStatus(
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("startDate") LocalDate startDate,
            @Nullable @Param("endDate") LocalDate endDate,
            @Nullable @Param("approvalStatusList") Collection<Integer> approvalStatusList
    );

    /**
     * 更新任务名称
     *
     * @param taskId   任务id
     * @param taskName 任务名称
     * @return int
     */
    int updateTaskNameByTaskId(@Param("taskId") Long taskId, @Param("taskName") String taskName);

    /**
     * 获取待审核日报
     *
     * @param taskIds 任务id列表
     * @return {@link List}<{@link DailyPaperEntry}>
     */
    List<DailyPaperEntry> findDSHByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 获取待审核日报
     *
     * @param taskIds 任务id列表
     * @param page    分页对象
     * @return {@link List}<{@link DailyPaperEntry}>
     */
    Page<DailyPaperEntry> findDSHPageByTaskIds(Page<DailyPaperEntry> page, @Param("taskIds") List<Long> taskIds);

    /**
     * 获取无效日报
     *
     * @param taskIds 任务id列表
     * @return {@link List}<{@link DailyPaperEntry}>
     */
    List<DailyPaperEntry> findInvalidByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 获取无效日报
     *
     * @param taskIds 任务id列表
     * @param page    分页对象
     * @return {@link List}<{@link DailyPaperEntry}>
     */
    Page<DailyPaperEntry> findInvalidPageByTaskIds(Page<DailyPaperEntry> page, @Param("taskIds") List<Long> taskIds);

    LocalDate findFirstSqByTaskId(@Param("taskId") Long taskId);

    LocalDate findFirstShByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据日期范围、用户ID列表、项目ID列表查询部门ID列表
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param approvalStatus 审核状态
     * @param userIds        用户ID列表
     * @param projectIds     项目ID列表
     * @return 部门ID列表
     */
    List<Pair<Long, Long>> findDeptIdsBySubmissionDateRangeAndApprovalStatusAndUserIdsAndProjectIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("approvalStatus") Integer approvalStatus,
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * 根据项目ID集合查询项目工时分摊
     * 为项目测算查询提供工时数据
     *
     * @param projectIds
     * @return
     */
    List<AllocationFindPageVO> allocationForCalculation(@Param("projectIds") List<Long> projectIds,
                                                        @Param("workCodes") List<String> workCodes,
                                                        @Param("memberNames") List<String> memberNames);


    /**
     * 按工单ID 查询 工时条目
     *
     * @param costTaskIds 成本任务 ID
     * @return {@link List }<{@link DailyPaperEntry }>
     */
    List<DailyPaperEntryCostTaskDTO> getPaperEntryListByCostTaskIds(@Param("costTaskIds") Collection<Long> costTaskIds);

    /**
     * 根据工单ID查询日报详情
     *
     * @param page           分页参数
     * @param costTaskId         工单ID
     * @param approvalStatus 审批状态
     * @return 日报详情列表
     */
    Page<TaskDailyPaperDetailVO> findDailyDetailsByCostTaskId(@Param("page") Page<TaskDailyPaperDetailVO> page,
                                                              @Param("costTaskId") Long costTaskId,
                                                              @Param("approvalStatus") Integer approvalStatus);

    /**
     * 找到日常详由成本任务id
     *
     * @param taskId         任务id
     * @param approvalStatus 审批状态
     * @return {@link List }<{@link TaskDailyPaperDetailVO }>
     */
    List<TaskDailyPaperDetailVO> findDailyDetailsByCostTaskId(@Param("costTaskId") Long taskId,
                                                              @Param("approvalStatus") Integer approvalStatus);
    /**
     * 按日报条目任务ID 查询 工时条目
     *
     * @param costTaskIds 成本任务 ID
     * @return {@link List }<{@link DailyPaperEntry }>
     */
    List<DailyPaperEntryCostTaskDTO> getPaperEntryListByTaskIds(@Param("costTaskIds") Collection<Long> costTaskIds);

}
