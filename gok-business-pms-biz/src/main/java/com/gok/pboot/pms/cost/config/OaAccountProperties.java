package com.gok.pboot.pms.cost.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OaAccountProperties {

    /**
     * 销售费用
     */
    @Value("${oa.xsfyOaId:62}")
    private Long xsfyOaId;

    /**
     * 项目管理费用
     */
    @Value("${oa.xmglfysqrgOaId:8102}")
    private Long xmglfysqrgOaId;

    /**
     * 主营业务成本-直接人工
     */
    @Value("${oa.mainBusinessCostsDirectLabor:99}")
    private Long mainBusinessCostsDirectLabor;

    /**
     * 主营业务成本-直接人工-税率
     */
    @Value("${oa.mainBusinessCostsDirectLaborTaxRate:5}")
    private Integer mainBusinessCostsDirectLaborTaxRate;
}
