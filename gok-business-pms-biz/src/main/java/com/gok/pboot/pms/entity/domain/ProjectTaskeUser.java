package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.enumeration.ProjectTaskRoleEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 任务-用户关联表
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@Data
@TableName("project_taske_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ProjectTaskeUser extends BeanEntity<Long> {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 任务角色（0=负责人，1=参与人）
     * @see ProjectTaskRoleEnum#getValue()
     */
    private Integer taskRole;

    /**
     * 部门名
     */
    private String deptName;

    /**
     * 岗位/职位
     */
    private String position;

}
