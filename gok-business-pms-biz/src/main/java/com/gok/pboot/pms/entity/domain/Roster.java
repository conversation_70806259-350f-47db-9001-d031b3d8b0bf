package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * @<NAME_EMAIL>
 * @date 2022/5/17 10:25
 */
@Data
@Builder
@TableName(Roster.ALIAS)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class Roster {
    public static final String ALIAS = "mhour_roster";
    /**
     * id
     */
    private Long id;
    /**
     * OA表主键
     */
    private Long oaId;
    /**
     * OA工号
     */
    private String workCode;
    /**
     * oa部门id
     */
    private Long deptId;
    /**
     * 姓名
     */
    private String aliasName;
    /**
     * 身份证号
     */
    private String idCardNo;
    /**
     * 人员类型（0=在编人员，1=正式交付人员，2=实习交付人员，3=退休返聘人员，4=非全日制人员，5=内部实习生，6=兼职）
     */
    private Integer employeeType;
    /**
     * 人员状态
     * @see com.gok.pboot.pms.enumeration.EmployeeStatusEnum
     */
    private Integer employeeStatus;
    /**
     * 人员职位
     */
    private String job;
    /**
     * 入职日期
     */
    private LocalDate startDate;
    /**
     * 离职日期
     */
    private LocalDate endDate;

//    /**
//     * 直属上级用户ID
    // * 不兼容EHR虚拟上级 已经弃用
//     */
//    private Long leaderId;

}
