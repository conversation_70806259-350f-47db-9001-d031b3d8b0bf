package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@AllArgsConstructor
@Getter
public enum OrderTypeEnum {

    /**
     * 机票订单
     */
    FLIGHT_TICKET("FLIGHT_TICKET", "机票订单", "TRAVEL", "滴滴商旅平台差旅费用-交通费", "北京小桔国际旅行社有限公司", "招商银行股份有限公司北京东三环支行", "1109424354107014000523740"),

    /**
     * 酒店订单
     */
    HOTEL("HOTEL", "酒店订单", "TRAVEL", "滴滴商旅平台差旅费用-住宿费", "北京小桔国际旅行社有限公司", "招商银行股份有限公司北京东三环支行", "1109424354107014000523740"),

    /**
     * 用车订单
     */
    VEHICLE("VEHICLE", "用车订单", "CAR", "滴滴商旅平台差旅费用-市内交通费", "滴滴出行科技有限公司", "招商银行股份有限公司北京东三环支行", "1229059399101080000208755");

    private final String code;
    private final String name;
    //订单类型：用车、商旅
    private final String type;
    //费用摘要
    private final String expenseSummary;
    //对方名称
    private final String dfmc;
    //开户行
    private final String khx;
    //银行账号
    private final String yxzh;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static OrderTypeEnum getByCode(String code) {
        for (OrderTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 