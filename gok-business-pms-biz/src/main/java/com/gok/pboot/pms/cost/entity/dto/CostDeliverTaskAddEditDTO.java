package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.enumeration.OperateEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 成本 交付任务 添加编辑 DTO
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
public class CostDeliverTaskAddEditDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 项目ID
     */
    private Long projectId;


    /**
     * 项目阶段
     */
    @NotBlank(message = "项目阶段不能为空")
    private String stage;

    /**
     * 工单名称
     */
    @NotBlank(message = "工单名称不能为空")
    @Length(max = 25, message = "工单名称不能超过${max}个字符")
    private String taskName;


    /**
     * 工单类型（0=售前支撑，1=售后交付）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    /**
     * 工单描述
     */
    @Length(max = 200, message = "工单描述不能超过${max}个字符")
    private String taskDesc;

    /**
     * OA成本科目ID
     */
    @NotNull(message = "OA成本科目ID不能为空")
    private Long accountOaId;


    /**
     * 税率OA字典ID
     */
    private Integer taxRate;

    /**
     * 预算成本
     */
    @NotNull(message = "预算成本不能为空")
    private BigDecimal budgetCost;

    ///**
    // * 拆解类型（0=标准工单，1=总成工单）
    // *
    // * @see com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum
    // */
    //@NotNull(message = "拆解类型不能为空")
    //private Integer disassemblyType;

    /**
     * 工单负责人ID
     */
    @NotNull(message = "工单负责人不能为空")
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    @NotNull(message = "负责人姓名不能为空")
    private String managerName;

    /**
     * 开始时间
     */
    @NotNull(message = "起止日期不能为空")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @NotNull(message = "起止日期不能为空")
    private LocalDate endDate;

    /**
     * 工单级别
     */
    private Integer taskLevel;

    /**
     * 工单状态
     *
     * @see CostTaskStatusEnum#getValue()
     */
    private Integer taskStatus;

    /**
     * 操作枚举
     *
     * @see OperateEnum
     */
    private OperateEnum operateEnum;

    /**
     * 工单类别（来自工单类别管理）
     */
    @NotNull(message = "工单类别不能为空")
    private Integer taskCategory;

    /**
     * 预计工时(时)
     */
    private BigDecimal estimatedHours;

    ///**
    // * 产值
    // */
    //@NotNull(message = "产值不能为空")
    //private BigDecimal income;

}
