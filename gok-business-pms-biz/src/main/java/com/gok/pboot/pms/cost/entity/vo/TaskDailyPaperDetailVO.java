package com.gok.pboot.pms.cost.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工单日报详情VO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
public class TaskDailyPaperDetailVO {

    private Long dailyPaperEntryId;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 日期类型 0-普通休息日 1-法定节假日 null-工作日
     */
    private Integer holidayType;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作内容描述
     */
    private String description;

    /**
     * 日报状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 填报状态（0=正常，1=滞后）
     */
    private Integer fillingState;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submissionTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    private String actualLaborCost;

    /**
     * 工时类型
     */
    private Integer workType;

    private String workTypeName;
} 