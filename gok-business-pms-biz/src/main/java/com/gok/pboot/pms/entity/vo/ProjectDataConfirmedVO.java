package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.gok.pboot.pms.enumeration.ProjectOperationRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目质保/关闭确认数据VO类
 *
 * <AUTHOR>
 * @create 2025/07/03
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDataConfirmedVO {

    /**
     * ID
     */
    private String id;

    /**
     * 计划签署合同数量
     */
    private Integer planContractNum;

    /**
     * 已签合同数量
     */
    private Integer signedContractNum;

    /**
     * 计划签署合同金额(含税)
     */
    private BigDecimal planContractAmountIncludeTax;

    /**
     * 已签合同金额(含税)
     */
    private BigDecimal signedContractAmountIncludeTax;

    /**
     * 计划总产值
     */
    private BigDecimal planTotalOutputValue;

    /**
     * 实际总产值
     */
    private BigDecimal actualTotalOutputValue;

    /**
     * 偏差说明
     */
    private String deviationExplanation;

    /**
     * B表收入总额
     */
    @JsonProperty("bTableRevenueTotal")
    private BigDecimal bTableRevenueTotal;

    /**
     * 当前收入总额
     */
    private BigDecimal currentRevenueTotal;

    /**
     * B表成本总额
     */
    @JsonProperty("bTableCostTotal")
    private BigDecimal bTableCostTotal;

    /**
     * 当前成本总额
     */
    private BigDecimal currentCostTotal;

    /**
     * B表毛利
     */
    @JsonProperty("bTableGrossProfit")
    private BigDecimal bTableGrossProfit;

    /**
     * 当前毛利
     */
    private BigDecimal currentGrossProfit;

    /**
     * B表毛利率
     */
    @JsonProperty("bTableGrossProfitRate")
    private BigDecimal bTableGrossProfitRate;

    /**
     * 当前毛利率
     */
    private BigDecimal currentGrossProfitRate;

    /**
     * 成本偏差和毛利偏差说明
     */
    private String costGrossDeviationExplanation;

    /**
     * 已验收金额(含税)
     */
    private BigDecimal acceptedAmountIncludeTax;

    /**
     * 已回款金额(含税)
     */
    private BigDecimal receivedAmountIncludeTax;

    /**
     * 待回款金额(含税)
     */
    private BigDecimal pendingAmountIncludeTax;

    /**
     * 坏账
     */
    private BigDecimal badDebt;

    /**
     * 应付现金含税
     */
    private BigDecimal payableCashTax;

    /**
     * 已付现金含税
     */
    private BigDecimal paidCashTax;

    /**
     * 代付现金含税
     */
    private BigDecimal agentPaidCashTax;

    /**
     * 现金流净额_权责发生制
     */
    private BigDecimal cashFlowNetAccrual;

    /**
     * 现金流净额_收付实现制
     */
    private BigDecimal cashFlowNetCashBasis;

    /**
     * 待回款说明
     */
    private String pendingAmountExplanation;

    /**
     * 现金流说明
     */
    private String cashExplanation;

    /**
     * 坏账说明
     */
    private String badDebtExplanation;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 验收日期
     */
    private LocalDate acceptanceDate;

    /**
     * 质保开始日期
     */
    private LocalDate warrantyPeriodStartDate;

    /**
     * 质保月数
     */
    private String warrantyMonths;

    /**
     * 质保结束日期
     */
    private LocalDate warrantyPeriodEndDate;

    /**
     * 项目后续计划集合
     */
    private List<ProjectFollowupPlanVO> projectFollowupPlanList;

    /**
     * 当前用户角色集合
     * {@link ProjectOperationRoleEnum}
     */
    private List<Integer> currentUserRole;

    /**
     * 质保期预估工时
     */
    private BigDecimal warrantyEstimatedHours;

    /**
     * 质保期预算直接人工成本
     */
    private BigDecimal warrantyEstimatedDirectLaborCost;

    /**
     * 剩余预算
     */
    private BigDecimal remainBudget;

}
