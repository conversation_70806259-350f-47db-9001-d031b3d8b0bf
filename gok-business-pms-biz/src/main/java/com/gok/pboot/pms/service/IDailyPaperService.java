package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostTaskDailyDetailVO;
import com.gok.pboot.pms.cost.entity.vo.DailyPaperTotalVO;
import com.gok.pboot.pms.cost.entity.vo.TaskDailyPaperDetailVO;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.dto.DailyPaperAddOrUpdateDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperMonthlyDTO;
import com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO;
import com.gok.pboot.pms.entity.vo.DailyPaperVO;
import com.gok.pboot.pms.entity.vo.DailyPapersStatisticMonthlyVO;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * - 日报服务类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 15:20
 */
public interface IDailyPaperService {
    /***
     * ~ 分页查询 ~
     * @param pageRequest 分页对象
     * @param filter 查询条件
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaper>
     * <AUTHOR>
     * @date 2022/8/23 15:22
     */
    Page<DailyPaper> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * ~ 根据ID查询 ~
     * @param id id
     * @return com.gok.pboot.pms.entity.DailyPaper
     * <AUTHOR>
     * @date 2022/8/23 15:23
     */
    DailyPaper getById(Long id);

    /**
     * ~ 根据ID查询详情 ~
     * @param id id
     * @return com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO
     * <AUTHOR>
     * @date 2022/8/25 9:43
     */
    DailyPaperDetailsVO getDetailsById(Long id);

    /**
     * ~ 根据日期查询详情，如果传入的日期为节假日，则前推至工作日 ~
     * @param date date
     * @return com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO
     * <AUTHOR>
     * @date 2022/9/5 10:42
     */
    DailyPaperDetailsVO getDetailsByDateForCurrentUser(LocalDate date);

    /**
     * ~ 根据日期查询详情 ~
     * @param date 日期
     * @return com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO
     * <AUTHOR>
     * @date 2022/9/19 14:23
     */
    DailyPaperDetailsVO getDetailsByDateForCurrentUserAccurate(LocalDate date);

    /**
     * ~ 添加/编辑 日报 ~
     * @param request 请求实体
     * <AUTHOR>
     * @date 2022/8/24 9:58
     */
    void newAddOrUpdate(DailyPaperAddOrUpdateDTO request);

    /**
     * ~ 按指定的日期列出当月日报 ~
     * @param request 请求实体
     * @return java.util.List<com.gok.pboot.pms.entity.vo.DailyPaperVO>
     * <AUTHOR>
     * @date 2022/8/26 14:41
     */
    List<DailyPaperVO> listDailyPaperMonthly(DailyPaperMonthlyDTO request);

    /**
     * ~ 统计指定日期月份的日报填报数据 ~
     * @param request 请求参数
     * @return com.gok.pboot.pms.entity.vo.DailyPapersStatisticMonthlyVO
     *
     * <AUTHOR>
     */
    DailyPapersStatisticMonthlyVO getStatisticMonthly(DailyPaperMonthlyDTO request);

    /**
     * ~ 检查条目，如果需要，更新日报审核状态 ~
     * @param dailyPaperIds 日报ID列表
     * @param targetStatus 目标状态
     * <AUTHOR>
     * @date 2022/9/7 17:26
     */
    void updateApprovalStatusIfNeeded(List<Long> dailyPaperIds, ApprovalStatusEnum targetStatus);

    /**
     * ~ 自动提交指定日期前一天的日报的调度任务触发 ~
     * <AUTHOR>
     * @param now 日期 yyyy-MM-dd
     * @date 2022/9/16 14:39
     */
    void autoSubmitJob(String now);

    CostTaskDailyDetailVO taskDailyPaperPage(PageRequest pageRequest, Long costTaskId, Integer approvalStatus);

    Page<TaskDailyPaperDetailVO> getDailyDetailPageByTaskId(PageRequest pageRequest, Long costTaskId, Integer approvalStatus);

    List<Long> getEndTaskIds(List<Long> taskIds);


    DailyPaperTotalVO dailyDetailsTotalVO(Long costTaskId, Integer approvalStatus);
}
