package com.gok.pboot.pms.common;

/**
 * Redis
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
public class RedisConstant {

    private RedisConstant() {}

    public static final String APPLICATION_CODE = "PMS";

    /**
     * 项目回款单据编号
     */
    public static final String SETTLEMENT_DETAILS_NUMBER = APPLICATION_CODE + ":DETAILS_NUMBER:SETTLEMENT";

    public static final String SETTLEMENT_NUMBER = APPLICATION_CODE + ":NUMBER:SETTLEMENT";

    public static final String SETTLEMENT_DETAILS_NUMBER_PREFIX = "JSMX";

    public static final String SETTLEMENT_NUMBER_PREFIX = "JSD";


    /**
     * 工单编号锁
     */
    public static final String COST_TASK_NO_LOCK = "cost_task_no_lock:";

    /**
     * 工单编号序列
     */
    public static final String COST_TASK_NO_SEQ = "cost_task_no_seq:";

    /**
     * 工单拆解草稿
     */
    public static final String COST_TASK_DECOMPOSITION_DRAFT = "cost_task_decomposition_draft:";

    public static final String COST_TASK_DRAFT = "cost_task_draft:";

}
