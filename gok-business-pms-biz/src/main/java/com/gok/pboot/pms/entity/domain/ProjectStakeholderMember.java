package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberDTO;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import lombok.*;

/**
 * 项目干系人-成员
 *
 * <AUTHOR>
 * @LocalDateTime 2023-07-11 17:05:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("project_stakeholder_member")
public class ProjectStakeholderMember extends BeanEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 成员id
     */
    private Long memberId;
    /**
     * 成员名
     */
    private String memberName;
    /**
     * 部门名
     */
    private String deptName;
    /**
     * 岗位/职位
     */
    private String position;

    /**
     * 角色类型（0=项目销售经理，1=项目售前经理，2=项目经理，3=项目操作助理，4=项目成员）
     * @link com.gok.pboot.pms.enumeration.RoleTypeEnum
     */
    private Integer roleType;

    /**
     *
     * @link com.gok.pboot.pms.enumeration.RoleTypeEnum
     */
    @TableField(exist = false)
    private Integer sortOrder;
    /**
     * 职责
     */
    private String duty;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String remark;

    /**
     * 是否同步OA（0=否，1=是，2=关键角色默认同步OA）
     * @link com.gok.pboot.enumeration.entity.YesOrNoEnum
     */
    private Integer syncOaType;

    /**
     * 项目干系人创建或更新
     *
     * @param request 项目干系人-成员dto
     * @return {@link ProjectStakeholderMember}
     */
    public static ProjectStakeholderMember saveOrUpdate(ProjectStakeholderMemberDTO request) {
        ProjectStakeholderMember result = new ProjectStakeholderMember();
        if (request.getId() != null) {
            result.setId(request.getId());
            BaseBuildEntityUtil.buildUpdate(result);
        } else {
            result = BaseBuildEntityUtil.buildSave(result);
        }
        result.setProjectId(request.getProjectId());
        result.setMemberId(request.getMemberId());
        result.setMemberName(request.getMemberName());
        result.setPosition(request.getPosition());
        result.setDuty(request.getDuty());
        result.setRemark(request.getRemark());
        result.setDeptName(request.getDeptName());
        result.setRoleType(request.getRoleType());
        result.setSyncOaType(YesOrNoEnum.NO.getValue());
        return result;
    }


}
