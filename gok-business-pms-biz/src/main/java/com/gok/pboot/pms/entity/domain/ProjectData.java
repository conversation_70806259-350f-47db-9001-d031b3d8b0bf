package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 项目核心数据
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Data
@TableName("project_data")
@EqualsAndHashCode(callSuper = true)
public class ProjectData extends BaseEntity<Long> {

    /**
     * 预算总收入（含税）
     */
    private BigDecimal totalBudgetRevenueIncludeTax;

    /**
     * 预估收入总额(不含税)
     */
    private BigDecimal totalBudgetRevenue;

    /**
     * 预算总成本（含税）
     */
    private BigDecimal totalBudgetCostIncludeTax;

    /**
     * 预估成本总额(不含税)
     */
    private BigDecimal totalBudgetCost;

    /**
     * 实际总成本（含税）
     */
    private BigDecimal actualTotalBudgetCostIncludeTax;

    /**
     * 实际总成本（不含税）
     */
    private BigDecimal actualTotalBudgetCost;

    /**
     * 预估总人天
     */
    private String estimatedTotalManDays;

    /**
     * 销售合同金额（含税）
     */
    private BigDecimal contractPrice;

    /**
     * 管理费用
     */
    private BigDecimal managementCost;

    /**
     * 销售费用
     */
    private BigDecimal salesCost;

    /**
     * 已发生人天
     */
    private BigDecimal manDays;

    /**
     * 售前已发生人天
     */
    private BigDecimal beforeSalesManDays;

    /**
     * 售后已发生人天
     */
    private BigDecimal afterSalesManDays;

    /**
     * 学员人天
     */
    private BigDecimal studentManDay;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 售前人工投入
     */
    private BigDecimal preSalesLaborInput;

    /**
     * 售前费用投入
     */
    private BigDecimal sqfytr;

    /**
     * 内部项目预算总成本
     */
    private BigDecimal internalTotalBudgetCostIncludeTax;

    /**
     * 内部项目预估总人天
     */
    private String internalEstimatedTotalManDays;

    /**
     * 内部项目人工预算
     */
    private BigDecimal internalLaborBudget;

    /**
     * 内部项目外采预算(含税)
     */
    private BigDecimal internalOutsourcingBudgetTax;

    /**
     * 内部项目预算变更次数
     */
    private Long internalBudgetChangesNum;

    /**
     * 预估毛利(B表)
     */
    private BigDecimal estimatedGrossProfit;

    /**
     * 预估毛利率(B表)
     */
    private BigDecimal estimatedGrossProfitRate;

    /**
     * 预估收入总额(不含税 毛利测算)
     */
    private BigDecimal secondTotalBudgetRevenue;

    /**
     * 预估成本总额(不含税 毛利测算)
     */
    private BigDecimal secondTotalBudgetCost;

    /**
     * 预估毛利(毛利测算)
     */
    private BigDecimal secondEstimatedGrossProfit;

    /**
     * 预估毛利率(毛利测算)
     */
    private BigDecimal secondEstimatedGrossProfitRate;

    /**
     * 销售合同金额_不含税
     */
    private BigDecimal xshtjeBhs;

    /**
     * 项目回款
     */
    private BigDecimal xmhk;

    /**
     * A表收入总额(不含税)
     */
    private BigDecimal abygsrzeBhs;

    /**
     * A表成本总额(不含税)
     */
    private BigDecimal abygcbzeBhs;

    /**
     * A表项目预计毛利
     */
    private BigDecimal abygmle;

    /**
     * A表项目预计毛利率
     */
    private BigDecimal abygmll;

    /**
     * 主营业务收入
     */
    private BigDecimal zyywsr;

    /**
     * 主营业务成本
     */
    private BigDecimal zyywcb;

    /**
     * C表毛利额
     */
    private BigDecimal cbmle;

    /**
     * C表毛利率
     */
    private BigDecimal cbmll;

    /**
     * 主营业务收入_财务口径
     */
    private BigDecimal zyywsrCwkj;

    /**
     * 主营业务成本_财务口径
     */
    private BigDecimal zyywcbCwkj;

    /**
     * 毛利额
     */
    private BigDecimal mle;

    /**
     * 毛利率
     */
    private BigDecimal mll;

    /**
     * 流程ID
     */
    private Long requestId;

    /**
     * 流程名称
     */
    private String requestName;

    /**
     * 计划签署合同数量
     */
    private Integer planContractNum;

    /**
     * 已签合同数量
     */
    private Integer signedContractNum;

    /**
     * 计划签署合同金额(含税)
     */
    private BigDecimal planContractAmountIncludeTax;

    /**
     * 已签合同金额(含税)
     */
    private BigDecimal signedContractAmountIncludeTax;

    /**
     * 计划总产值
     */
    private BigDecimal planTotalOutputValue;

    /**
     * 实际总产值
     */
    private BigDecimal actualTotalOutputValue;

    /**
     * 偏差说明
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String deviationExplanation;

    /**
     * B表收入总额
     */
    private BigDecimal bTableRevenueTotal;

    /**
     * 当前收入总额
     */
    private BigDecimal currentRevenueTotal;

    /**
     * B表成本总额
     */
    private BigDecimal bTableCostTotal;

    /**
     * 当前成本总额
     */
    private BigDecimal currentCostTotal;

    /**
     * B表毛利
     */
    private BigDecimal bTableGrossProfit;

    /**
     * 当前毛利
     */
    private BigDecimal currentGrossProfit;

    /**
     * B表毛利率
     */
    private BigDecimal bTableGrossProfitRate;

    /**
     * 当前毛利率
     */
    private BigDecimal currentGrossProfitRate;

    /**
     * 成本偏差和毛利偏差说明
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String costGrossDeviationExplanation;

    /**
     * 已验收金额(含税)
     */
    private BigDecimal acceptedAmountIncludeTax;

    /**
     * 已回款金额(含税)
     */
    private BigDecimal receivedAmountIncludeTax;

    /**
     * 待回款金额(含税)
     */
    private BigDecimal pendingAmountIncludeTax;

    /**
     * 坏账
     */
    private BigDecimal badDebt;

    /**
     * 应付现金含税
     */
    private BigDecimal payableCashTax;

    /**
     * 已付现金含税
     */
    private BigDecimal paidCashTax;

    /**
     * 代付现金含税
     */
    private BigDecimal agentPaidCashTax;

    /**
     * 现金流净额_权责发生制
     */
    private BigDecimal cashFlowNetAccrual;

    /**
     * 现金流净额_收付实现制
     */
    private BigDecimal cashFlowNetCashBasis;

    /**
     * 待回款说明
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String pendingAmountExplanation;

    /**
     * 现金流说明
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String cashExplanation;

    /**
     * 坏账说明
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String badDebtExplanation;

    /**
     * 质保期预估工时
     */
    private BigDecimal warrantyEstimatedHours;

    /**
     * 质保期预算直接人工成本
     */
    private BigDecimal warrantyEstimatedDirectLaborCost;

    /**
     * 交付期项目已分配预算直接人工成本
     */
    private BigDecimal deliverTaskAssignedLaborCost;

    /**
     * 交付期项目已分配预算直接人工成本
     */
    private BigDecimal deliverTaskUsedBudget;

}