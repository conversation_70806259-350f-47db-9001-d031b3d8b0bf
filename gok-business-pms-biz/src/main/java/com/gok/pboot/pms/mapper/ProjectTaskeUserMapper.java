package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectTaskeUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目任务-用户关联Mapper
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@Mapper
public interface ProjectTaskeUserMapper extends BaseMapper<ProjectTaskeUser> {

    /**
     * 根据用户ID查询
     * @param userId 用户ID
     * @return 数据列表
     */
    List<ProjectTaskeUser> findByUserId(@Param("userId") Long userId);

    /**
     * 根据任务id获取任务-人员关联列表
     *
     * @param taskIds 任务id列表
     * @return {@link List}<{@link ProjectTaskeUser}>
     */
    List<ProjectTaskeUser> findListByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 根据任务id和任务角色获取任务-人员关联列表
     * @param taskIds 任务id列表
     * @param taskRole 任务角色
     * @return 数据列表
     */
    List<ProjectTaskeUser> findByTaskIdsAndTaskRole(
            @Param("taskIds") Collection<Long> taskIds, @Param("taskRole") Integer taskRole
    );

    /**
     * 根据任务id和任务角色和用户id获取任务-人员关联列表
     * @param taskIds 任务id列表
     * @param taskRole 任务角色
     * @param userId 用户id
     * @return 数据列表
     */
    List<ProjectTaskeUser> findByTaskIdsAndTaskRoleAndUserId(
            @Param("taskIds") Collection<Long> taskIds,
            @Param("taskRole") Integer taskRole,
            @Param("userId") Long userId
    );

    /**
     * 根据任务id获取任务-人员关联列表
     *
     * @param taskId   任务id
     * @param taskRole 人员角色
     * @return {@link List}<{@link ProjectTaskeUser}>
     */
    List<ProjectTaskeUser> findListByTaskId(@Param("taskId") Long taskId, @Param("taskRole") Integer taskRole);

    /**
     * 根据任务id获取任务-人员关联列表
     *
     * @param taskId   任务id
     * @param taskRole 人员角色
     * @param page     分页对象
     * @return {@link List}<{@link ProjectTaskeUser}>
     */
    Page<ProjectTaskeUser> findPageByTaskId(Page<ProjectTaskeUser> page, @Param("taskId") Long taskId, @Param("taskRole") Integer taskRole);

    /**
     * 根据任务id逻辑删除任务-用户关联
     *
     * @param taskId 任务id
     * @return int
     */
    int logicDeleteByTaskId(@Param("taskId") Long taskId);

    int batchDeleteByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 根据任务id物理删除任务-用户关联
     *
     * @param taskId   任务id
     * @param taskRole 成员类型
     * @return int
     */
    int physicalDeleteByTaskId(@Param("taskId") Long taskId, @Param("taskRole") Integer taskRole);

    /**
     * 批量保存任务-人员关联
     *
     * @param list 项目任务-人员关联列表
     * @return int
     */
    int batchSave(@Param("list")List<ProjectTaskeUser> list);

    /**
     * 根据用户ID和任务角色查询任务ID列表
     * @param userId 用户ID
     * @param taskRole 任务角色
     * @return 任务ID列表
     */
    List<Long> findTaskIdByUserIdAndTaskRole(@Param("userId") Long userId, @Param("taskRole") Integer taskRole);

    /**
     * 通过任务id列表获取负责人
     *
     * @param taskIds 任务id列表
     * @return {@link Map}<{@link Long}, {@link List}<{@link Long}>>
     */
    List<ProjectTaskeUser> findLeadersByTaskIds(@Param("taskIds") Collection<Long> taskIds);

    /**
     * 根据用户id批量删除成员
     *
     * @param taskId   任务id
     * @param userIds  用户di列表
     * @param taskRole 角色类型
     * @return int
     */
    int batchDelByUserIds(@Param("taskId") Long taskId, @Param("userIds") List<Long> userIds, @Param("taskRole") Integer taskRole);

}
