package com.gok.pboot.pms.enumeration;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * OA表格枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OAFormTypeEnum implements ValueEnum<Integer> {

    /**
     * 商业论证A表
     */
    SYLZAB(0, "XM-34项目商业论证(A表)", "formtable_main_356_dt2"),
    /**
     * 项目立项B表
     */
    XMLXBB(1, "XM-35项目立项申请(B表)", "formtable_main_366_dt2"),
    /**
     * 合同会签
     */
    HTHQ(2, "XM-38项目销售合同会签", ""),
    /**
     * 项目变更
     */
    XMBG(3, "XM-37项目变更申请", "formtable_main_371_dt2"),
    /**
     * 项目结项
     */
    XMJX(4, "XM-39项目结项申请", ""),
    /**
     * 里程碑达成审批
     */
    LCBDCSP(5, "XM-36里程碑达成审批", "formtable_main_367_dt4"),
    /**
     * 变更里程碑（项目变更）
     */
    BGLCB(6, "XM-37项目变更申请", "formtable_main_371_dt2"),
    /**
     * 合同变更
     */
    BGHTHQ(7, "XM-40项目销售合同变更", ""),
    /**
     * 客户备案
     */
    KHBA(8, "XM-01客户备案", ""),
    /**
     * 客户沟通记录表
     */
    KHGTJL(9, "XM-30客户沟通记录表", ""),
    /**
     * 客户移交
     */
    KHYJ(10, "XM-02客户移交", ""),
    /**
     * 商机报备382013
     */
    SJBB(11, "XM-03商机报备", ""),

    /**
     * 人力外包结算审批
     */
    RLWBJSSP(12, "XM-32人力外包结算审批", "formtable_main_336_dt1"),

    /**
     * CW-03费用报销单
     */
    FYBXD(13, "CW-03费用报销单", "formtable_main_125_dt1"),

    /**
     * XM-28商机项目费用报销
     */
    SJFYBX(14, "XM-28商机项目费用报销", "formtable_main_304_dt1"),

    /**
     * XM-15在建项目费用报销
     */
    ZJXMFYBX(15, "XM-15在建项目费用报销", "formtable_main_74_dt1"),
    ;


    /**
     * 值
     */
    private final Integer value;
    /**
     * 名
     */
    private final String name;


    /**
     * 明细表名
     */
    private final String detailTable;


    public static OAFormTypeEnum getBusinessTypeEnum(Integer value) {
        for (OAFormTypeEnum businessTypeEnum : OAFormTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum;
            }
        }
        return null;
    }

    public static String getDetailTableByVal(Integer value) {
        for (OAFormTypeEnum businessTypeEnum : OAFormTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum.getDetailTable();
            }
        }
        return StrUtil.EMPTY;
    }

    public static String getMainTable(String detailTableName) {
        if (StrUtil.isBlank(detailTableName)) {
            return detailTableName;
        }

        int lastUnderscoreIndex = detailTableName.lastIndexOf('_');
        if (lastUnderscoreIndex != -1) {
            return detailTableName.substring(0, lastUnderscoreIndex);
        }
        return StringUtils.EMPTY;
    }
}
