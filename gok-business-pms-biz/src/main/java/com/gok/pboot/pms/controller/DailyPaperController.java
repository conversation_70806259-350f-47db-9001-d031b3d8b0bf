package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostTaskDailyDetailVO;
import com.gok.pboot.pms.cost.entity.vo.DailyPaperTotalVO;
import com.gok.pboot.pms.cost.entity.vo.TaskDailyPaperDetailVO;
import com.gok.pboot.pms.entity.dto.DailyPaperAddOrUpdateDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperMonthlyDTO;
import com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO;
import com.gok.pboot.pms.entity.vo.DailyPaperVO;
import com.gok.pboot.pms.entity.vo.DailyPapersStatisticMonthlyVO;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import com.gok.pboot.pms.service.IDailyPaperService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 日报控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 16:28
 * @menu PMS业务模块-日报
 */
@Slf4j
@RestController
@RequestMapping("/dailyPaper")
@RequiredArgsConstructor
public class DailyPaperController extends BaseController {

    private final IDailyPaperService service;

    private final ICompensatoryLeaveDataService overtimeLeaveDataService;


    /**
     * 添加/编辑 日报
     *
     * @param request 请求实体
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     * @date 2022/8/23 16:37
     */
    @PostMapping("/addOrUpdate")
    public ApiResult<Void> addOrUpdate(@Valid @RequestBody DailyPaperAddOrUpdateDTO request) {
        request.setUserId(SecurityUtils.getUser().getId());
        service.newAddOrUpdate(request);
        return ApiResult.success(null);
    }

    /**
     * 根据ID获取绩效报表详情
     *
     * @param id 报表ID
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO>
     * <AUTHOR>
     * @date 2022/8/25 9:45
     */
    @GetMapping("/details/{id}")
    public ApiResult<DailyPaperDetailsVO> details(@PathVariable("id") Long id) {
        return ApiResult.success(service.getDetailsById(id));
    }

    @GetMapping("/statisticMonthly")
    public ApiResult<DailyPapersStatisticMonthlyVO> statisticMonthly(DailyPaperMonthlyDTO request) {

        return ApiResult.success(service.getStatisticMonthly(request));
    }

    /**
     * 查询日报
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperVO>
     * <AUTHOR>
     * @date 2022/8/26 14:30
     */
    @PostMapping("/listMonthly")
    public ApiResult<List<DailyPaperVO>> listMonthly(@Valid @RequestBody DailyPaperMonthlyDTO request) {
        return ApiResult.success(service.listDailyPaperMonthly(request));
    }

    /**
     * ~ 根据日期查找离其最近的日报详情 ~
     *
     * @param date date
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO>
     * <AUTHOR>
     * @date 2022/9/5 11:20
     */
    @GetMapping("/detailsForBackFill")
    public ApiResult<DailyPaperDetailsVO> detailsForBackFill(LocalDate date) {
        if (date == null) {
            return ApiResult.failure("请传入日期");
        }

        return ApiResult.success(service.getDetailsByDateForCurrentUser(date));
    }

    /**
     * 根据日期查询日报信息
     *
     * @param date 日期
     * @return {@link ApiResult<DailyPaperDetailsVO>}
     */
    @GetMapping("/detailsByDate")
    public ApiResult<DailyPaperDetailsVO> detailsBySubmissionDate(LocalDate date) {
        if (date == null) {
            return ApiResult.failure("请传入日期");
        }

        return ApiResult.success(service.getDetailsByDateForCurrentUserAccurate(date));
    }


    /**
     * 定时器
     * ~ 自动提交前一日日报定时任务调用方法 ~    0 0 14 * * ? *
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     * @date 2022/9/16 14:38
     */
    @Inner(value = false)
    @GetMapping("/autoSubmitJob")
    public ApiResult<Void> autoSubmitJob(@RequestParam("para") String para) {
        log.info("定时任务触发, 每天14:00自动提交指定日期前一天的日报，输入参数 {}，执行开始", para);
        service.autoSubmitJob(para);
        log.info("定时任务触发, 每天14:00自动提交指定日期前一天的日报，输入参数 {}，执行结束", para);
        return ApiResult.success(null);
    }

    /**
     * 工单审核人信息和相关日报分页查询
     *
     * @return {@link ApiResult }<{@link Page }<{@link TaskDailyPaperDetailVO }>>
     */
    @GetMapping("/taskDailyPaperPage/{costTaskId}")
    public ApiResult<CostTaskDailyDetailVO> taskDailyPaperPage(
            PageRequest pageRequest,
            @PathVariable Long costTaskId,
            @RequestParam(required = false,defaultValue = "2") Integer approvalStatus) {

        return ApiResult.success(service.taskDailyPaperPage(pageRequest, costTaskId, approvalStatus));
    }

    /**
     * 根据工单ID获取日报详情
     *
     * @param pageRequest 分页参数
     * @param costTaskId      工单ID
     * @return 日报详情分页数据
     */
    @GetMapping("/daily-details/{costTaskId}")
    public ApiResult<Page<TaskDailyPaperDetailVO>> getDailyDetailPageByTaskId(
            PageRequest pageRequest,
            @PathVariable Long costTaskId,
            @RequestParam(required = false) Integer approvalStatus) {
        return ApiResult.success(service.getDailyDetailPageByTaskId(pageRequest, costTaskId,approvalStatus));
    }


    /**
     * 工单工时日报总和查询
     *
     * @param costTaskId     成本任务id
     * @param approvalStatus 审批状态
     * @return {@link ApiResult }<{@link DailyPaperTotalVO }>
     */
    @GetMapping("/dailyDetailsTotalVO")
    public ApiResult<DailyPaperTotalVO>dailyDetailsTotalVO(@RequestParam("costTaskId") Long costTaskId,
                                                           @RequestParam("approvalStatus") Integer approvalStatus) {
        return ApiResult.success(service.dailyDetailsTotalVO(costTaskId,approvalStatus));
    }

    /**
     * 判断工单是否到了”计划结束日期“
     * @param taskIds 日报任务id集合
     * @return
     */
    @GetMapping("/end-task")
    public ApiResult<List<Long>> getEndTaskIds(@RequestParam List<Long> taskIds) {
        return ApiResult.success(service.getEndTaskIds(taskIds));
    }

}
