package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.upms.dto.MemberDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.EntitySign;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.constant.PmsConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.entity.dto.CalculateLaborCostDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.dto.CostSalaryDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO;
import com.gok.pboot.pms.cost.entity.vo.DeliverCostBudgetListVO;
import com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.cost.enums.CostTypeEnum;
import com.gok.pboot.pms.cost.mapper.CostDeliverTaskMapper;
import com.gok.pboot.pms.cost.mapper.CostManageEstimationResultsMapper;
import com.gok.pboot.pms.cost.service.ICostConfigLevelPriceService;
import com.gok.pboot.pms.cost.service.ICostManageVersionService;
import com.gok.pboot.pms.cost.service.ICostTaskCategoryManagementService;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.Filing;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryProjectBO;
import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryViewBO;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectTaske;
import com.gok.pboot.pms.entity.domain.ProjectTaskeUser;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.IDailyPaperService;
import com.gok.pboot.pms.service.IDailyReviewService;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.CastUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.Collator;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gok.pboot.pms.enumeration.ApprovalStatusEnum.*;

/**
 * <AUTHOR>
 * @since 2022-08-31
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DailyReviewServiceImpl implements IDailyReviewService {

    private final HolidayMapper holidayMapper;
    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    private final TomorrowPlanPaperEntryMapper tomorrowPlanPaperEntryMapper;
    private final DailyPaperMapper dailyPaperMapper;
    private final FilingMapper filingMapper;
    private final EntityOptionMapper entityOptionMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final ProjectTaskeMapper projectTaskeMapper;
    private final ProjectTaskeUserMapper projectTaskeUserMapper;
    private final IDailyPaperService dailyPaperService;
    private final PersonnelReuseMapper personnelReuseMapper;
    private final PersonnelDeliveryHourMapper personnelDeliveryHourMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    // 接入中台的接口
    private final RemoteSendMsgService remoteSendMsgService;
    private final RemoteMailService remoteMailService;
    private final RemoteUserService remoteUserService;
    private final RemoteDeptService remoteDeptService;
    private final BcpLoggerUtils bcpLoggerUtils;
    private final ICostTaskCategoryManagementService costTaskCategoryManagementService;
    private final ICostConfigLevelPriceService costConfigLevelPriceService;
    private final CostDeliverTaskMapper costDeliverTaskMapper;
    private final CostManageEstimationResultsMapper costManageEstimationResultsMapper;
    private final ICostManageVersionService costManageVersionService;

    @Value("${project.task.leftUrl}")
    private String hostUrl;

    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefixUrl;

    private static final String NO_DAILY_PAPER_TO_BE_REVIEWED = "尚无要审核的日报";
    private static final String USERNAME_KEY = "username";
    private static final String PROJECT_ID_KEY = "projectId";
    private static final String REMOVE_FLAG_KEY = "removeFlag";
    private static final String IS_DEPT_KEY = "isDept";

    @Override
    public ApiResult<Page<DailyReviewDateVO>> dailyReviewFindPageVO(DailyReviewDTO dailyReviewDTO) {
        List<DailyPaperEntry> entries = findEntriesAuditable(dailyReviewDTO);
        Map<Pair<Long, LocalDate>, List<DailyPaperEntry>> entriesGroupByProjectIdAndSubmissionDate;
        Page<List<DailyPaperEntry>> pageBean = new Page<>(dailyReviewDTO.getPageNumber(), dailyReviewDTO.getPageSize());

        if (entries.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(pageBean, x -> null));
        }
        entriesGroupByProjectIdAndSubmissionDate = entries.stream()
                .sorted(
                        Comparator.comparing(DailyPaperEntry::getSubmissionDate).reversed()
                                .thenComparing(DailyPaperEntry::getProjectName, Collator.getInstance(Locale.CHINA))
                )
                .collect(Collectors.groupingBy(
                        entry -> Pair.of(entry.getProjectId(), entry.getSubmissionDate()),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        PageUtils.doPaging(
                ImmutableList.copyOf(entriesGroupByProjectIdAndSubmissionDate.values()),
                pageBean,
                true
        );
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        LinkedHashMap<LocalDate, Integer>  holidayMap
                = holidayList.stream()
                .filter(h-> h.getHolidayType()!=null)
                .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));

        return ApiResult.success(PageUtils.mapTo(pageBean, entry -> {
            return DailyReviewDateVO.of(
                    entry,
                    holidayMap
            );
        }));
    }

    @Override
    public ApiResult<Page<DailyProjectDetailsFindPageVO>> dailyProjectDetailsFindPageVO(
            DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO
    ) {
        DailyReviewDTO dto = transformToDailyReviewDTO(new PageRequest(), dailyProjectDetailsFindPageDTO);
        List<DailyPaperEntry> entries = findEntriesAuditable(dto);
        Page<DailyPaperEntry> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        List<DailyPaperEntry> pageRecords;
        List<Long> paperIds;
        Map<Long, DailyPaper> paperIdMap;
        Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> lastDayPlanEntriesTable;

        if (entries.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(page, x -> null));
        }
        PageUtils.doPaging(
                entries.stream()
                        .sorted(Comparator.comparing(
                                DailyPaperEntry::getUserRealName, Collator.getInstance(Locale.CHINA)
                        ))
                        .collect(Collectors.toList()),
                page, true
        );
        pageRecords = page.getRecords();
        paperIds = pageRecords.stream().map(DailyPaperEntry::getDailyPaperId).collect(Collectors.toList());
        paperIdMap = BaseEntityUtils.mapCollectionToIdMap(dailyPaperMapper.selectBatchIds(paperIds));
        lastDayPlanEntriesTable = findLastDayPlanEntries(pageRecords);
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        return ApiResult.success(PageUtils.mapTo(page, e -> {
            DailyProjectDetailsFindPageVO result = new DailyProjectDetailsFindPageVO();
            Long entryId = e.getId();
            Long dailyPaperId = e.getDailyPaperId();
            DailyPaper paper = paperIdMap.get(dailyPaperId);
            Long userId = e.getUserId();
            LocalDate submissionDate = e.getSubmissionDate();
            List<TomorrowPlanPaperEntry> lastDayPlans = ObjectUtils.defaultIfNull(
                    lastDayPlanEntriesTable.get(userId, submissionDate),
                    ImmutableList.of()
            );
            Map<Long, String> taskIdAndLastDayPlanDescMap = lastDayPlans.isEmpty() ?
                    ImmutableMap.of() :
                    lastDayPlans.stream().collect(Collectors.toMap(
                            TomorrowPlanPaperEntry::getTaskId, TomorrowPlanPaperEntry::getDescription)
                    );

            result.setId(entryId);
            result.setProjectId(e.getProjectId());
            result.setAddedHours(e.getAddedHours());
            if (EnumUtils.valueEquals(dto.getAuditStatus(), YesOrNoEnum.YES)) {
                result.setApprovalName(Strings.nullToEmpty(e.getModifier()));
            } else {
                result.setApprovalName("");
            }
            result.setApprovalReason(e.getApprovalReason());
            result.setApprovalStatus(e.getApprovalStatus());
            result.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, e.getApprovalStatus()));
            result.setDailyPaperId(dailyPaperId);
            result.setDescription(e.getDescription());
            result.setFillingStateName(EnumUtils.getNameByValue(DailyPaperFillingStateEnum.class, paper.getFillingState()));
            result.setNormalHours(e.getNormalHours());
            result.setSubmissionDate(submissionDate);
            result.setTaskName(e.getTaskName());
            result.setUserId(userId);
            result.setUserRealName(e.getUserRealName());
            result.setWorkType(e.getWorkType());
            result.setWorkTypeName(costTaskCategoryMap.getOrDefault(e.getWorkType(), new CostTaskCategoryManagement()).getTaskCategoryName());
            result.setYesterdayPlan(taskIdAndLastDayPlanDescMap.getOrDefault(e.getTaskId(), StringUtils.EMPTY));

            return result;
        }));
    }

    /**
     * 转换为DailyReviewDTO
     * @param pageRequest 分页对象
     * @param dto 参数对象
     * @return 转换后的DailyReviewDTO
     */
    private DailyReviewDTO transformToDailyReviewDTO(
            @Nullable PageRequest pageRequest, DailyProjectDetailsFindPageDTO dto
    ) {
        DailyReviewDTO result = new DailyReviewDTO();
        int pageNumber = dto.getPageNumber();
        int pageSize = dto.getPageSize();

        result.setProjectId(NumberUtils.toLong(dto.getProjectId()));
        result.setAuditStatus(dto.getStatus());
        result.setStartTime(dto.getSubmissionDate());
        result.setEndTime(dto.getSubmissionDate());
        result.setTaskName(dto.getTaskName());
        result.setUsername(dto.getUserRealName());
        result.setInCharge(dto.getInCharge());
        if ((pageNumber == 0 || pageSize == 10) && pageRequest != null) {
            // 兼容问题，如果发现分页参数是默认参数，就使用另一个对象中的
            pageNumber = pageRequest.getPageNumber();
            pageSize = pageRequest.getPageSize();
        }
        result.setPageNumber(pageNumber);
        result.setPageSize(pageSize);

        return result;
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> changeApprovalStatus(ChangeApprovalStatusDTO changeApprovalStatusDTO) {
        Long id = changeApprovalStatusDTO.getId();
        Integer dailyPaperType = changeApprovalStatusDTO.getDailyPaperType();
        PigxUser user = SecurityUtils.getUser();

        // 按日报类型获取日报信息
        DailyPaperCommonDTO dailyPaperDTO;
        if (ObjectUtil.isNull(dailyPaperType) || DailyPaperTypeEnum.NORMAL_DAILY_PAPER.getKey().equals(dailyPaperType)) {
            dailyPaperDTO = dailyPaperEntryMapper.selectDailyPaperById(id);
        } else if (DailyPaperTypeEnum.TALENT_REUSE_MAN_HOUR.getKey().equals(dailyPaperType)) {
            dailyPaperDTO = personnelReuseMapper.selectDailyPaperById(id);
        } else if (DailyPaperTypeEnum.DELIVERER_MAN_HOUR.getKey().equals(dailyPaperType)) {
            dailyPaperDTO = personnelDeliveryHourMapper.selectDailyPaperById(id);
        } else {
            return ApiResult.failure("日报类型参数有误，请联系管理员！");
        }
        if (dailyPaperDTO == null) {
            return ApiResult.failure("数据异常：未找到日报数据，请联系管理员！");
        }

        //是否已归档
        LocalDate submissionDate = dailyPaperDTO.getSubmissionDate();
        Filing filing = filingMapper.getFilingByDate(submissionDate);
        if (Optional.ofNullable(filing).isPresent()
                &&filing.getFiled()!=null
                &&FilingTypeEnum.YGD.getValue().equals(filing.getFiled())) {
            return ApiResult.failure("当前填报时间内存在归档日期，请重新选择或联系管理员！");
        }

        // 未通过时，校验未通过原因是否为空
        Integer approvalStatus = changeApprovalStatusDTO.getApprovalStatus();
        if (
                BTG.getValue().equals(approvalStatus) &&
                StringUtils.isBlank(changeApprovalStatusDTO.getApprovalReason())
        ) {
            return ApiResult.failure("请输入未通过原因！");
        }

        if (user == null) {
            throw new ServiceException("用户信息获取异常");
        }
        changeApprovalStatusDTO.setApprovalName(user.getName());
        changeApprovalStatusDTO.setApprovalId(user.getId());
        changeApprovalStatusDTO.setMtime(new Timestamp(System.currentTimeMillis()));
        // 更新审批状态
        if (ObjectUtil.isNull(dailyPaperType) || DailyPaperTypeEnum.NORMAL_DAILY_PAPER.getKey().equals(dailyPaperType)) {
            List<DailyPaperEntry> dailyPaperEntries = dailyPaperEntryMapper.selectList(Wrappers.<DailyPaperEntry>lambdaQuery()
                    .eq(DailyPaperEntry::getId, changeApprovalStatusDTO.getId()));
            dailyPaperEntryMapper.updateApprovalStatusById(changeApprovalStatusDTO);

            //计算工单实际人工成本
            batchUpdateTaskActualLaborCost(dailyPaperEntries);
            //根据日报id集合 更新外层日报状态
            List<Long> dailyPaperIds = new ArrayList<>();
            dailyPaperIds.add(dailyPaperDTO.getDailyPaperId());
            dailyPaperService.updateApprovalStatusIfNeeded(dailyPaperIds,
                    ApprovalStatusEnum.getApprovalStatusEnum(approvalStatus));
            if (ApprovalStatusEnum.YTH.getValue().equals(approvalStatus)) {
                //已退回发送企微和门户通知
                DailyPaperEntry dailyPaperEntry = dailyPaperEntryMapper.selectById(id);
                Long userId = dailyPaperEntry.getUserId();
                String userName = dailyPaperEntry.getUserRealName();
                List<BcpMessageTargetDTO> targetList = Lists.newArrayList(
                        new BcpMessageTargetDTO(String.valueOf(userId), Optional.ofNullable(userName).orElse("defaultName"), null)
                );
                log.info("发送企微的用户信息targetList：{}",targetList);
                // 设置信息对象
                String redirectUrl =
                        hostUrl + "/view-businesses/daily/write?approvalStatus=" + ApprovalStatusEnum.YTH.getValue() +
                                "&startDate=" + DateTimeFormatter.ofPattern("yyyy-MM").format(dailyPaperEntry.getSubmissionDate());
                String baseContent = "【已退回】您好,您" +
                        dailyPaperEntry.getSubmissionDate() +
                        "：项目：" +
                        dailyPaperEntry.getProjectName() +
                        " 任务：" +
                        dailyPaperEntry.getTaskName() +
                        "已被退回，请关注对应日报填写详情";
                WeComModel weComModel =
                        createWeComModel(
                                "日报被退回",
                                baseContent,
                                redirectPrefixUrl + Base64.encode(redirectUrl, Charsets.UTF_8),
                                targetList
                        );
                MailModel mailModel = MailModel.from(WeComModel.to(weComModel));

                // 发送企微消息
                remoteSendMsgService.sendMsg(weComModel);
                // 发送门户消息
                mailModel.setContent(baseContent);
                remoteMailService.sendMsg(mailModel);
            } else if (BTG.getValue().equals(approvalStatus)) {
                // 不通过发送企微和门户消息
                DailyPaperEntry dailyPaperEntry = dailyPaperEntryMapper.selectById(id);
                Long userId = dailyPaperEntry.getUserId();
                String userName = dailyPaperEntry.getUserRealName();
                List<BcpMessageTargetDTO> targetList = Lists.newArrayList(
                        new BcpMessageTargetDTO(String.valueOf(userId), Optional.ofNullable(userName).orElse("defaultName"), null)
                );
                log.info("发送企微的用户信息targetList：{}",targetList);
                // 设置信息对象
                String redirectUrl =
                        hostUrl + "/view-businesses/daily/write?approvalStatus=" + BTG.getValue() +
                                "&startDate=" + DateTimeFormatter.ofPattern("yyyy-MM").format(dailyPaperEntry.getSubmissionDate());
                String baseContent = "【不通过】您好，您在" +
                        dailyPaperEntry.getSubmissionDate() +
                        "填报的【" +
                        dailyPaperEntry.getProjectName() +
                        "】 - 【" +
                        dailyPaperEntry.getTaskName() +
                        "】的日报未通过审核，请重新填报~";
                WeComModel weComModel = createWeComModel(
                        "日报不通过",
                        baseContent,
                        redirectPrefixUrl + Base64.encode(redirectUrl, Charsets.UTF_8),
                        targetList
                );
                MailModel mailModel = MailModel.from(WeComModel.to(weComModel));

                // 发送企微消息
                remoteSendMsgService.sendMsg(weComModel);
                // 发送门户消息
                mailModel.setContent(baseContent);
                remoteMailService.sendMsg(mailModel);
            }
            if (ApprovalStatusEnum.YTG.getValue().equals(changeApprovalStatusDTO.getApprovalStatus())) {
                batchUpdateTaskStartDate(changeApprovalStatusDTO.getId());
            }
        } else if (DailyPaperTypeEnum.TALENT_REUSE_MAN_HOUR.getKey().equals(dailyPaperType)) {
            personnelReuseMapper.updateApprovalStatusById(changeApprovalStatusDTO);
        } else if (DailyPaperTypeEnum.DELIVERER_MAN_HOUR.getKey().equals(dailyPaperType)) {
            personnelDeliveryHourMapper.updateApprovalStatusById(changeApprovalStatusDTO);
        }
        //【通过/不通过】【任务名称】【提交人】【填报日期】
        try {
            if(DailyPaperTypeEnum.NORMAL_DAILY_PAPER.getKey().equals(dailyPaperType)&&
                    ApprovalStatusEnum.YTG.getValue().equals(changeApprovalStatusDTO.getApprovalStatus())){
                bcpLoggerUtils.log(FunctionConstants.REVIEW_OF_DAILY_WORK_HOURS_REPORT, LogContentEnum.WORK_HOUR_REVIEW,
                        "通过",dailyPaperDTO.getProjectName(),1,dailyPaperDTO.getSubmissionDate());
            }else if(DailyPaperTypeEnum.NORMAL_DAILY_PAPER.getKey().equals(dailyPaperType)&&
                    ApprovalStatusEnum.BTG.getValue().equals(changeApprovalStatusDTO.getApprovalStatus())){
                bcpLoggerUtils.log(FunctionConstants.REVIEW_OF_DAILY_WORK_HOURS_REPORT, LogContentEnum.WORK_HOUR_REVIEW,
                        "不通过",dailyPaperDTO.getProjectName(),1,dailyPaperDTO.getSubmissionDate());
            }else if(DailyPaperTypeEnum.TALENT_REUSE_MAN_HOUR.getKey().equals(dailyPaperType)&&
                    ApprovalStatusEnum.YTG.getValue().equals(changeApprovalStatusDTO.getApprovalStatus())){
                bcpLoggerUtils.log(FunctionConstants.REUSE_WORKING_HOURS_REVIEW, LogContentEnum.REUSE_WORKING_HOURS_REVIEW,
                        "通过",dailyPaperDTO.getProjectName(),1,dailyPaperDTO.getAggregatedDays());
            }else if(DailyPaperTypeEnum.TALENT_REUSE_MAN_HOUR.getKey().equals(dailyPaperType)&&
                    ApprovalStatusEnum.BTG.getValue().equals(changeApprovalStatusDTO.getApprovalStatus())){
                bcpLoggerUtils.log(FunctionConstants.REUSE_WORKING_HOURS_REVIEW, LogContentEnum.REUSE_WORKING_HOURS_REVIEW,
                        "不通过",dailyPaperDTO.getProjectName(),1,dailyPaperDTO.getAggregatedDays());
            }
        }catch (Exception e){
            log.error("工时日报审核日志记录异常:"+e);
        }
        return ApiResult.success("审核状态更新成功！");
    }

    private WeComModel createWeComModel(
            String title, String baseContent, String redirectUrl, List<BcpMessageTargetDTO> targetList
    ) {
        WeComModel result = new WeComModel();
        PigxUser user = SecurityUtils.getUser();
        boolean userNotExists = user == null;

        result.setTitle(title);
        result.setSource(SourceEnum.PROJECT.getValue());
        result.setType(MsgTypeEnum.WARN_MSG.getValue());
        result.setTargetType(TargetTypeEnum.USERS.getValue());
        result.setContent(baseContent + "\n<a href=\"" + redirectUrl + "\">查看详情</a>");
        result.setSenderId(userNotExists ? 10000L : user.getId());
        result.setSender(userNotExists ? StringUtils.EMPTY : user.getName());
        result.setTargetList(targetList);
        result.setSendTime(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
        result.setRedirectUrl(redirectUrl);

        return result;
    }

    /**
     * 批量更新任务开始时间（首次填报日报时间）
     *
     * @param id 日报条目id
     */
    private void batchUpdateTaskStartDate(Long id) {
        // 判断是否是挂靠任务第一次填报日报，是的话填入开始时间
        DailyPaperEntry dailyPaperEntry = dailyPaperEntryMapper.selectById(id);
        ProjectTaske projectTaske = projectTaskeMapper.selectById(dailyPaperEntry.getTaskId());
        if (Optional.ofNullable(projectTaske).isPresent() && (!Optional.ofNullable(projectTaske.getStartDate()).isPresent() || projectTaske.getStartDate().isAfter(dailyPaperEntry.getSubmissionDate()))) {
            projectTaske.setStartDate(dailyPaperEntry.getSubmissionDate());
            List<ProjectTaske> taskes = new ArrayList<>();
            taskes.add(projectTaske);
            projectTaskeMapper.batchUpdateStartDate(taskes);
        }
    }

    @Override
    @Transactional
    public ApiResult<String> dailyProjectDetailsOneAudit(DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO) {
        PigxUser user = SecurityUtils.getUser();
        Long userId = user.getId();
        Page<DailyProjectDetailsFindPageVO> page = dailyPaperEntryMapper.dailyProjectDetailsFindPageVO(
                new Page<>(1, Integer.MAX_VALUE),
                dailyProjectDetailsFindPageDTO
        );
        List<DailyProjectDetailsFindPageVO> records = page.getRecords();
        List<Long> entryIds;
        List<LocalDate> submissionDates;
        List<Filing> filings;
        List<Long> dailyPaperIds;
        ChangeApprovalStatusDTO changeApprovalStatusDTO;

        if (records.isEmpty()) {
            throw new ServiceException(NO_DAILY_PAPER_TO_BE_REVIEWED);
        }
        submissionDates = records.stream()
                .map(DailyProjectDetailsFindPageVO::getSubmissionDate)
                .collect(Collectors.toList());
        filings = filingMapper.findByDates(submissionDates);
        if (filings.stream().anyMatch(f -> EnumUtils.valueEquals(f.getFiled(), FilingTypeEnum.YGD))) {
            return ApiResult.failure("当前填报时间内存在归档日期，请重新选择或联系管理员！");
        }
        //需要改变状态为审核的日报条目id集合
        entryIds = BaseEntityUtils.mapCollectionToList(records, DailyProjectDetailsFindPageVO::getId);
        //日报id
        dailyPaperIds = BaseEntityUtils.mapCollectionToList(records, DailyProjectDetailsFindPageVO::getDailyPaperId);
        //批量审核
        changeApprovalStatusDTO = new ChangeApprovalStatusDTO();
        changeApprovalStatusDTO.setApprovalName(user.getName());
        changeApprovalStatusDTO.setApprovalId(userId);
        changeApprovalStatusDTO.setMtime(new Timestamp(System.currentTimeMillis()));
        dailyPaperEntryMapper.batchApproval(entryIds, changeApprovalStatusDTO);
        //批量更新日报状态 、实际人工成本
        List<DailyPaperEntry> dailyPaperEntries = dailyPaperEntryMapper.selectList(Wrappers.<DailyPaperEntry>lambdaQuery()
                .in(DailyPaperEntry::getId,entryIds));
        //计算工单实际人工成本
        batchUpdateTaskActualLaborCost(dailyPaperEntries);

        //根据日报id集合 更新外层日报状态
        dailyPaperService.updateApprovalStatusIfNeeded(dailyPaperIds, ApprovalStatusEnum.YTG);
        ProjectInfo projectInfo = projectInfoMapper.selectById(dailyProjectDetailsFindPageDTO.getProjectId());

        List<Long> userIds = records.stream().map(r -> r.getUserId()).distinct().collect(Collectors.toList());
        BigDecimal allHours=BigDecimal.ZERO;
        for (DailyProjectDetailsFindPageVO vo : records){
            if(Optional.ofNullable(vo.getNormalHours()).isPresent()){
                allHours=allHours.add(vo.getNormalHours());
            }
            if(Optional.ofNullable(vo.getAddedHours()).isPresent()){
                allHours=allHours.add(vo.getAddedHours());
            }
        }
        bcpLoggerUtils.log(FunctionConstants.REUSE_WORKING_HOURS_REVIEW, LogContentEnum.REUSE_WORKING_HOURS_REVIEW,
                "通过",projectInfo.getItemName(),userIds.size(),allHours);
        return ApiResult.success("一键审核成功！");
    }

    @Override
    public ApiResult<Page<DailyReviewProjectVO>> projectDimensionPage(DailyReviewDTO dto) {
        List<DailyPaperEntry> entries = findEntriesAuditable(dto);
        Page<ProjectInfo> projectPage = new Page<>(dto.getPageNumber(), dto.getPageSize());
        List<ProjectInfo> projects;
        List<Long> projectIds;
        Map<Long, List<DailyPaperEntry>> projectIdAndEntriesMap;

        if (entries.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(projectPage, x -> null));
        }
        projectIds = entries.stream()
                .sorted(Comparator.comparing(DailyPaperEntry::getProjectName, Collator.getInstance(Locale.CHINA)))
                .map(DailyPaperEntry::getProjectId)
                .distinct()
                .collect(Collectors.toList());
        projectPage = projectInfoMapper.findByIdsOrderByItemName(projectPage, projectIds);
        projects = projectPage.getRecords();
        if (projects.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(projectPage, x -> null));
        }
        projectIdAndEntriesMap = entries.stream().collect(Collectors.groupingBy(DailyPaperEntry::getProjectId));

        return ApiResult.success(PageUtils.mapTo(
                projectPage,
                p -> DailyReviewProjectVO.of(p, projectIdAndEntriesMap.getOrDefault(p.getId(), ImmutableList.of()))
        ));
    }

    /**
     * 获取所有可审核的工时（不包括交付工时）
     * @param dto 日报审核dto
     * @return 可审核的工时列表
     */
    private List<DailyPaperEntry> findEntriesAuditable(DailyReviewDTO dto) {
        Map<String, Object> filter;
        Long userId = SecurityUtils.getUser().getId();
        LocalDate dtoStartTime;
        LocalDate dtoEndTime;
        List<Long> triangleProjectIds;
        List<MemberDto> userAndUnderUserList;
        Set<Long> subUserIds;
        Set<Long> taskIds;
        List<Long> leaderTaskIds;
        List<Long> subUserTaskIds;
        List<DailyPaperEntry> entries;
        Set<Long> projectIdsByTaskIds;
        Set<Long> specialProjectIds;
        Map<Long, ProjectInfo> allProjectIdMap;
        List<Pair<LocalDate, LocalDate>> filingDateRanges;
        Map<Long, List<ProjectTaskeUser>> taskIdAndLeadersMap;

        filter = buildDailyReviewAndFilterMap(dto);
        // 额外添加过滤归档日期的参数
        filingDateRanges = filingMapper.findByFiled(FilingTypeEnum.YGD.getValue())
                .stream()
                .map(filing -> Pair.of(
                        filing.getFilingStartDatetime().toLocalDate(),
                        filing.getFilingEndDatetime().toLocalDate().minusDays(1)
                ))
                .collect(Collectors.toList());
        if (!filingDateRanges.isEmpty()) {
            filter.put("filingDateRanges", filingDateRanges);
        }
        // 1. 查询铁三角项目
        /*
         * 商机、商机终止状态，铁三角可审核参与人工时
         * 项目经理可审核参与人工时
         */
        triangleProjectIds = projectInfoMapper.findIdAuditableByTriangleUserId(userId);
        // 2. 查询直属下级
        userAndUnderUserList = remoteUserService.getImmediateSubordinates(userId).getData();
        subUserIds = CollectionUtils.isEmpty(userAndUnderUserList) ?
                ImmutableSet.of() : userAndUnderUserList.stream().map(MemberDto::getUserId).collect(Collectors.toSet());
        // 3. 根据1、2，查询当前用户铁三角项目下的所有任务 + 当前用户负责的所有任务（任务负责人） + 当前用户直属下级填写的所有任务
        taskIds = Sets.newHashSet();
        // 主责审核任务必定加入查询条件
        leaderTaskIds = projectTaskeUserMapper.findTaskIdByUserIdAndTaskRole(
                userId, ProjectTaskRoleEnum.LEADER.getValue()
        );
        taskIds.addAll(leaderTaskIds);
        // 查询 当前用户铁三角项目的 + 当前用户直属下级填写的所有任务
        if (!triangleProjectIds.isEmpty()) {
            taskIds.addAll(projectTaskeMapper.findTaskIdByProjectIds(triangleProjectIds));
        }
        dtoStartTime = dto.getStartTime();
        dtoEndTime = dto.getEndTime();
        if (!subUserIds.isEmpty()) {
            // 查询直属下级填写的所有任务（通过工时查询），以及这些任务的负责人
            subUserTaskIds = dailyPaperEntryMapper.findTaskIdByUserIdsAndDateRangeAndApprovalStatus(
                    subUserIds, dtoStartTime, dtoEndTime,
                    EnumUtils.valueEquals(dto.getAuditStatus(), YesOrNoEnum.YES) ?
                            ImmutableList.of(BTG.getValue(), ApprovalStatusEnum.YTG.getValue())
                            :
                            ImmutableList.of(ApprovalStatusEnum.DSH.getValue())
            );
            taskIds.addAll(subUserTaskIds);
        }
        taskIdAndLeadersMap = taskIds.isEmpty() ?
                Maps.newHashMap() : projectTaskeUserMapper.findByTaskIdsAndTaskRole(
                taskIds, ProjectTaskRoleEnum.LEADER.getValue()
        ).stream().filter(tu -> tu.getTaskId() != null).collect(Collectors.groupingBy(ProjectTaskeUser::getTaskId));
        // 4. 查询所有特殊项目
        specialProjectIds = entityOptionMapper.findIdSetBySign(EntitySign.SPECIAL_PROJECT);
        // 5. 查询所有已知项目
        projectIdsByTaskIds = projectTaskeMapper.findByTaskIds(taskIds)
                .stream()
                .map(ProjectTaske::getProjectId)
                .collect(Collectors.toSet());
        allProjectIdMap = projectInfoMapper.selectBatchIds(
                CollUtil.union(projectIdsByTaskIds, specialProjectIds, triangleProjectIds)
        ).stream().collect(Collectors.toMap(ProjectInfo::getId, x -> x));
        // 6. 查询所有工时条目
        filter.put(
                "reviewTriple",
                Triple.of(
                        taskIds.isEmpty() ? ImmutableList.of(-1) : taskIds,
                        subUserIds.isEmpty() ? ImmutableList.of(-1) : subUserIds,
                        allProjectIdMap.isEmpty() ? ImmutableList.of(-1) : allProjectIdMap.keySet()
                )
        );
        entries = dailyPaperEntryMapper.findList(filter);
        if (entries.isEmpty()) {
            return ImmutableList.of();
        }
        // 7. 进行筛选，得出可审核的工时条目

        return entries.stream().filter(entry -> {
            Long entryUserId;
            Long projectId = entry.getProjectId();
            Long taskId = entry.getTaskId();
            ProjectInfo project = allProjectIdMap.get(projectId);
            // 特殊项目工时条目
            boolean isSpecialProjectEntry;
            // 直属下级的工时条目
            boolean isSubUsersEntry;
            // 当前人员是铁三角
            boolean isEntryUserIronTriangle;
            // 当前人员是任务负责人
            boolean isEntryUserTaskLeader;
            // 当前用户（审核当前人员日报的人）是铁三角
            boolean isCurrUserIronTriangle;
            // 当前用户（审核当前人员日报的人）是任务负责人
            boolean isCurrUserTaskLeader;
            // 正在查询“我负责的”
            boolean isInChargeQuery = EnumUtils.valueEquals(dto.getInCharge(), YesOrNoEnum.YES);
            List<ProjectTaskeUser> taskLeaders;

            if (project == null) {
                return false;
            }
            isSpecialProjectEntry = specialProjectIds.contains(projectId);
            entryUserId = entry.getUserId();
            isSubUsersEntry = subUserIds.contains(entryUserId);
            isEntryUserIronTriangle = entryUserId.equals(project.getManagerUserId()) ||
                    entryUserId.equals(project.getSalesmanUserId()) ||
                    entryUserId.equals(project.getPreSaleUserId());
            taskLeaders = taskIdAndLeadersMap.get(taskId);
            isEntryUserTaskLeader = CollectionUtils.isNotEmpty(taskLeaders) &&
                    taskLeaders.stream().anyMatch(t -> entryUserId.equals(t.getUserId()));
            isCurrUserIronTriangle = triangleProjectIds.contains(projectId);
            isCurrUserTaskLeader = CollectionUtils.isNotEmpty(taskLeaders) &&
                    taskLeaders.stream().anyMatch(leader -> userId.equals(leader.getUserId()));

            /*
             * 以下情况之一可以审核：
             * 不是自己的工时，不是下级工时，不是特殊项目，当前用户是任务负责人（主审）；
             * 不是自己的工时，不是下级工时，是特殊项目，是铁三角的工时，当前用户也是铁三角（主审）；
             * 不是自己的工时，不是下级工时，当前用户是铁三角，是任务负责人的工时，且不是特殊项目或没有勾选“我负责的”（主审）；
             * 不是自己的工时，是下级工时，该下级同时是项目铁三角和任务负责人，或者项目是特殊项目；
             * 不是自己的工时，当前用户是铁三角，没有勾选“我负责的”（辅助审核）；
             * 是自己的工时，是特殊项目
             */
            return (!userId.equals(entryUserId) && (
                    (
                        (isSubUsersEntry && isSpecialProjectEntry) ||
                        (isSpecialProjectEntry && isCurrUserIronTriangle && isEntryUserIronTriangle) ||
                        (isSubUsersEntry && isEntryUserTaskLeader && isEntryUserIronTriangle) ||
                        (isCurrUserIronTriangle && isEntryUserTaskLeader && !isEntryUserIronTriangle && !isSpecialProjectEntry) ||
                        (isCurrUserTaskLeader && !isSpecialProjectEntry && !isEntryUserTaskLeader) ||
                        (isCurrUserIronTriangle && !isInChargeQuery && (!isEntryUserTaskLeader || !isEntryUserIronTriangle))
                    )
            )) || (userId.equals(entryUserId) && isSpecialProjectEntry && isCurrUserIronTriangle);
        }).collect(Collectors.toList());
    }

    /**
     * 处理、构建日报审核DTO对象和查询参数Map
     * ps: 因旧接口设计，无法直接使用DTO对象，所以需要处理
     * @param dto dto对象
     * @return filter 查询参数Map
     */
    private Map<String, Object> buildDailyReviewAndFilterMap(DailyReviewDTO dto) {
        Long projectId = dto.getProjectId();
        String projectName = dto.getProjectName();
        String taskName = dto.getTaskName();
        String username = dto.getUsername();
        LocalDate startTime = dto.getStartTime();
        LocalDate endTime = dto.getEndTime();
        Long userId = dto.getUserId();
        List<Long> userIds = dto.getUserIds();
        Map<String, Object> filter = Maps.newHashMap();

        if (projectId != null) {
            filter.put(PROJECT_ID_KEY, projectId);
        }
        if (StringUtils.isNotBlank(projectName)) {
            filter.put("projectName", projectName);
        }
        if (StringUtils.isNotBlank(taskName)) {
            filter.put("taskName", taskName);
        }
        if (StringUtils.isNotBlank(username)) {
            filter.put(USERNAME_KEY, username);
        }
        if (startTime != null) {
            filter.put("startDate", startTime);
        }
        if (endTime != null) {
            filter.put("endDate", endTime);
        }
        if (userId != null) {
            filter.put("userId", userId);
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            filter.put("userIds", userIds);
        }
        putApprovalStatusIntoFilterMap(dto.getAuditStatus(), filter);

        return filter;
    }

    @Override
    public ApiResult<DailyReviewProjectViewTotalVO> projectDimensionViewTotal(DailyReviewDTO totalDto) {
        List<DailyPaperEntry> entries = findEntriesAuditable(totalDto);
        DailyReviewProjectViewTotalVO result;
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal normalHours = BigDecimal.ZERO;
        BigDecimal addedHours = BigDecimal.ZERO;
        BigDecimal workOvertimeHours = BigDecimal.ZERO;
        BigDecimal restOvertimeHours = BigDecimal.ZERO;
        BigDecimal holidayOvertimeHours = BigDecimal.ZERO;


        Long projectId = totalDto.getProjectId();
        ProjectInfo projectInfo;

        if (projectId == null) {
            throw new ValidationException("项目ID不能为空");
        }
        projectInfo = projectInfoMapper.selectById(projectId);
        if (entries.isEmpty()) {
            return ApiResult.success(DailyReviewProjectViewTotalVO.empty(projectInfo));
        }
        result = new DailyReviewProjectViewTotalVO();
        result.setProjectId(String.valueOf(projectId));
        result.setProjectName(entries.get(0).getProjectName());
        result.setApprovalNum(entries.size());
        for (DailyPaperEntry entry : entries) {
            totalHours = totalHours.add(entry.getNormalHours()).add(entry.getAddedHours());
            normalHours = normalHours.add(entry.getNormalHours());
            addedHours = addedHours.add(entry.getAddedHours());
            workOvertimeHours = workOvertimeHours.add(entry.getWorkOvertimeHours());
            restOvertimeHours = restOvertimeHours.add(entry.getRestOvertimeHours());
            holidayOvertimeHours = holidayOvertimeHours.add(entry.getHolidayOvertimeHours());
        }
        result.setTotalHours(totalHours);
        result.setNormalHours(normalHours);
        result.setAddedHours(addedHours);
        result.setHolidayOvertimeHours(holidayOvertimeHours.setScale(2, RoundingMode.HALF_UP).toString());
        result.setRestOvertimeHours(restOvertimeHours.setScale(2, RoundingMode.HALF_UP).toString());
        result.setWorkOvertimeHours(workOvertimeHours.setScale(2, RoundingMode.HALF_UP).toString());
        return ApiResult.success(result);
    }

    @Override
    public ApiResult<Page<DailyReviewProjectViewVO>> projectDimensionViewPage(DailyReviewDTO dto) {
        List<DailyPaperEntry> entries = findEntriesAuditable(dto);
        Page<DailyPaperEntry> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        List<DailyPaperEntry> pagedRecords;
        Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> submissionDateAndTomorrowPlanTable;
        Map<Long, Integer> dailyPaperIdAndFillingStateMap;

        if (entries.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(page, x -> null));
        }
        entries=entries.stream()
                .sorted(Comparator.comparing(DailyPaperEntry::getSubmissionDate, Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(DailyPaperEntry::getCtime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

        pagedRecords = PageUtils.doPaging(entries, page, true);
        if (pagedRecords.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(page, x -> null));
        }
        // 获取昨日计划
        submissionDateAndTomorrowPlanTable = findLastDayPlanEntries(pagedRecords);
        // 获取日报对象（用于取得填报状态）
        dailyPaperIdAndFillingStateMap = findDailyPaperIdAndFillingStateMapByEntries(pagedRecords);

        List<Holiday> holidayList = holidayMapper.findHolidayList();
        LinkedHashMap<LocalDate, Integer>  holidayMap
                = holidayList.stream()
                .filter(h-> h.getHolidayType()!=null)
                .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));
        return ApiResult.success(PageUtils.mapTo(page, e -> {
            List<TomorrowPlanPaperEntry> lastDayPlanEntries = ObjectUtils.defaultIfNull(
                    submissionDateAndTomorrowPlanTable.get(e.getUserId(), e.getSubmissionDate()),
                    ImmutableList.of()
            );
            Integer fillingState = dailyPaperIdAndFillingStateMap.get(e.getDailyPaperId());
            Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
            return DailyReviewProjectViewVO.of(
                    e,
                    IterableUtils.find(lastDayPlanEntries, x -> e.getTaskId().equals(x.getTaskId())),
                    ObjectUtils.defaultIfNull(fillingState, DailyPaperFillingStateEnum.NORMAL.getValue()),
                    holidayMap,
                    costTaskCategoryMap
            );
        }));
    }

    /**
     * 根据日报条目集合，查询昨日计划
     * @param entries 日报条目集合
     * @return 用户id-日报条目提交日期-昨日计划
     */
    private Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> findLastDayPlanEntries(List<DailyPaperEntry> entries) {
        Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> result = HashBasedTable.create();
        int size = entries.size();
        if(size == 0){
            return result;
        }
        Set<Long> userIds = Sets.newHashSetWithExpectedSize(size);
        Set<LocalDate> submissionDates = Sets.newHashSetWithExpectedSize(size);
        List<TomorrowPlanPaperVo> resultList;

        entries.forEach(e -> {
            userIds.add(e.getUserId());
            submissionDates.add(e.getSubmissionDate());
        });
        resultList = tomorrowPlanPaperEntryMapper.findBySubmissionDatesAndUserIds(
                submissionDates, userIds
        );

        resultList.forEach(e -> {
            Long userId = e.getUserId();
            LocalDate selectDate = e.getSelectDate();
            List<TomorrowPlanPaperEntry> list = result.get(userId, selectDate);

            if (list == null) {
                list = Lists.newArrayList();
                result.put(userId, selectDate, list);
            }

            TomorrowPlanPaperEntry tomorrowPlanPaperEntry = new TomorrowPlanPaperEntry();
            BeanUtil.copyProperties(e, tomorrowPlanPaperEntry);
            list.add(tomorrowPlanPaperEntry);
        });

        return result;
    }

    /**
     * 获取 日报ID-填报状态Map
     * @param entries 日报条目集合
     * @return 日报ID-填报状态Map
     */
    private Map<Long, Integer> findDailyPaperIdAndFillingStateMapByEntries(List<DailyPaperEntry> entries) {
        return dailyPaperMapper.selectBatchIds(
                entries.stream().map(DailyPaperEntry::getDailyPaperId).collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(BaseEntity::getId, DailyPaper::getFillingState));
    }

    @Override
    public ApiResult<Page<DailyReviewReuseAndDeliveryProjectVO>> reuseAndDeliveryPage(
            DailyReviewReuseAndDeliveryProjectDTO dto
    ) {
        Page<DailyReviewReuseAndDeliveryProjectBO> page = Page.of(dto.getPageNumber(), dto.getPageSize());
        Map<String, Object> filter = buildReuseAndDeliveryReviewProjectFilterMap(dto);
        if (filter == null) {
            return ApiResult.success(PageUtils.mapTo(page, x -> null));
        }
        page = projectInfoMapper.findReuseAndDeliveryReview(page, filter);

        return ApiResult.success(PageUtils.mapTo(page, DailyReviewReuseAndDeliveryProjectVO::from));
    }

    /**
     * 构建复用+交付项目审核的过滤条件Map
     * ps: 因旧接口设计，无法直接使用DTO对象，所以需要处理
     * @param dto 前端参数dto
     * @return filterMap，如果返回null，说明没有可审核的项目
     */
    @Nullable
    private Map<String, Object> buildReuseAndDeliveryReviewProjectFilterMap(DailyReviewReuseAndDeliveryProjectDTO dto) {
        // 查询当前用户可以审核的项目id（铁三角逻辑）
        List<Long> projectIds = projectInfoMapper.findIdAuditableByTriangleUserId(SecurityUtils.getUser().getId());
        Map<String, Object> filter;
        String projectName;

        if (projectIds.isEmpty()) {
            return null;
        }
        filter = Maps.newHashMap();
        filter.put("projectIds", projectIds);
        putApprovalStatusIntoFilterMap(dto.getAuditStatus(), filter);
        projectName = dto.getProjectName();
        if (StringUtils.isNotBlank(projectName)) {
            filter.put("projectName", projectName);
        }

        return filter;
    }

    /**
     * 向过滤条件Map中添加approvalStatus
     * @param auditStatus dto中的审批状态 0待审批 1已审批 YesOrNoEnum
     * @param filter 过滤条件Map
     */
    private void putApprovalStatusIntoFilterMap(Integer auditStatus, Map<String, Object> filter) {
        filter.put("approvalStatus", EnumUtils.valueEquals(auditStatus, YesOrNoEnum.NO) ?
                ApprovalStatusEnum.DSH.getValue() : ApprovalStatusEnum.YTG.getValue()
        );
    }

    @Override
    public ApiResult<DailyReviewReuseAndDeliveryViewTotalVO> reuseAndDeliveryViewTotal(DailyReviewTotalDTO dto) {
        Long projectId = dto.getProjectId();
        Long userId = SecurityUtils.getUser().getId();
        // 判断当前projectId是否为可审核的
        List<Long> projectIdsAuditable = projectInfoMapper.findIdAuditableByTriangleUserId(userId);
        Map<String, Object> filter;

        if (projectId == null || !projectIdsAuditable.contains(projectId)) {
            return ApiResult.success(DailyReviewReuseAndDeliveryViewTotalVO.empty());
        }
        filter = buildReuseAndDeliveryReviewViewTotalFilterMap(dto);

        return ApiResult.success(DailyReviewReuseAndDeliveryViewTotalVO.from(
                personnelReuseMapper.findReuseAndDeliveryViewTotal(filter)
        ));
    }

    /**
     * 处理、构建日报审核DTO对象和查询参数Map
     * ps: 因旧接口设计，无法直接使用DTO对象，所以需要处理
     * @param dto dto对象
     * @return filterMap
     */
    private Map<String, Object> buildReuseAndDeliveryReviewViewTotalFilterMap(DailyReviewTotalDTO dto) {
        Map<String, Object> filter = Maps.newHashMap();
        String username = dto.getUsername();

        filter.put(PROJECT_ID_KEY, dto.getProjectId());
        putApprovalStatusIntoFilterMap(dto.getAuditStatus(), filter);
        putStartEndTimeIntoFilterMap(dto.getStartTime(), dto.getEndTime(), filter);
        if (StringUtils.isNotBlank(username)) {
            filter.put(USERNAME_KEY, username);
        }

        return filter;
    }

    @Override
    public Page<DailyReviewReuseAndDeliveryViewVO> reuseAndDeliveryViewPage(DailyReviewReuseAndDeliveryViewDTO dto) {
        // 判断当前查询的projectId是否为可审核的id
        Long userId = SecurityUtils.getUser().getId();
        Long projectId = dto.getProjectId();
        List<Long> projectIdsAuditable = projectInfoMapper.findIdAuditableByTriangleUserId(userId);
        Page<DailyReviewReuseAndDeliveryViewBO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());

        if (projectId == null || !projectIdsAuditable.contains(projectId)) {
            return PageUtils.mapTo(page, x -> null);
        }
        Map<String, Object> filter = buildReuseAndDeliveryReviewViewFilterMap(dto);
        personnelReuseMapper.findReuseAndDeliveryView(page, filter);

        return PageUtils.mapTo(page, DailyReviewReuseAndDeliveryViewVO::from);
    }

    public List<DailyReviewReuseAndDeliveryViewVO> reuseAndDeliveryViewList(DailyReviewReuseAndDeliveryViewDTO dto) {
        // 判断当前查询的projectId是否为可审核的id
        Long userId = SecurityUtils.getUser().getId();
        Long projectId = dto.getProjectId();
        List<Long> projectIdsAuditable = projectInfoMapper.findIdAuditableByTriangleUserId(userId);
        
        if (projectId == null || !projectIdsAuditable.contains(projectId)) {
            return Collections.emptyList();
        }
        Map<String, Object> filter = buildReuseAndDeliveryReviewViewFilterMap(dto);
        List<DailyReviewReuseAndDeliveryViewBO> records = personnelReuseMapper.findReuseAndDeliveryView( filter);
        return records.stream().map(DailyReviewReuseAndDeliveryViewVO::from).collect(Collectors.toList());
    }

    /**
     * 处理、构建日报审核DTO对象和查询参数Map
     * ps: 因旧接口设计，无法直接使用DTO对象，所以需要处理
     * @param dto dto对象
     * @return filterMap
     */
    private Map<String, Object> buildReuseAndDeliveryReviewViewFilterMap(DailyReviewReuseAndDeliveryViewDTO dto) {
        Map<String, Object> filter;
        String username = dto.getUsername();

        filter = Maps.newHashMap();
        putApprovalStatusIntoFilterMap(dto.getAuditStatus(), filter);
        filter.put(PROJECT_ID_KEY, dto.getProjectId());
        if (StringUtils.isNotBlank(username)) {
            filter.put(USERNAME_KEY, username);
        }
        putStartEndTimeIntoFilterMap(dto.getStartTime(), dto.getEndTime(), filter);

        return filter;
    }

    /**
     * 处理日期查询参数将其存入filterMap
     * @param startTimeStr eg. 2023-12
     * @param endTimeStr eg. 2024-01
     * @param filter filterMap
     */
    private void putStartEndTimeIntoFilterMap(
            String startTimeStr, String endTimeStr, Map<String, Object> filter
    ) {
        LocalDate startTime;
        LocalDate endTime;

        try {
            if (StringUtils.isNotBlank(startTimeStr)) {
                startTime = LocalDateTimeUtil.parseDate(startTimeStr + "-01", DatePattern.NORM_DATE_FORMATTER);
                filter.put("startTime", startTime);
            }
            if (StringUtils.isNotBlank(endTimeStr)) {
                endTime = LocalDateTimeUtil.parseDate(endTimeStr + "-01", DatePattern.NORM_DATE_PATTERN);
                filter.put("endTime", endTime);
            }
        } catch (DateTimeParseException e) {
            log.warn("日期传参异常，{} {}", startTimeStr, endTimeStr);

            throw new ValidationException("日期传参异常");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> reuseAndDeliveryOneAudit(DailyReviewReuseAndDeliveryViewDTO filter) {
        // 获取当前页的数据
        List<DailyReviewReuseAndDeliveryViewVO> records = reuseAndDeliveryViewPage(filter).getRecords();
        // 区分人才复用和交付人员 <人才复用或交付人员，<填报的工时明细>>，并过滤已归档的日期的数据
        try {
            updateReuseAndDeliveryApprovalStatus(records,ApprovalStatusEnum.YTG);
        } catch (Exception e) {
            log.error("交付复用一键审核报错", e);

            return ApiResult.failure("审核异常，请联系管理员");
        }
//       //【通过/不通过】【项目名称】【待审核人数】个人，汇总工时为【沉时数量】人天
//        bcpLoggerUtils.log(FunctionConstants.REUSE_WORKING_HOURS_REVIEW, LogContentEnum.REUSE_WORKING_HOURS_REVIEW,
//                auditableIds.size(),totalHours);
        return ApiResult.success("审核成功");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ApiResult<String> projectDimensionOneAudit(String dailyPaperEntryIdStr) {
        if (StrUtil.isEmptyIfStr(dailyPaperEntryIdStr)) {
            return ApiResult.failure(NO_DAILY_PAPER_TO_BE_REVIEWED);
        }

        List<Long> dailyPaperEntryIds = Arrays.stream(dailyPaperEntryIdStr.split(","))
                .map(Long::parseLong).collect(Collectors.toList());

        if (dailyPaperEntryIds.isEmpty()){
            return ApiResult.failure(NO_DAILY_PAPER_TO_BE_REVIEWED);
        }

        // 查询日报及其对应填报日期的归档状态
        List<DailyPaperEntryFilingDTO> filing = dailyPaperEntryMapper.selectFilingByDailyPaperIds(dailyPaperEntryIds);

        // 区分可审核的日报和对应日期已归档的日期
        List<Long> auditableIds = new ArrayList<>();
        List<Long> dailyPaperIds = new ArrayList<>();
        Set<String> filingDate = new HashSet<>();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
        List<DailyPaperEntry> dailyPaperEntries = new ArrayList<>();
        //批量审核总工时统计
        AtomicReference<BigDecimal> totalHours = new AtomicReference<>(BigDecimal.ZERO);
        filing.forEach(f -> {
            if (FilingTypeEnum.WGD.getValue().equals(f.getFiled())) {
                auditableIds.add(f.getDailyPaperEntryId());
                dailyPaperIds.add(f.getDailyPaperId());
                totalHours.set(totalHours.get().add(f.getNormalHours()).add(f.getAddedHours()));
            } else {
                String date = dtf.format(f.getSubmissionDate());
                filingDate.add(date);
            }
        });

        try {
            if (CollectionUtils.isNotEmpty(auditableIds)) {
                //批量审核
                ChangeApprovalStatusDTO changeApprovalStatusDTO = new ChangeApprovalStatusDTO();
                changeApprovalStatusDTO.setApprovalName(SecurityUtils.getUser().getName());
                changeApprovalStatusDTO.setApprovalId(SecurityUtils.getUser().getId());
                changeApprovalStatusDTO.setMtime(new Timestamp(System.currentTimeMillis()));
                dailyPaperEntryMapper.batchApproval(auditableIds, changeApprovalStatusDTO);
                //批量更新日报状态 、实际人工成本
                dailyPaperEntries = dailyPaperEntryMapper.selectList(Wrappers.<DailyPaperEntry>lambdaQuery()
                        .in(DailyPaperEntry::getId,auditableIds));
                //根据日报id集合 更新外层日报状态
                dailyPaperService.updateApprovalStatusIfNeeded(dailyPaperIds, ApprovalStatusEnum.YTG);

                batchUpdateTaskStartDateOneAudit(auditableIds);
            }
        } catch (Exception e) {
            log.error("工时审核（项目维度）一键审批接口报错", e);
            return ApiResult.failure("审核异常，请联系管理员");
        }
        //计算工单实际人工成本
        batchUpdateTaskActualLaborCost(dailyPaperEntries);
        if (CollectionUtils.isNotEmpty(filingDate)) {
            StringBuilder msg = new StringBuilder();
            for (String date : filingDate) {
                msg.append(date).append("月份 ");
            }
            msg.append("月份工时数据已归档，请联系管理员，未归档数据已审核成功！");
            return ApiResult.success(null, msg.toString());
        }
        //批量通过【审核条数】条工时，共【总工时数】
        bcpLoggerUtils.log(FunctionConstants.REVIEW_OF_DAILY_WORK_HOURS_REPORT, LogContentEnum.BATCH_REVIEW,
                auditableIds.size(),totalHours);
        return ApiResult.success("一键审核成功！");
    }

    @Override
    @Deprecated
    public ApiResult<Map<String, Integer>> unauditedNum(Long userId) {
        return ApiResult.success(null);
    }

    @Override
    public ApiResult<DailyProjectDetailsFindTotalVO> dailyProjectDetailsFindTotal(
            DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO
    ) {
        DailyReviewDTO dto = transformToDailyReviewDTO(null, dailyProjectDetailsFindPageDTO);
        List<DailyPaperEntry> entries = findEntriesAuditable(dto);
        DailyProjectDetailsFindTotalVO result = new DailyProjectDetailsFindTotalVO();
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal normalHours = BigDecimal.ZERO;
        BigDecimal addedHours = BigDecimal.ZERO;
        long size = entries.size();
        DailyPaperEntry entry;
        Integer approvalStatus;

        if (size < 1) {
            ProjectInfo projectInfo = projectInfoMapper.selectById(dailyProjectDetailsFindPageDTO.getProjectId());
            if (Optional.ofNullable(projectInfo).isPresent()) {
                result.setProjectId(String.valueOf(projectInfo.getId()));
                result.setProjectName(projectInfo.getItemName());
                result.setNormalHours(normalHours);
                result.setAddedHours(addedHours);
                result.setTotalHours(totalHours);
                result.setApprovalStatus(dto.getAuditStatus());
                result.setApprovalStatusName(dto.getAuditStatus() == 0 ? ApprovalStatusEnum.DSH.getName() : ApprovalStatusEnum.YTG.getName());
                result.setSubmissionDate(dto.getStartTime());
                result.setNumber(0L);
                result.setNumberTotal(0L);
            }
            return ApiResult.success(result);
        }
        entry = entries.get(0);
        approvalStatus = entry.getApprovalStatus();
        result.setProjectId(String.valueOf(entry.getProjectId()));
        result.setProjectName(entry.getProjectName());
        result.setSubmissionDate(entry.getSubmissionDate());
        for (DailyPaperEntry e : entries) {
            totalHours = totalHours.add(e.getNormalHours()).add(e.getAddedHours());
            normalHours = normalHours.add(e.getNormalHours());
            addedHours = addedHours.add(e.getAddedHours());
        }
        result.setTotalHours(totalHours);
        result.setNormalHours(normalHours);
        result.setAddedHours(addedHours);
        result.setNumber(size);
        result.setNumberTotal(size);
        result.setApprovalStatus(approvalStatus);
        result.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, approvalStatus));

        return ApiResult.success(result);

    }

    @Override
    public ApiResult<Page<DailyReviewPersonnelViewVO>> dailyPersonnelViewFindPage(DailyReviewDTO dto) {
        List<DailyPaperEntry> entries = findEntriesAuditable(dto);
        Page<DailyPaperEntry> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        List<DailyPaperEntry> pagedRecords;
        Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> submissionDateAndTomorrowPlanTable;
        Map<Long, Integer> dailyPaperIdAndFillingStateMap;

        if (entries.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(page, x -> null));
        }
        pagedRecords = PageUtils.doPaging(entries, page, true);
        //排序
        List<DailyPaperEntry> records = page.getRecords();
        records=records.stream()
                .sorted(Comparator.comparing(DailyPaperEntry::getSubmissionDate, Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(DailyPaperEntry::getCtime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
        page.setRecords(records);
        if (pagedRecords.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(page, x -> null));
        }
        // 获取昨日计划
        submissionDateAndTomorrowPlanTable = findLastDayPlanEntries(pagedRecords);
        dailyPaperIdAndFillingStateMap = findDailyPaperIdAndFillingStateMapByEntries(pagedRecords);
        //节假日
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        LinkedHashMap<LocalDate, Integer>  holidayMap
                = holidayList.stream()
                .filter(h-> h.getHolidayType()!=null)
                .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));
        return ApiResult.success(PageUtils.mapTo(page, entry -> {
            List<TomorrowPlanPaperEntry> lastDayPlanEntries = ObjectUtils.defaultIfNull(
                    submissionDateAndTomorrowPlanTable.get(entry.getUserId(), entry.getSubmissionDate()),
                    ImmutableList.of()
            );
            Integer fillingState = dailyPaperIdAndFillingStateMap.get(entry.getDailyPaperId());
            Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
            return DailyReviewPersonnelViewVO.of(
                    entry,
                    IterableUtils.find(lastDayPlanEntries, x -> entry.getTaskId().equals(x.getTaskId())),
                    ObjectUtils.defaultIfNull(fillingState, DailyPaperFillingStateEnum.NORMAL.getValue()),
                    holidayMap,
                    costTaskCategoryMap
            );
        }));
    }

    @Override
    public ApiResult<DailyReviewPersonnelTotalVO> dailyPersonnelFindTotal(DailyReviewDTO dto) {
        List<DailyPaperEntry> entries = findEntriesAuditable(dto);
        int approvalNum = entries.size();
        BigDecimal normalHours = BigDecimal.ZERO;
        BigDecimal addedHours = BigDecimal.ZERO;
        BigDecimal workOvertimeHours = BigDecimal.ZERO;
        BigDecimal restOvertimeHours = BigDecimal.ZERO;
        BigDecimal holidayOvertimeHours = BigDecimal.ZERO;
        if (entries.isEmpty()) {
            return ApiResult.success(DailyReviewPersonnelTotalVO.empty());
        }
        for (DailyPaperEntry entry : entries) {
            normalHours = normalHours.add(entry.getNormalHours());
            addedHours = addedHours.add(entry.getAddedHours());
            workOvertimeHours = workOvertimeHours.add(entry.getWorkOvertimeHours());
            restOvertimeHours = restOvertimeHours.add(entry.getRestOvertimeHours());
            holidayOvertimeHours = holidayOvertimeHours.add(entry.getHolidayOvertimeHours());
        }

        return ApiResult.success(DailyReviewPersonnelTotalVO.of(approvalNum, normalHours, addedHours,
                workOvertimeHours,restOvertimeHours,holidayOvertimeHours));
    }

    @Override
    public ApiResult<List<Tree<String>>> dailyPersonnelFindDeptUserTree(DailyReviewDTO dto) {
        List<DailyPaperEntry> entries;
        Set<String> userIds;
        List<Tree<String>> deptUserTreeList;
        Object cacheResultTree;
        String userId = String.valueOf(SecurityUtils.getUser().getId());
        String cacheKey = PmsConstants.RDK_DAILY_PERSONNEL_FIND_DEPT_USER_TREE + userId + ":" + dto.getAuditStatus();

        cacheResultTree = redisTemplate.opsForValue().get(cacheKey);
        if (cacheResultTree != null) {
            return ApiResult.success(CastUtils.cast(cacheResultTree));
        }
        entries = findEntriesAuditable(dto);
        if (CollectionUtils.isEmpty(entries)) {
            return ApiResult.success(cacheDeptUserTree(ImmutableList.of(), cacheKey));
        }
        deptUserTreeList = CastUtils.cast(remoteDeptService.getDeptUserTree(-1L).getData());
        if (CollectionUtils.isEmpty(deptUserTreeList)) {
            return ApiResult.success(cacheDeptUserTree(ImmutableList.of(), cacheKey));
        }
        userIds = entries.stream().map(e -> String.valueOf(e.getUserId())).collect(Collectors.toSet());
        deptUserTreeList = deptUserTreeList.stream()
                // 重新构建树（经过中台返回结果、反序列化后，Tree类型已变为LinkedHashMap，要通过重新构建树的方式转变回来）
                .map(this::rebuildTree)
                .filter(Objects::nonNull)
                // 过滤部门用户树中没有下挂人员的部门和无需审核工时的人员
                .map(tree -> filterDeptUserTreeByDailyEntry(userIds, tree))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return ApiResult.success(cacheDeptUserTree(deptUserTreeList, cacheKey));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reuseAndDeliveryBatchCancelReview(DailyReviewReuseAndDeliveryViewDTO dto) {
        // 判断当前查询的projectId是否为可审核的id
        Page<DailyReviewReuseAndDeliveryViewVO> page = reuseAndDeliveryViewPage(dto);
        List<DailyReviewReuseAndDeliveryViewVO> records = page.getRecords();
        try {
            updateReuseAndDeliveryApprovalStatus(records, ApprovalStatusEnum.DSH);
        } catch (Exception e) {
            log.error("交付复用取消审核报错", e);
            throw new ServiceException("取消审核异常，请联系管理员");
        }
    }

    private void updateReuseAndDeliveryApprovalStatus(List<DailyReviewReuseAndDeliveryViewVO> records,ApprovalStatusEnum approvalStatusEnum) {
        // 区分人才复用和交付人员 <人才复用或交付人员，<填报的工时明细>>，并过滤已归档的日期的数据
        Map<String, List<DailyReviewReuseAndDeliveryViewVO>> collect = CollStreamUtil.groupByKey(records, DailyReviewReuseAndDeliveryViewVO::getType);
        PigxUser user = SecurityUtils.getUser();
        String auditName = user.getName();
        Long auditId = user.getId();
        LocalDateTime now = LocalDateTime.now();
        List<Long> reuseIds;
        // 修改人才复用相关数据的审核状态
        if (collect.containsKey(DailyPaperTypeEnum.TALENT_REUSE_MAN_HOUR.getDesc())) {
            reuseIds = collect.get(DailyPaperTypeEnum.TALENT_REUSE_MAN_HOUR.getDesc())
                    .stream()
                    .map(entry -> Long.parseLong(entry.getId()))
                    .collect(Collectors.toList());
            personnelReuseMapper.updateApprovalStatusByIds(approvalStatusEnum.getValue(), auditName, auditId, now, reuseIds);
        }
        // 修改交付人员相关的审核状态
        if (collect.containsKey(DailyPaperTypeEnum.DELIVERER_MAN_HOUR.getDesc())) {
            reuseIds = collect.get(DailyPaperTypeEnum.DELIVERER_MAN_HOUR.getDesc())
                    .stream()
                    .map(entry -> Long.parseLong(entry.getId()))
                    .collect(Collectors.toList());
            personnelDeliveryHourMapper.updateApprovalStatusByIds(approvalStatusEnum.getValue(), auditName, auditId, now, reuseIds);
        }
    }


    /**
     * 根据TreeMap重建Tree（如果结点removeFlag为true，则新树中不会包含该结点）
     * @param treeMap HuTool Tree对象反序列化为的LinkedHashMap
     * @return Tree，如果无结点，返回null
     */
    @Nullable
    private Tree<String> rebuildTree(Map<String, Object> treeMap) {
        List<TreeNode<String>> treeNodes = Lists.newArrayList();

        treeMapForEach(treeMap, node -> {
            Boolean removeFlag = CastUtils.cast(node.get(REMOVE_FLAG_KEY));
            TreeNode<String> newNode;
            boolean isDept;
            Map<String, Object> extra;

            if (BooleanUtils.toBoolean(removeFlag)) {
                return;
            }
            extra = Maps.newHashMapWithExpectedSize(7);
            newNode = new TreeNode<>();
            isDept = CastUtils.cast(node.get(IS_DEPT_KEY));
            newNode.setId(CastUtils.cast(node.get("id")));
            newNode.setName(CastUtils.cast(node.get("name")));
            newNode.setWeight(CastUtils.cast(node.get("weight")));
            newNode.setParentId(CastUtils.cast(node.get("parentId")));
            extra.put("uid", node.get("uid"));
            extra.put("sortOrder", node.get("sortOrder"));
            extra.put(IS_DEPT_KEY, isDept);
            extra.put("status", node.get("status"));
            if (!isDept) {
                extra.put("isAccount", node.get("isAccount"));
                extra.put("online", node.get("online"));
                extra.put("staffId", node.get("staffId"));
            }
            newNode.setExtra(extra);
            treeNodes.add(newNode);
        });
        if (treeNodes.isEmpty()) {
            return null;
        }

        return TreeUtil.build(treeNodes, "-1").get(0);
    }

    /**
     * 遍历树Map
     * @param tree 由HuTool Tree反序列化成的TreeMap
     * @param consumer 遍历回调
     */
    private void treeMapForEach(Map<String, Object> tree, Consumer<Map<String, Object>> consumer) {
        List<Map<String, Object>> children = CastUtils.cast(tree.get("children"));

        consumer.accept(tree);
        if (CollectionUtils.isNotEmpty(children)) {
            children.forEach(child -> treeMapForEach(child, consumer));
        }
    }

    /**
     * 根据用户ID过滤树中的无关人员，以及过滤后没有下挂人员的部门
     * @param userIds 用户ID列表
     * @param tree 部门用户树
     * @return 过滤后的部门用户树，如果整棵树被过滤掉了，返回null
     */
    @Nullable
    private Tree<String> filterDeptUserTreeByDailyEntry(Set<String> userIds, Tree<String> tree) {
        Tree<String> resultTree;

        if (tree.isEmpty()) {
            return null;
        }
        // 过滤，只保留需要审核的用户
        treeMapForEach(tree, deptUser -> {
            boolean isDept = CastUtils.cast(deptUser.get(IS_DEPT_KEY));
            String uId = String.valueOf(deptUser.get("uid"));
            boolean removeFlag = !isDept && !userIds.contains(uId);

            deptUser.put(REMOVE_FLAG_KEY, removeFlag);
        });
        // 过滤，如果部门下没有下挂人员，则设置其removeFlag为true
        putEmptyDeptRemoveFlagInDeptUserTree(tree);
        // 移除所有需要删除的结点（重建树）
        resultTree = rebuildTree(tree);
        if (resultTree == null || !resultTree.hasChild()) {
            return null;
        }

        return resultTree;
    }

    /**
     * 过滤没有下挂任何人员的部门（设置removeFlag）
     * @param tree 部门用户树
     * @return 该树是否被整棵过滤
     */
    private boolean putEmptyDeptRemoveFlagInDeptUserTree(Tree<String> tree) {
        List<Tree<String>> children;
        boolean parentNeedRemove = true;
        boolean childNeedRemove = true;
        boolean removeFlag;
        boolean isDept;

        children = tree.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            tree.put(REMOVE_FLAG_KEY, true);

            return parentNeedRemove;
        } else {
            // 排序，让子结点中的部门排到人员前面
            children.sort((o1, o2) -> {
                boolean isO1Dept = CastUtils.cast(o1.get(IS_DEPT_KEY));
                boolean isO2Dept = CastUtils.cast(o2.get(IS_DEPT_KEY));

                if (isO1Dept && !isO2Dept) {
                    return -1;
                } else if (!isO1Dept && isO2Dept) {
                    return 1;
                } else {
                    return 0;
                }
            });
            for (Tree<String> child : children) {
                removeFlag = CastUtils.cast(child.getOrDefault(REMOVE_FLAG_KEY, false));
                isDept = CastUtils.cast(child.get(IS_DEPT_KEY));
                if (isDept) {
                    childNeedRemove = putEmptyDeptRemoveFlagInDeptUserTree(child);
                    if (parentNeedRemove) {
                        parentNeedRemove = childNeedRemove;
                    }
                } else if (!removeFlag) {
                    parentNeedRemove = false;
                    childNeedRemove = false;
                }
                if (childNeedRemove) {
                    child.put(REMOVE_FLAG_KEY, true);
                }
            }
        }

        return parentNeedRemove;
    }

    /**
     * 缓存部门用户树
     * @param deptUserTreeList 部门用户树
     * @param cacheKey 缓存键
     * @return 被缓存的部门用户树
     */
    private List<Tree<String>> cacheDeptUserTree(List<Tree<String>> deptUserTreeList, String cacheKey) {
        redisTemplate.opsForValue().set(
                cacheKey,
                deptUserTreeList,
                Duration.ofSeconds(10L)
        );

        return deptUserTreeList;
    }

    /**
     * 批量更新任务开始时间（首次填报日报时间）
     *
     * @param ids id列表
     */
    private void batchUpdateTaskStartDateOneAudit(List<Long> ids) {
        // 判断是否是挂靠任务第一次填报日报，是的话填入开始时间
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        List<DailyPaperEntry> dailyPaperEntries = dailyPaperEntryMapper.selectBatchIds(ids);
        Map<Long, LocalDate> taskMap = dailyPaperEntries.stream().collect(Collectors.toMap(DailyPaperEntry::getTaskId, DailyPaperEntry::getSubmissionDate, (a, b) -> a.isBefore(b) ? a : b));
        List<ProjectTaske> tasks = projectTaskeMapper.findByTaskIds(new ArrayList<>(taskMap.keySet())).stream()
                .filter(t -> !Optional.ofNullable(t.getStartDate()).isPresent() || t.getStartDate().isAfter(taskMap.get(t.getId())))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tasks)) {
            tasks.forEach(t -> t.setStartDate(taskMap.get(t.getId())));
            projectTaskeMapper.batchUpdateStartDate(tasks);
        }
    }

    //自动计算人工成本
    private void autoCalculateLaborCost(List<DailyPaperEntry> dailyPaperEntries,List<ChangeApprovalStatusDTO> dtoList) {
        // 获取计算工资的DTO
        List<CalculateLaborCostDTO> calculateLaborCostDTOList = new ArrayList<>();
        dailyPaperEntries.forEach(entry -> {
            calculateLaborCostDTOList.add(new CalculateLaborCostDTO()
                    .setId(entry.getId())
                    .setUserId(entry.getUserId())
                    .setRelateId(entry.getId())
                    .setRelateTypeEnum(CostSalaryRelateTypeEnum.PRE_TASK_ACTUAL_COST)
                    .setNormalHours(entry.getNormalHours())
                    .setWorkOvertimeHours(entry.getWorkOvertimeHours())
                    .setRestOvertimeHours(entry.getRestOvertimeHours())
                    .setHolidayOvertimeHours(entry.getHolidayOvertimeHours()));
        });
        Map<Long, CostSalaryDTO> laborCostMap = costConfigLevelPriceService.batchCalculateLaborCost(calculateLaborCostDTOList);
        dtoList.forEach(dto -> {
            if (YTG.getValue().equals(dto.getApprovalStatus())) {
                CostSalaryDTO costSalaryDTO = laborCostMap.getOrDefault(dto.getId(), CostSalaryDTO.empty());
                BigDecimal laborCost = costSalaryDTO.getLaborCost();
                if (laborCost != null) {
                    dto.setActualLaborCost(laborCost.toString());
                    dto.encrypt();
                }
            }
        });
    }

    /**
     * 批量更新工单实际人工成本
     */
    private void batchUpdateTaskActualLaborCost(List<DailyPaperEntry> dailyPaperEntries) {
        StrBuilder error = new StrBuilder();
        //任务ID列表
        Set<Long> taskIdList = dailyPaperEntries.stream().map(DailyPaperEntry::getTaskId).collect(Collectors.toSet());
        // 已审核通过的工时日报
        List<DailyPaperEntry> ytgPaperEntryList = dailyPaperEntryMapper.selectList(Wrappers.<DailyPaperEntry>lambdaQuery()
                        .in(DailyPaperEntry::getTaskId, taskIdList)
                        .eq(DailyPaperEntry::getApprovalStatus, YTG.getValue()))
                .stream().map(DailyPaperEntry::decrypt)
                .collect(Collectors.toList());
        Map<Long, List<DailyPaperEntry>> entryMapByTaskId = ytgPaperEntryList.stream()
                .collect(Collectors.groupingBy(DailyPaperEntry::getTaskId));
        // 查询本次审核的日报关联的工单
        List<DailyPaperEntryCostTaskDTO> paperEntryCostTaskList = dailyPaperEntryMapper.getPaperEntryListByTaskIds(taskIdList);
        //key:工单id value:任务id
        Map<Long, Long> taskIdMap = paperEntryCostTaskList.stream()
                .collect(Collectors.toMap(DailyPaperEntryCostTaskDTO::getCostTaskId, DailyPaperEntryCostTaskDTO::getTaskId, (a, b) -> a));

        Set<Long> taskIdSet = paperEntryCostTaskList.stream().map(DailyPaperEntryCostTaskDTO::getCostTaskId).collect(Collectors.toSet());
        List<CostDeliverTask> taskList = costDeliverTaskMapper.selectList(Wrappers.<CostDeliverTask>lambdaQuery()
                .in(CostDeliverTask::getId, taskIdSet));

        // 需要更新的工单列表
        List<CostDeliverTask> updateTaskList = new ArrayList<>();
        taskList.forEach(task -> {
            task.decrypt();
            List<DailyPaperEntry> taskPaperEntryList = entryMapByTaskId.get(taskIdMap.get(task.getId()));
            BigDecimal totalNormalHours = BigDecimal.ZERO;
            BigDecimal totalWorkOvertimeHours = BigDecimal.ZERO;
            BigDecimal totalRestOvertimeHours = BigDecimal.ZERO;
            BigDecimal totalHolidayOvertimeHours = BigDecimal.ZERO;
            BigDecimal childrenActualLaborCost = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(taskPaperEntryList)){
                totalNormalHours = taskPaperEntryList.stream()
                        .map(DailyPaperEntry::getNormalHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                totalWorkOvertimeHours = taskPaperEntryList.stream()
                        .map(DailyPaperEntry::getWorkOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                totalRestOvertimeHours = taskPaperEntryList.stream()
                        .map(DailyPaperEntry::getRestOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                totalHolidayOvertimeHours = taskPaperEntryList.stream()
                        .map(DailyPaperEntry::getHolidayOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                childrenActualLaborCost = taskPaperEntryList.stream()
                        .map(DailyPaperEntry::getActualLaborCost)
                        .filter(StrUtil::isNotEmpty)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            task.setActualLaborCost(childrenActualLaborCost.toString());
            task.setNormalHours(totalNormalHours);
            task.setWorkOvertimeHours(totalWorkOvertimeHours);
            task.setRestOvertimeHours(totalRestOvertimeHours);
            task.setHolidayOvertimeHours(totalHolidayOvertimeHours);
            verifyActualLaborCost(error,task);
            updateTaskList.add(BaseBuildEntityUtil.buildUpdate(task));
        });
        if (!error.isEmpty()) {
            throw new ServiceException(String.join("\n", error) + "对应的成本科目预算将超支，请联系项目经理处理～");
        }
        // 批量更新工单实际人工成本
        if (!updateTaskList.isEmpty()) {
            List<CostDeliverTask> distinctList = updateTaskList.stream().distinct().collect(Collectors.toList());
            distinctList.forEach(CostDeliverTask::encrypt);
            costDeliverTaskMapper.updateBatchById(distinctList);
        }
    }

    /**
     * 校验工单实际人工成本是否超出预算
     *
     * @param task 工单
     */
    private void verifyActualLaborCost(StrBuilder error,CostDeliverTask task) {
        //获取已确认人工成本预算
        DeliverCostBudgetListVO matchedBudget = getCostBudgetVO(task);
        if (matchedBudget == null) {
            return;
        }
        //查询项目所有级工单
        List<CostDeliverTask> deliverTaskList = costDeliverTaskMapper.selectList(Wrappers.<CostDeliverTask>lambdaQuery()
                .eq(CostDeliverTask::getProjectId, task.getProjectId()))
                .stream().filter(Objects::nonNull)
                .map(CostDeliverTask::decrypt)
                .collect(Collectors.toList());
        List<CostDeliverTask> subTaskList = deliverTaskList.stream()
                .filter(e -> Objects.equals(task.getAccountOaId(), e.getAccountOaId())
                        && Objects.equals(task.getTaxRate(), e.getTaxRate())
                        && e.getTaskLevel() == 1)
                .collect(Collectors.toList());

        // 1. 计算已完成状态工单的实际人工成本总和
        BigDecimal confirmedBudget = subTaskList.stream()
                .map(CostDeliverTask::getActualLaborCost)
                .filter(StrUtil::isNotEmpty)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 检查是否超出预算
        if (new BigDecimal(task.getActualLaborCost()).add(confirmedBudget).compareTo(matchedBudget.getBudgetAmountIncludedTax()) > 0) {
            error.append("【").append(task.getTaskName()).append("】");
        }
    }

    public DeliverCostBudgetListVO getCostBudgetVO(CostDeliverTask task) {
        // 获取项目最新已确认人工成本预算版本信息
        CostManageVersionVO costManageVersionVO = getLatestConfirmedCostManageVersionVO(task.getProjectId(), ProjectTaskKindEnum.AFTER_SALES_DELIVERY);
        if (costManageVersionVO == null || costManageVersionVO.getVersionId() == null) {
            return null;
        }
        // 查询版本对应人工成本预算明细
        List<DeliverCostBudgetListVO> costBudgetList = costManageEstimationResultsMapper.findCostBudgetByVersionId(
                costManageVersionVO.getVersionId(), CostTypeEnum.RGCB.getValue());
        // 找到匹配的预算科目
        return costBudgetList.stream()
                .filter(budget -> Objects.equals(budget.getAccountOaId(), task.getAccountOaId())
                        && Objects.equals(budget.getTaxRate(), task.getTaxRate()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查询项目最新已确认人工成本预算版本信息
     *
     * @param projectId 项目 ID
     * @return {@link CostManageVersionVO }
     */
    private CostManageVersionVO getLatestConfirmedCostManageVersionVO(Long projectId, ProjectTaskKindEnum taskType) {
        List<CostManageVersionVO> costManageVersionVOList = costManageVersionService.getHistoryVersions(
                new PageRequest(1, 1000),
                CostManageVersionDTO.builder().projectId(projectId).build()).getRecords();
        Stream<CostManageVersionVO> voStream = CollUtil.emptyIfNull(costManageVersionVOList).stream()
                .filter(costManageVersion -> EnumUtils.valueEquals(costManageVersion.getStatus(), CostManageStatusEnum.CONFIRMED))
                .filter(costManageVersion -> !EnumUtils.valueEquals(costManageVersion.getCostBudgetType(), CostBudgetTypeEnum.ABCB))
                .sorted(Comparator.comparing(CostManageVersionVO::getCostBudgetType).reversed()
                        .thenComparing(Comparator.comparing(CostManageVersionVO::getVersionId).reversed()));
        if (taskType == null) {
            return voStream.findFirst().orElse(null);
        }
        switch (taskType) {
            // 售后交付
            case AFTER_SALES_DELIVERY:
                voStream = voStream.filter(costManageVersion -> !EnumUtils.valueEquals(costManageVersion.getCostBudgetType(), CostBudgetTypeEnum.SQCB));
                break;
            case PRE_SALES_SUPPORT:
                voStream = voStream.filter(costManageVersion -> EnumUtils.valueEquals(costManageVersion.getCostBudgetType(), CostBudgetTypeEnum.SQCB));
                break;
            default:
        }
        return voStream.findFirst().orElse(null);
    }
}
