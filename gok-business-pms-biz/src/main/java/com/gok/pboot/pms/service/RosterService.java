package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.vo.RosterSelectionVO;
import org.springframework.data.util.Pair;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 花名册服务
 *
 * <AUTHOR>
 * @version 1.3.4
 */
public interface RosterService extends IService<Roster> {

    /**
     * 根据姓名查询花名册选项
     *
     * @param aliasName 姓名
     * @return 花名册列表选项
     */
    List<RosterSelectionVO> findSelectionList(String aliasName);


    /**
     * 根据用户id查找其对应的上级信息
     *
     * @param userIds 用户 ID
     * @return {@link Map }<{@link Long }, {@link Roster }>
     */
    Map<Long, Roster> findUserLeaderMap(Collection<Long> userIds);

    /**
     * 寻找用户上级
     *
     * @param userId 用户id
     * @return {@link Roster }
     */
    Roster findUserLeader(Long userId);

    /**
     * 根据用户id查找其对应的下属
     *
     * @param userIds 用户 ID
     * @return {@link Map }<{@link Long }, {@link Roster }>
     */
    Map<Long, List<Pair<Long,String>>> findUserUnderlingMap(Collection<Long> userIds);

    Map<Long, Roster> findUserIdMap(Set<Long> userIds);
}
