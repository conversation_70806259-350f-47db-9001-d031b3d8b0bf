package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskeTypeEnum;
import lombok.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 日报条目中的任务对象VO
 *
 * <AUTHOR>
 * @date 2024/01/24
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class TaskInDailyPaperEntryUnifyVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 名称
     */
    private String taskName;

    /**
     * 任务类型
     * {@link ProjectTaskKindEnum}
     */
    private Integer taskKind;

    /**
     * 任务类型txt
     */
    private String taskKindTxt;

    /**
     * 工时审核人姓名列表
     */
    private List<String> auditorNames;

    /**
     * 任务完成/结束标识
     */
    private Boolean taskFinishFlag;

    /**
     * 计划开始日期
     */
    private LocalDate expectStartDate;

    /**
     * 计划结束日期
     */
    private LocalDate expectEndDate;

    /**
     * 类型
     * 0-默认任务 1-工单任务
     * @see ProjectTaskeTypeEnum#getValue()
     */
    private Integer type;

    /**
     * 关联id
     */
    private Long relateId;
}
