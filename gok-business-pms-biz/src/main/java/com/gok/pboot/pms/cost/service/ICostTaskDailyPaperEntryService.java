package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry;
import com.gok.pboot.pms.cost.entity.vo.CostTaskDailyPaperEntryVO;
import com.gok.pboot.pms.entity.dto.SubordinatesDailyPaperDTO;
import com.gok.pboot.pms.entity.vo.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 工单工时条目服务接口
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Deprecated
public interface ICostTaskDailyPaperEntryService extends IService<CostTaskDailyPaperEntry> {


    /**
     * 获取日报条目联系人
     *
     * @param projectId 项目id
     * @param taskId 任务id
     * @return {@link DailyPaperEntryContactsVo}
     */
    DailyPaperEntryContactsVo getContacts(Long projectId, Long taskId);

    /**
     * ~ 分页查询 ~
     *
     * @param pageRequest 分页对象
     * @param filter      查询条件
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/8/23 15:24
     */
    Page<CostTaskDailyPaperEntryVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * ~ 根据日报ID查询 ~
     *
     * @param dailyPaperId 日报ID
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/8/23 15:26
     */
    List<CostTaskDailyPaperEntry> findByDailyPaperId(Long dailyPaperId);

    List<Object> exportExcel(LocalDate startDate, LocalDate endDate, Map<String, Object> filter);

    /**
     * @create by yzs at 2023/5/11
     * @description: 查询下级的日报详细
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    Page<DailyReviewProjectAuditPageVO> findBySubordinate(Page page, SubordinatesDailyPaperDTO dto);

    /**
     * @create by yzs at 2023/5/11
     * @description:统计下级工时
     * @param: dto
     * @return: com.gok.pboot.pms.entity.vo.SubordinatePaperEntryStaticVO
     */
    SubordinatePaperEntryStaticStrVO findBySubordinateStatic(SubordinatesDailyPaperDTO dto);

    /**
     * @create by yzs at 2023/5/12
     * @description:个人面板查询日报详情
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    Page<DailPanelPaperPageVO> findByPanel(Page page, SubordinatesDailyPaperDTO dto);

    /**
     * 统计个人面板-日报详细-总计
     *
     * @param dto 查询条件 startTime，endTime，projectName
     * @return {@link ApiResult <PanelProjectSituationAnalysisVO>}
     */
    R<PanelProjectSituationAnalysisVO> analysisTotal(SubordinatesDailyPaperDTO dto);

    /**
     * 通过任务id获取日报分页
     *
     * @param pageRequest 分页对象
     * @param filter      参数
     * @return {@link Page}<{@link DailyPaperEntryVO}>
     */
    Page<CostTaskDailyPaperEntryVO> findPageByTaskId(PageRequest pageRequest, Map<String, Object> filter);
} 