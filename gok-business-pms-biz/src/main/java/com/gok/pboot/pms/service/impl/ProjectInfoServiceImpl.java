package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.entity.SysDictItem;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.DictConstants;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.eval.entity.vo.EvalSatisfactionSurveyProjectVo;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectMapper;
import com.gok.pboot.pms.mapper.ProjectStakeholderMemberMapper;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.*;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目信息服务
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class ProjectInfoServiceImpl extends ServiceImpl<ProjectInfoMapper, ProjectInfo> implements IProjectInfoService {

    @Value("${oa.annualPlanFileUrl:XXX}")
    private String annualPlanFileUrl;

    private final ProjectInfoMapper mapper;

    private final PmsRetriever pmsRetriever;

    private final IProjectStakeholderMemberService projectStakeholderMemberService;

    private final ProjectStakeholderMemberMapper projectStakeholderMemberMapper;

    private final RosterMapper rosterMapper;

    private final RemoteOutService remoteOutService;

    private final RemoteDeptService remoteDeptService;

    private final IPmsDocImageFileService pmsDocImageFileService;
    private final IProjectDeliverAndAcceptCriteriaService deliverAndAcceptCriteriaService;
    private final IProjectEstimatedCostService estimatedCostService;
    private final IProjectResearchRiskService researchRiskService;
    private final IProjectMemberService memberService;
    private final IContractLedgerService contractLedgerService;
    private final RemoteBcpDictService remoteBcpDictService;

    private final PmsDictUtil pmsDictUtil;
    private final ProjectMapper projectMapper;
    private final ICustomerBusinessService customerBusinessService;
    private final ICustomerBusinessUnitService customerBusinessUnitService;
    private final OaUtil oaUtil;

    @Override
    public Page<ProjectInfoOverViewVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        // 获取用户权限范围内的项目
        List<Long> projectIdsAvailable = pmsRetriever.getProjectIdsAvailable(filter);
        Page<ProjectInfo> page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        if (!"all".equals(filter.get("scope")) && projectIdsAvailable.isEmpty()) {
            return PageUtils.mapTo(page, x -> null);
        }
        filter.put(PmsRetriever.PROJECT_AVAILABLE_KEY, projectIdsAvailable);
        // 查询用户关注项目id列表
        Set<Long> attentionIds = projectMapper.getAttentionProjectList(SecurityUtils.getUser().getId()).stream().map(ProjectVO::getId).collect(Collectors.toSet());
        Integer attention = (Integer) filter.get("isAttention");

        if (BaseConstants.YES.equals(attention)) {
            // 只查询已关注的
            if (attentionIds.isEmpty()) {
                // 如果关注列表为空，不用再走查询
                return PageUtils.mapTo(page, x -> null);
            }
            filter.put("projectIds", attentionIds);
        }
        boolean isNotInternalProject = IsNoInternalProjectEnum.yes.getValue().equals(filter.get("isNotInternalProject"));
        if (isNotInternalProject) {
            parseDeptParam(filter);
        }
        Page<ProjectInfo> poList = mapper.findList(page, filter);
        if (poList.getRecords().isEmpty()) {
            return PageUtils.mapTo(page, x -> null);
        }
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap = SysDeptUtils.getDeptIdMap(deptList);
        return PageUtils.mapTo(page, e -> {
            ProjectInfoOverViewVO vo;
            if (isNotInternalProject) {
                // 内部项目
                vo = ProjectInfoInnerVO.from(e, deptIdMap);
                calculateInnerData((ProjectInfoInnerVO) vo);
            } else {
                // 外部项目
                vo = ProjectInfoOuterVO.from(e, deptIdMap);
            }
            // 添加关注操作
            if (!attentionIds.contains(Long.parseLong(vo.getId()))) {
                vo.setIsAttention(BaseConstants.NO);
            } else {
                vo.setIsAttention(BaseConstants.YES);
            }
            return vo;
        });
    }

    /**
     * 计算内部项目额外属性(这里只有成本进度)
     *
     * @param vo 内部项目对象
     */
    private void calculateInnerData(ProjectInfoInnerVO vo) {
        // 预算成本
        BigDecimal totalBudgetCostIncludeTax = BigDecimalUtils.bigDecimalNullToZero(vo.getEstimatedCost());
        //实际总成本
        BigDecimal actualTotalBudgetCost = BigDecimal.ZERO;
        //成本进度
        if (totalBudgetCostIncludeTax.compareTo(BigDecimal.ZERO) != 0) {
            vo.setProjectCostProgress(actualTotalBudgetCost.divide(totalBudgetCostIncludeTax, 0, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "%");
        } else {
            vo.setProjectCostProgress("0%");
        }
    }

    /**
     * 解析前端传输的部门id字段
     *
     * @param filter 过滤器
     */
    private static void parseDeptParam(Map<String, Object> filter) {
        String projectDeptIdsStr = (String) filter.get("projectDeptIds");
        if (StrUtil.isNotBlank(projectDeptIdsStr)) {
            String[] split = projectDeptIdsStr.split(",");
            List<String> list = Arrays.asList(split);
            List<Long> deptIds = list.stream().map(Long::parseLong).collect(Collectors.toList());
            filter.put("projectDeptIds", deptIds);
        }

        String businessDeptIdsStr = (String) filter.get("businessDeptIds");
        if (StrUtil.isNotBlank(businessDeptIdsStr)) {
            String[] split = businessDeptIdsStr.split(",");
            List<String> list = Arrays.asList(split);
            List<Long> deptIds = list.stream().map(Long::parseLong).collect(Collectors.toList());
            filter.put("businessDeptIds", deptIds);
        }
    }

    @Override
    public ProjectInfoVO getProjectInfoById(Long id) {
        // 获取项目信息
        ProjectInfo projectInfo = mapper.selectById(id);
        // 为空则返回
        if (projectInfo == null) {
            return new ProjectInfoVO();
        }
        // 获取全部数据字典项Map
        HashMultimap<String, PmsDictItem> dictItemMap = pmsDictUtil.getPmsDictItemMap();
        //部门
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap = SysDeptUtils.getDeptIdMap(deptList);
        ProjectInfoVO projectInfoVO = ProjectInfoVO.from(projectInfo, deptIdMap, dictItemMap);
        projectInfoVO.setAnnualProjectPlanFileUrl(annualPlanFileUrl);

        // 经营单元、所属客户
        projectInfoVO.setBusinessUnitName(projectInfo.getBusinessId() == null ?
                StringUtils.EMPTY :
                customerBusinessService.getById(projectInfo.getBusinessId()).getName());
        projectInfoVO.setUnitName(projectInfo.getUnitId() == null ?
                StringUtils.EMPTY :
                customerBusinessUnitService.getById(projectInfo.getUnitId()).getUnitName());

        // B表立项流程ID
        Long bblxfl = projectInfo.getBblxfl();
        // 附件ID转换
        List<Long> xgxxsmwdFileIdList = Optional.ofNullable(projectInfo.getXgxxsmwd())
                .map(value -> Arrays.stream(value.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        List<Long> qtsqyjzlFileIdList = Optional.ofNullable(projectInfo.getQtsqyjzl())
                .map(value -> Arrays.stream(value.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        // 获取文件信息
        List<PmsDocImageFile> xgxxsmwdDocImageFileList = ObjectUtils.isNotEmpty(xgxxsmwdFileIdList)
                ? pmsDocImageFileService.getByDocIds(xgxxsmwdFileIdList)
                : Collections.emptyList();
        List<PmsDocImageFile> qtsqyjzlDocImageFileList = ObjectUtils.isNotEmpty(qtsqyjzlFileIdList)
                ? pmsDocImageFileService.getByDocIds(qtsqyjzlFileIdList)
                : Collections.emptyList();
        // 获取OA附件信息
        projectInfoVO.setXgxxsmwd(getOaFileVoList(xgxxsmwdDocImageFileList, String.valueOf(bblxfl)));
        projectInfoVO.setQtsqyjzl(getOaFileVoList(qtsqyjzlDocImageFileList, String.valueOf(bblxfl)));

        //外部项目
        if (projectInfo.getIsNotInternalProject() != 1) {
            List<SysDictItem> sysDictItems = CollUtil.emptyIfNull(remoteBcpDictService.batchLeverList(new HashSet<>(Arrays.asList("业务类型"))).getData().get("业务类型"));
            List<Long> parentIds = sysDictItems.stream().map(SysDictItem::getParentId).distinct().collect(Collectors.toList());
            SysDictItem projectTypeDictItem = CollUtil.emptyIfNull(sysDictItems)
                    .stream()
                    .filter(e -> StrUtil.equals(String.valueOf(projectInfoVO.getProjectType()), e.getItemValue()) && !parentIds.contains(e.getItemId()))
                    .findAny().orElseGet(SysDictItem::new);
            projectInfoVO.setProjectTypeName(projectTypeDictItem.getItemName());
            return projectInfoVO;
        }

        projectInfoVO.setBusinessDepartment(projectInfo.getBusinessDepartmentId() == null ? StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, projectInfo.getBusinessDepartmentId()));
        // 项目归属部门
        projectInfoVO.setProjectDepartment(projectInfo.getProjectDepartmentId() == null ?
                StrUtil.EMPTY :
                SysDeptUtils.collectFullName(deptIdMap, projectInfo.getProjectDepartmentId()));

        //OA文件
        assembleProjectBusinessRequirementsOaFile(projectInfo, projectInfoVO);

        //OA字典
        Set<Integer> fieldIds = CollUtil.newHashSet(
                Integer.valueOf(DictConstants.CATEGORY_OF_EXPENSE_ITEMS),
                Integer.valueOf(DictConstants.PROJECT_RISK_TYPE),
                Integer.valueOf(DictConstants.IMPACT_LEVER),
                Integer.valueOf(DictConstants.PERSON_LEVER));
        Map<Integer, Map<Integer, String>> projectDictMap = contractLedgerService.getProjectDictMap(fieldIds);
        if (CollUtil.isEmpty(projectDictMap)) {
            throw new BusinessException("查询Oa字典失败");
        }

        //项目交付成果和验收标准
        List<ProjectDeliverAndAcceptCriteria> deliverAndAcceptCriteriaList = deliverAndAcceptCriteriaService.getByProjectId(id);
        if (CollUtil.isNotEmpty(deliverAndAcceptCriteriaList)) {
            projectInfoVO.setProjectDeliverAndAcceptCriteriaVOList(BeanUtil.copyToList(deliverAndAcceptCriteriaList, ProjectDeliverAndAcceptCriteriaVO.class));
        }

        //项目预估成本表Vo
        List<ProjectEstimatedCost> estimatedCostList = estimatedCostService.getByProjectId(id);
        if (CollUtil.isNotEmpty(estimatedCostList)) {
            Map<Integer, String> expenseItemsMap = projectDictMap.get(Integer.valueOf(DictConstants.CATEGORY_OF_EXPENSE_ITEMS));
            List<ProjectEstimatedCostVO> projectEstimatedCostVOS = BeanUtil.copyToList(estimatedCostList, ProjectEstimatedCostVO.class);
            for (int i = 0; i < projectEstimatedCostVOS.size(); i++) {
                ProjectEstimatedCost projectEstimatedCost = estimatedCostList.get(i);
                ProjectEstimatedCostVO projectEstimatedCostVO = projectEstimatedCostVOS.get(i);
                projectEstimatedCostVO.setBudgetAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(projectEstimatedCost.getBudgetAmount(), DecimalFormatUtil.ZERO));
                projectEstimatedCostVO.setBudgetManDays(new BigDecimal(projectEstimatedCost.getBudgetManDays() == null ? 0 : projectEstimatedCost.getBudgetManDays()).setScale(2, RoundingMode.HALF_UP).toString());
                projectEstimatedCostVO.setCostExpenseItemsName(expenseItemsMap.getOrDefault(projectEstimatedCostVO.getCostExpenseItems(), ""));
            }
            projectInfoVO.setProjectEstimatedCostVOList(projectEstimatedCostVOS);
        }

        //研发风险表VO
        List<ProjectResearchRisk> researchRiskList = researchRiskService.getByProjectId(id);
        if (CollUtil.isNotEmpty(researchRiskList)) {
            Map<Integer, String> riskTypeMap = projectDictMap.get(Integer.valueOf(DictConstants.PROJECT_RISK_TYPE));
            Map<Integer, String> impactLeverMap = projectDictMap.get(Integer.valueOf(DictConstants.IMPACT_LEVER));
            List<ProjectResearchRiskVO> projectResearchRiskVOS = BeanUtil.copyToList(researchRiskList, ProjectResearchRiskVO.class);

            for (int i = 0; i < projectResearchRiskVOS.size(); i++) {
                ProjectResearchRisk projectResearchRisk = researchRiskList.get(i);
                ProjectResearchRiskVO projectResearchRiskVO = projectResearchRiskVOS.get(i);
                if (CollUtil.isNotEmpty(riskTypeMap)) {
                    projectResearchRiskVO.setRiskTypeName(riskTypeMap.getOrDefault(projectResearchRiskVO.getRiskType(), ""));
                }
                if (CollUtil.isNotEmpty(impactLeverMap)) {
                    projectResearchRiskVO.setImpactLevelName(impactLeverMap.getOrDefault(projectResearchRiskVO.getImpactLevel(), ""));
                }
                projectResearchRiskVO.setProbability((projectResearchRisk.getProbability() == null ? BigDecimal.ZERO : projectResearchRisk.getProbability()).setScale(2, RoundingMode.HALF_UP).toString());
            }
            projectInfoVO.setProjectResearchRiskVOList(projectResearchRiskVOS);
        }

        //项目组成员表VO
        List<ProjectMember> projectMemberList = memberService.getByProjectId(id);
        if (CollUtil.isNotEmpty(projectMemberList)) {
            Map<Integer, String> personLeverMap = projectDictMap.get(Integer.valueOf(DictConstants.PERSON_LEVER));
            List<ProjectMemberVO> projectMemberVOS = BeanUtil.copyToList(projectMemberList, ProjectMemberVO.class);

            for (int i = 0; i < projectMemberVOS.size(); i++) {
                ProjectMember projectMember = projectMemberList.get(i);
                ProjectMemberVO projectMemberVO = projectMemberVOS.get(i);
                projectMemberVO.setExpectedEngagement((projectMember.getExpectedEngagement() == null ? BigDecimal.ZERO : projectMember.getExpectedEngagement()).setScale(2, RoundingMode.HALF_UP) + "%");
                if (CollUtil.isNotEmpty(personLeverMap)) {
                    projectMemberVO.setMemberLevelName(personLeverMap.getOrDefault(projectMemberVO.getMemberLevel(), ""));
                }

            }
            projectInfoVO.setProjectMemberVOList(projectMemberVOS);
        }

        return projectInfoVO;
    }

    /**
     * 获取OA附件信息
     *
     * @param docImageFileList OA附件映射列表
     * @param bblxfl           B表立项流程ID
     * @return OA附件信息列表
     */
    public List<OaFileVo> getOaFileVoList(List<PmsDocImageFile> docImageFileList, String bblxfl) {
        List<OaFileVo> oaFileVoList = new ArrayList<>();
        for (PmsDocImageFile file : docImageFileList) {
            // 获取资源数据
            List<OaFileVo> resourcesData = oaUtil.getResourcesData(bblxfl, String.valueOf(file.getOperateUserId()));
            if (ObjectUtils.isEmpty(resourcesData)) {
                return Collections.emptyList();
            }
            // 找到匹配的文件信息
            OaFileVo fileInfo = resourcesData.stream()
                    .filter(f -> Objects.equals(Long.valueOf(f.getId()), file.getImageFileId()))
                    .findFirst()
                    .orElse(null);
            oaFileVoList.add(fileInfo);
        }
        return oaFileVoList;
    }

    @Override
    public ProjectOverViewInnerVO getProjectOverviewInnerById(Long id) {
        ProjectInfo projectInfo = mapper.selectById(id);
        ProjectOverViewInnerVO vo = ProjectOverViewInnerVO.from(projectInfo);
        //部门
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap = SysDeptUtils.getDeptIdMap(deptList);
        vo.setBusinessDepartment(projectInfo.getBusinessDepartmentId() == null ? StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, projectInfo.getBusinessDepartmentId()));
        vo.setProjectDepartment(projectInfo.getProjectDepartmentId() == null ? StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, projectInfo.getProjectDepartmentId()));
        return vo;
    }

    @Override
    public Integer countByUnitId(Long unitId, String projectStatus) {
        return mapper.countByUnitId(unitId, projectStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncMembers() {
        List<ProjectInfo> projectInfos = mapper.findAll();
        if (CollUtil.isEmpty(projectInfos)) {
            return;
        }
        // 获取各项目已存在的铁三角成员
        Map<Long, Triple<Long, Long, Long>> existsMap = new HashMap<>();
        List<ProjectStakeholderMember> members = projectStakeholderMemberMapper.selectList(null);
        List<ProjectStakeholderMember> ironTriangle = new ArrayList<>();
        if (CollUtil.isNotEmpty(members)) {
            ironTriangle = members.stream().filter(m -> Arrays.asList(RoleTypeEnum.PROJECT_MANAGER.getValue(), RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue(), RoleTypeEnum.PROJECT_SALES_MANAGER.getValue()).contains(m.getRoleType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ironTriangle)) {
                // 循环设置铁三角成员
                ironTriangle.forEach(i -> {
                    Triple<Long, Long, Long> triple = existsMap.get(i.getProjectId());
                    if (Optional.ofNullable(triple).isPresent()) {
                        if (RoleTypeEnum.PROJECT_MANAGER.getValue().equals(i.getRoleType())) {
                            existsMap.put(i.getProjectId(), Triple.of(i.getMemberId(), triple.getMiddle(), triple.getRight()));
                        } else if (RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue().equals(i.getRoleType())) {
                            existsMap.put(i.getProjectId(), Triple.of(triple.getLeft(), i.getMemberId(), triple.getRight()));
                        } else if (RoleTypeEnum.PROJECT_SALES_MANAGER.getValue().equals(i.getRoleType())) {
                            existsMap.put(i.getProjectId(), Triple.of(triple.getLeft(), triple.getMiddle(), i.getMemberId()));
                        }
                    } else {
                        if (RoleTypeEnum.PROJECT_MANAGER.getValue().equals(i.getRoleType())) {
                            existsMap.put(i.getProjectId(), Triple.of(i.getMemberId(), null, null));
                        } else if (RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue().equals(i.getRoleType())) {
                            existsMap.put(i.getProjectId(), Triple.of(null, i.getMemberId(), null));
                        } else if (RoleTypeEnum.PROJECT_SALES_MANAGER.getValue().equals(i.getRoleType())) {
                            existsMap.put(i.getProjectId(), Triple.of(null, null, i.getMemberId()));
                        }
                    }
                });
            }
        }
        // 判断哪些是需要新增的
        List<ProjectStakeholderMember> add = new ArrayList<>();
        List<ProjectStakeholderMember> del = new ArrayList<>();
        Map<Long, Roster> userIdMap = Optional.ofNullable(rosterMapper.findUserIdMap(null)).orElse(new HashMap<>());
        List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));
        projectInfos.forEach(p -> {
            Triple<Long, Long, Long> triple = existsMap.get(p.getId());
            Long managerUserId = p.getManagerUserId();
            Long preSaleUserId = p.getPreSaleUserId();
            Long salesmanUserId = p.getSalesmanUserId();
            // 判断铁三角是否已存在或已更换
            if (Optional.ofNullable(triple).isPresent()) {
                if (Optional.ofNullable(managerUserId).isPresent()) {
                    if (!Optional.ofNullable(triple.getLeft()).isPresent() || !triple.getLeft().equals(managerUserId)) {
                        ProjectStakeholderMember member = new ProjectStakeholderMember();
                        member.setProjectId(p.getId());
                        member.setMemberId(managerUserId);
                        Roster roster = userIdMap.get(managerUserId);
                        Optional.ofNullable(roster).ifPresent(r -> {
                            member.setMemberName(roster.getAliasName());
                            member.setPosition(r.getJob());
                            member.setDeptName(projectStakeholderMemberService.buildDeptName(r.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        });
                        member.setRoleType(RoleTypeEnum.PROJECT_MANAGER.getValue());
                        add.add(member);

                        if (Optional.ofNullable(triple.getLeft()).isPresent()) {
                            ProjectStakeholderMember delMember = new ProjectStakeholderMember();
                            delMember.setProjectId(p.getId());
                            delMember.setMemberId(triple.getLeft());
                            delMember.setRoleType(RoleTypeEnum.PROJECT_MANAGER.getValue());
                            del.add(delMember);
                        }
                    }
                } else {
                    if (Optional.ofNullable(triple.getLeft()).isPresent()) {
                        ProjectStakeholderMember delMember = new ProjectStakeholderMember();
                        delMember.setProjectId(p.getId());
                        delMember.setMemberId(triple.getLeft());
                        delMember.setRoleType(RoleTypeEnum.PROJECT_MANAGER.getValue());
                        del.add(delMember);
                    }
                }
                if (Optional.ofNullable(preSaleUserId).isPresent()) {
                    if (!Optional.ofNullable(triple.getMiddle()).isPresent() || !triple.getMiddle().equals(preSaleUserId)) {
                        ProjectStakeholderMember member = new ProjectStakeholderMember();
                        member.setProjectId(p.getId());
                        member.setMemberId(preSaleUserId);
                        Roster roster = userIdMap.get(preSaleUserId);
                        Optional.ofNullable(roster).ifPresent(r -> {
                            member.setMemberName(roster.getAliasName());
                            member.setPosition(r.getJob());
                            member.setDeptName(projectStakeholderMemberService.buildDeptName(r.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        });
                        member.setRoleType(RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue());
                        add.add(member);

                        if (Optional.ofNullable(triple.getMiddle()).isPresent()) {
                            ProjectStakeholderMember delMember = new ProjectStakeholderMember();
                            delMember.setProjectId(p.getId());
                            delMember.setMemberId(triple.getMiddle());
                            delMember.setRoleType(RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue());
                            del.add(delMember);
                        }
                    }
                } else {
                    if (Optional.ofNullable(triple.getMiddle()).isPresent()) {
                        ProjectStakeholderMember delMember = new ProjectStakeholderMember();
                        delMember.setProjectId(p.getId());
                        delMember.setMemberId(triple.getMiddle());
                        delMember.setRoleType(RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue());
                        del.add(delMember);
                    }
                }
                if (Optional.ofNullable(salesmanUserId).isPresent()) {
                    if (!Optional.ofNullable(triple.getRight()).isPresent() || !triple.getRight().equals(salesmanUserId)) {
                        ProjectStakeholderMember member = new ProjectStakeholderMember();
                        member.setProjectId(p.getId());
                        member.setMemberId(salesmanUserId);
                        Roster roster = userIdMap.get(salesmanUserId);
                        Optional.ofNullable(roster).ifPresent(r -> {
                            member.setMemberName(roster.getAliasName());
                            member.setPosition(r.getJob());
                            member.setDeptName(projectStakeholderMemberService.buildDeptName(r.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                        });
                        member.setRoleType(RoleTypeEnum.PROJECT_SALES_MANAGER.getValue());
                        add.add(member);

                        if (Optional.ofNullable(triple.getRight()).isPresent()) {
                            ProjectStakeholderMember delMember = new ProjectStakeholderMember();
                            delMember.setProjectId(p.getId());
                            delMember.setMemberId(triple.getRight());
                            delMember.setRoleType(RoleTypeEnum.PROJECT_SALES_MANAGER.getValue());
                            del.add(delMember);
                        }
                    }
                } else {
                    if (Optional.ofNullable(triple.getRight()).isPresent()) {
                        ProjectStakeholderMember delMember = new ProjectStakeholderMember();
                        delMember.setProjectId(p.getId());
                        delMember.setMemberId(triple.getRight());
                        delMember.setRoleType(RoleTypeEnum.PROJECT_SALES_MANAGER.getValue());
                        del.add(delMember);
                    }
                }
            } else {
                if (Optional.ofNullable(managerUserId).isPresent()) {
                    ProjectStakeholderMember member = new ProjectStakeholderMember();
                    member.setProjectId(p.getId());
                    member.setMemberId(managerUserId);
                    Roster roster = userIdMap.get(managerUserId);
                    Optional.ofNullable(roster).ifPresent(r -> {
                        member.setMemberName(roster.getAliasName());
                        member.setPosition(r.getJob());
                        member.setDeptName(projectStakeholderMemberService.buildDeptName(r.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                    });
                    member.setRoleType(RoleTypeEnum.PROJECT_MANAGER.getValue());
                    add.add(member);
                }
                if (Optional.ofNullable(preSaleUserId).isPresent()) {
                    ProjectStakeholderMember member = new ProjectStakeholderMember();
                    member.setProjectId(p.getId());
                    member.setMemberId(preSaleUserId);
                    Roster roster = userIdMap.get(preSaleUserId);
                    Optional.ofNullable(roster).ifPresent(r -> {
                        member.setMemberName(roster.getAliasName());
                        member.setPosition(r.getJob());
                        member.setDeptName(projectStakeholderMemberService.buildDeptName(r.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                    });
                    member.setRoleType(RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue());
                    add.add(member);
                }
                if (Optional.ofNullable(salesmanUserId).isPresent()) {
                    ProjectStakeholderMember member = new ProjectStakeholderMember();
                    member.setProjectId(p.getId());
                    member.setMemberId(salesmanUserId);
                    Roster roster = userIdMap.get(salesmanUserId);
                    Optional.ofNullable(roster).ifPresent(r -> {
                        member.setMemberName(roster.getAliasName());
                        member.setPosition(r.getJob());
                        member.setDeptName(projectStakeholderMemberService.buildDeptName(r.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                    });
                    member.setRoleType(RoleTypeEnum.PROJECT_SALES_MANAGER.getValue());
                    add.add(member);
                }
            }
        });

        // 保存新增铁三角干系人、删除被移除的铁三角干系人
        if (CollUtil.isNotEmpty(add)) {
            add.forEach(BaseBuildEntityUtil::buildSave);
            projectStakeholderMemberMapper.batchSave(add);
        }
        if (CollUtil.isNotEmpty(del)) {
            List<Long> delIds = ironTriangle.stream().filter(i -> {
                Optional<ProjectStakeholderMember> any = del.stream().filter(d -> d.getProjectId().equals(i.getProjectId()) && d.getMemberId().equals(i.getMemberId()) && d.getRoleType().equals(i.getRoleType())).findAny();
                if (any.isPresent()) {
                    return true;
                }
                return false;
            }).map(ProjectStakeholderMember::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(delIds)) {
                projectStakeholderMemberMapper.deleteBatchIds(delIds);
            }
        }
    }

    @Override
    public void syncProjectMembers() {
        //对比项目信息表查询所有新增的项目经理、客户经理、售前经理、业务经理
        List<ProjectStakeholderMember> addList = projectStakeholderMemberMapper.getAddMembersByProject();
        if (CollUtil.isNotEmpty(addList)) {
            Map<Long, Roster> userIdMap = Optional.ofNullable(rosterMapper.findUserIdMap(null)).orElse(new HashMap<>());
            List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
            Map<Long, SysDeptOutVO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d, (a, b) -> a));
            addList.forEach(p -> {
                Roster roster = userIdMap.get(p.getMemberId());
                Optional.ofNullable(roster).ifPresent(r -> {
                    p.setMemberName(roster.getAliasName());
                    p.setPosition(r.getJob());
                    p.setDeptName(projectStakeholderMemberService.buildDeptName(r.getDeptId(), deptMap, Boolean.TRUE, CharSequenceUtil.EMPTY));
                });
            });

            addList.forEach(BaseBuildEntityUtil::buildSave);
            projectStakeholderMemberMapper.batchSave(addList);
        }

        //对比项目信息表查询所有删除的项目经理、客户经理、售前经理、业务经理
        List<Long> delList = projectStakeholderMemberMapper.getDelMembersByProject();
        if (CollUtil.isNotEmpty(delList)) {
            projectStakeholderMemberMapper.deleteBatchIds(delList);
        }
    }

    /**
     * 组装项目OA项目业务需求文档文件信息
     *
     * @param projectInfo   项目信息实体类
     * @param projectInfoVO 项目信息VO类
     */
    private void assembleProjectBusinessRequirementsOaFile(ProjectInfo projectInfo, ProjectInfoVO projectInfoVO) {
        List<Long> docIds = new ArrayList<>();
        if (StrUtil.isNotBlank(projectInfo.getProjectBusinessRequirementsOafileid())) {
            String[] split = projectInfo.getProjectBusinessRequirementsOafileid().split(",");
            Arrays.asList(split).stream().forEach(s -> docIds.add(Long.parseLong(s)));
        }
        if (CollUtil.isEmpty(docIds)) {
            return;
        }

        List<OaFileInfoVo> projectBusinessRequirementsOaFileName = new ArrayList<>();
        List<PmsDocImageFile> pmsDocImageFileList = pmsDocImageFileService.getByDocIds(docIds);
        pmsDocImageFileList.stream().forEach(f -> {
            OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
            oaFileInfoVo.setFileId(f.getDocId().toString());
            oaFileInfoVo.setFilename(f.getImageFileName());
            if (StrUtil.isNotBlank(projectInfo.getProjectBusinessRequirementsOafileid()) && projectInfo.getProjectBusinessRequirementsOafileid().contains(f.getDocId().toString())) {
                projectBusinessRequirementsOaFileName.add(oaFileInfoVo);
            }
        });
        projectInfoVO.setProjectBusinessRequirementsOafileName(projectBusinessRequirementsOaFileName);
    }

    /**
     * 获取结项项目且未进行满意度调查的项目列表
     *
     * @return {@link List }<{@link EvalSatisfactionSurveyProjectVo }>
     */
    @Override
    public List<EvalSatisfactionSurveyProjectVo> getCompletedProjectsWithoutSatisfactionSurvey() {
        return baseMapper.getCompletedProjectsWithoutSatisfactionSurvey();
    }

}
