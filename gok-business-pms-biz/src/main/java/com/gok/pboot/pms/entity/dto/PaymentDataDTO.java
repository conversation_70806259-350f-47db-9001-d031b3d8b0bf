package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目核心数据Dto
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentDataDTO implements Serializable {

    /**
     * 项目ID
     */
    @NotNull
    private Long id;

    /**
     * 偏差说明
     */
    private String deviationExplanation;

    /**
     * 成本偏差和毛利偏差说明
     */
    private String costGrossDeviationExplanation;

    /**
     * 待回款说明
     */
    private String pendingAmountExplanation;

    /**
     * 现金流说明
     */
    private String cashExplanation;

    /**
     * 坏账说明
     */
    private String badDebtExplanation;

    /**
     * 模块 0-质保 1-关闭
     * {@link com.gok.pboot.pms.enumeration.ProjectOperationModuleEnum}
     */
    @NotNull
    @Min(0)
    @Max(1)
    private Integer module;

    /**
     * 操作类型 0-否 1-确认 2-之后提醒 3-知悉
     * {@link com.gok.pboot.pms.enumeration.ProjectOperationTypeEnum}
     */
    @NotNull
    @Min(0)
    @Max(3)
    private Integer operationType;

    /**
     * 变更数据是否保存 true-是 false-否
     */
    @NotNull
    private boolean dataChangedSave;

    /**
     * 提醒日期
     */
    private LocalDate reminderDate;

    /**
     * 质保期预估工时
     */
    @NotNull
    private BigDecimal warrantyEstimatedHours;

    /**
     * 质保期预算直接人工成本
     */
    @NotNull
    private BigDecimal warrantyEstimatedDirectLaborCost;

    /**
     * 后续计划集合
     */
    private List<ProjectFollowupPlanDTO> projectFollowupPlanList;

}
