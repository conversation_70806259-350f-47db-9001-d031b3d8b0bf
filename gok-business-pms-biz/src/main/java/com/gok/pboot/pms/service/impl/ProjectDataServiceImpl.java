package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectData;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.PaymentDataDTO;
import com.gok.pboot.pms.entity.dto.ProjectOperationConfirmationDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.enums.EvalUserRoleEnum;
import com.gok.pboot.pms.eval.mapper.EvalUserRoleMapper;
import com.gok.pboot.pms.eval.service.IEvalProjectOverviewService;
import com.gok.pboot.pms.mapper.ProjectBusinessMilestonesMapper;
import com.gok.pboot.pms.mapper.ProjectDataMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;

/**
 * 项目核心数据服务
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectDataServiceImpl extends ServiceImpl<ProjectDataMapper, ProjectData> implements IProjectDataService {

    private final ProjectDataMapper mapper;

    private final DbApiUtil dbApiUtil;

    private final ProjectInfoMapper projectInfoMapper;
    private final EvalUserRoleMapper evalUserRoleMapper;
    private final ProjectBusinessMilestonesMapper projectBusinessMilestonesMapper;

    private final IProjectStatusService projectStatusService;
    private final ProjectDetailsService projectDetailsService;
    private final IProjectWorkHoursService projectWorkHoursService;
    private final IEvalProjectOverviewService evalProjectOverviewService;
    private final IProjectFollowupPlanService projectFollowupPlanService;
    private final IProjectOperationConfirmationService projectOperationConfirmationService;

    private final OaUtil oaUtil;

    @Override
    public ProjectDataVO findById(Long id) {
        // 获取项目核心数据
        ProjectData po = mapper.selectById(id);
        // 获取项目信息
        ProjectInfo projectInfo = projectInfoMapper.selectById(id);
        // 判断是否为内部项目
        boolean internalFlag = false;
        if (Optional.ofNullable(projectInfo).isPresent()) {
            internalFlag = projectInfo.getIsNotInternalProject() == 1 ? true : false;
        }
        ProjectDataVO result;
        // 项目核心数据为空返回
        if (po == null) {
            return new ProjectDataVO();
        }
        // 实体数据转换
        result = ProjectDataVO.of(po, internalFlag);
        if (internalFlag) {
            //预算成本
            result.setTotalBudgetCostIncludeTax(DecimalFormatUtil.setThousandthAndTwoDecimal(projectInfo.getEstimatedCost(), DecimalFormatUtil.ZERO));
            //实际总成本
            result.setActualTotalBudgetCost(DecimalFormatUtil.setThousandthAndTwoDecimal(BigDecimal.ZERO, DecimalFormatUtil.ZERO));
            //预算成本
            BigDecimal totalBudgetCostIncludeTax = projectInfo.getEstimatedCost() == null ? BigDecimal.ZERO : projectInfo.getEstimatedCost();
            //实际总成本
            BigDecimal actualTotalBudgetCost = new BigDecimal(StrUtil.isNotBlank(result.getActualTotalBudgetCost()) ? result.getActualTotalBudgetCost() : "0.00");
            //剩余可用预算
            result.setRemainingAvailableBudget(DecimalFormatUtil.setThousandthAndTwoDecimal(totalBudgetCostIncludeTax.subtract(actualTotalBudgetCost), DecimalFormatUtil.ZERO));
            //成本进度
            result.setCostProgress("0%");
            if (!totalBudgetCostIncludeTax.equals(BigDecimal.ZERO)
                    && !totalBudgetCostIncludeTax.equals(new BigDecimal("0.00"))
                    && !totalBudgetCostIncludeTax.equals(new BigDecimal("0.0"))) {
                result.setCostProgress(actualTotalBudgetCost
                        .divide(totalBudgetCostIncludeTax, 0, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal(100)) + "%");
            }


            // 内部项目预估总人天
            BigDecimal estimatedTotalManDays = new BigDecimal(result.getInternalEstimatedTotalManDays());
            result.setEstimatedTotalManDays(estimatedTotalManDays.setScale(2, RoundingMode.HALF_UP).toString());

            //已发生人天(取自本系统本项目审核通过的工时合计)
            Map<String, Object> filter = new HashMap<>();
            filter.put("projectId", id);
            ProjectWorkHoursVo avgWorkHoursVo = projectWorkHoursService.calculateAvgWorkHours(filter);
            BigDecimal manDays = BigDecimal.ZERO;
            if (Optional.ofNullable(avgWorkHoursVo).isPresent() && Optional.ofNullable(avgWorkHoursVo.getAvgSumHours()).isPresent()) {
                manDays = avgWorkHoursVo.getAvgSumHours();
            }
            result.setManDays(manDays.setScale(2, RoundingMode.HALF_UP).toString());

            //剩余可用人天
            result.setRemainingAvailableDays(estimatedTotalManDays.subtract(manDays).setScale(2, RoundingMode.HALF_UP).toString());

        } else {
            // 财务数据处理
            String projectIdStr = String.valueOf(id);
            FinancialDataVo financialData = dbApiUtil.financialData(projectIdStr, StrUtil.EMPTY, StrUtil.EMPTY);
            ProjectDataVO.update(financialData, result);

            // 外部项目预估总人天
            BigDecimal estimatedTotalManDays = new BigDecimal(result.getEstimatedTotalManDays());
            result.setEstimatedTotalManDays(estimatedTotalManDays.setScale(2, RoundingMode.HALF_UP).toString());

            // 合同数据处理
            Map<String, Object> contractFilter = new HashMap<>();
            contractFilter.put("projectId", projectIdStr);
            contractFilter.put("contractType", "1");
            ProjectContractVo projectContractVo = projectDetailsService.projectContract(new PageRequest(1, Integer.MAX_VALUE), contractFilter).getData();
            ProjectDataVO.update(projectContractVo, result);

            // 实际已发生人天
//            Map<String, Object> projectWorkHoursFilter = new HashMap<>();
//            projectWorkHoursFilter.put("projectId", projectIdStr);
//            projectWorkHoursFilter.put("dimension", 0);
//            ProjectWorkHoursVo projectWorkHoursVo = projectWorkHoursService.countWorkHours(new PageRequest(1, 1), projectWorkHoursFilter);
//            result.setManDays(null != projectWorkHoursVo.getAvgSumHours() ? projectWorkHoursVo.getAvgSumHours().toString() : "0.00");

            //实际已发生人天等于售前已发生人天+售后已发生人天
            result.setManDays(new BigDecimal(result.getBeforeSalesManDays()).add(new BigDecimal(result.getAfterSalesManDays())).toString());

        }

        return result;
    }

    @Override
    public ProjectDataConfirmedVO findConfirmedDataByProjectId(Long id) {
        ProjectData po = mapper.selectById(id);
        ProjectDataConfirmedVO result = null == po
                ? new ProjectDataConfirmedVO()
                : BeanUtil.copyProperties(po, ProjectDataConfirmedVO.class);

        // 查询项目后续计划
        List<ProjectFollowupPlanVO> projectFollowupPlans = projectFollowupPlanService.findByProjectId(id);
        result.setProjectFollowupPlanList(projectFollowupPlans);

        // 获取当前项目角色集合
        List<Integer> operationRolList = projectOperationConfirmationService.getOperationRole(id, SecurityUtils.getUser().getId());
        result.setCurrentUserRole(operationRolList);

        // 获取质保确认信息
        ProjectDataConfirmedVO milestonesConfirmData = projectBusinessMilestonesMapper.selConfirmedData(id);
        if (null != milestonesConfirmData) {
            result.setContractName(milestonesConfirmData.getContractName());
            result.setAcceptanceDate(milestonesConfirmData.getAcceptanceDate());
            LocalDate warrantyPeriodStartDate = milestonesConfirmData.getWarrantyPeriodStartDate();
            result.setWarrantyPeriodStartDate(warrantyPeriodStartDate);
            String warrantyMonths = milestonesConfirmData.getWarrantyMonths();
            result.setWarrantyMonths(warrantyMonths);
            if (null != warrantyPeriodStartDate && StrUtil.isNotBlank(warrantyMonths)) {
                LocalDate warrantyPeriodEndDate =
                        warrantyPeriodStartDate.plusMonths(convertWarrantyMonths(warrantyMonths));
                result.setWarrantyPeriodEndDate(warrantyPeriodEndDate);
            }
        }

        return result;
    }

    /**
     * 获取质保确认的时间间隔
     *
     * @param warrantyMonths
     * @return
     */
    public static long convertWarrantyMonths(String warrantyMonths) {
        if (warrantyMonths == null || warrantyMonths.trim().isEmpty()) {
            // 处理空值情况，返回默认值或抛出异常，根据业务需求决定
            return 0L; // 默认返回 0
        }

        try {
            BigDecimal value = new BigDecimal(warrantyMonths.trim());
            return value.longValueExact(); // 精确转换，避免小数部分被截断
        } catch (NumberFormatException e) {
            // 处理非法数字格式的情况
            return 0L; // 或者抛出自定义异常、记录日志等
        } catch (ArithmeticException e) {
            // 如果存在非零的小数部分（如 "12.5"），longValueExact() 会抛出 ArithmeticException
            return 0L; // 或者按需处理为四舍五入等逻辑
        }
    }

    @Override
    @Transactional
    public Long confirm(PaymentDataDTO request) {
        Long projectId = request.getId();
        Integer module = request.getModule();
        Integer operationType = request.getOperationType();

        ProjectData projectData = mapper.selectById(projectId);
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (null == projectInfo) {
            log.info("项目ID:{}对象不存在，操作结束", projectId);
            return projectId;
        }

        if (ProjectOperationModuleEnum.WARRANTY.getValue().equals(module)) {
            if (!DeliverTypeEnum.PROJECT_OUTSOURCING.getValue().equals(projectInfo.getDeliverType())
                    || !projectStatusService.isEnterProjectWarrantyPeriod(projectId)) {
                return projectId;
            }
        } else if (ProjectOperationModuleEnum.CLOSE.getValue().equals(module)) {
            if (!projectStatusService.isCloseProject(projectId)) {
                return projectId;
            }
        }

        // 更新项目核心数据
        updateConfirmedData(request, projectData, projectInfo);

        // 保存确认记录
        ProjectOperationConfirmationDTO confirmOperationDto = ProjectOperationConfirmationDTO.builder()
                .projectId(projectId)
                .module(module)
                .operationType(operationType)
                .reminderDate(null != request.getReminderDate() && ProjectOperationTypeEnum.DELAY.getValue().equals(operationType)
                        ? request.getReminderDate().plusDays(1)
                        : null)
                .build();
        projectOperationConfirmationService.saveOrUpdate(confirmOperationDto);

        // 项目状态变更
        boolean confirmedComplete = projectOperationConfirmationService.judgeConfirmedComplete(projectId, module);
        if (confirmedComplete) {
            List<ProjectOperationRoleEnum> roleEnumList = null;
            JSONObject mainTable = new JSONObject();
            if (ProjectOperationModuleEnum.WARRANTY.getValue().equals(module)) {
                mainTable.put("xmzt", ProjectStatusEnum.ZB.getStrValue());
                oaUtil.doAction(String.valueOf(projectId), mainTable);
                projectInfo.setProjectStatus(String.valueOf(ProjectStatusEnum.ZB.getValue()));
                roleEnumList = Arrays.asList(ProjectOperationRoleEnum.PMO);
            } else if (ProjectOperationModuleEnum.CLOSE.getValue().equals(module)) {
                mainTable.put("xmzt", ProjectStatusEnum.JX.getStrValue());
                mainTable.put("xmjd", "5");
                oaUtil.doAction(String.valueOf(projectId), mainTable);
                projectInfo.setProjectStatus(String.valueOf(ProjectStatusEnum.JX.getValue()));
                roleEnumList = Arrays.asList(ProjectOperationRoleEnum.MANAGER_LEADER, ProjectOperationRoleEnum.SALES_LEADER, ProjectOperationRoleEnum.PMO);
            }
            projectInfoMapper.updateById(projectInfo);

            EvalUserRole query = EvalUserRole.builder()
                    .role(EvalUserRoleEnum.PMO.getValue())
                    .build();
            EvalUserRole evalUserRole = CollUtil.getFirst(evalUserRoleMapper.selectList(new QueryWrapper<>(query)));
            if (null == evalUserRole) {
                return projectId;
            }

            // 创建项目整体评价
            evalProjectOverviewService.autoSaveProjectOverviewEval(Arrays.asList(projectId));

            // 项目状态变更知悉消息
            projectOperationConfirmationService.batchSaveKnowOperation(projectId, module, roleEnumList);
        }

        return projectId;
    }

    /**
     * 保存质保确认/关闭确认数据
     *
     * @param request
     * @param projectData
     */
    @Transactional
    public void updateConfirmedData(PaymentDataDTO request, ProjectData projectData, ProjectInfo projectInfo) {
        // 数据校验
        if (null == request || null == projectInfo) {
            return;
        }

        // 业务校验
        Long currentUserId = SecurityUtils.getUser().getId();
        if (!request.isDataChangedSave() || !currentUserId.equals(projectInfo.getManagerUserId())) {
            return;
        }

        if (currentUserId.equals(projectInfo.getManagerUserId())
                && ProjectOperationTypeEnum.CONFIRM.getValue().equals(request.getOperationType())
                && !StrUtil.isAllNotBlank(request.getDeviationExplanation(), request.getCostGrossDeviationExplanation(), request.getPendingAmountExplanation(), request.getCashExplanation())) {
            throw new BusinessException("参数校验不通过：不能为空字符串");
        }
        projectData = Optional.ofNullable(projectData).orElse(new ProjectData());
        projectData.setDeviationExplanation(request.getDeviationExplanation());
        projectData.setCostGrossDeviationExplanation(request.getCostGrossDeviationExplanation());
        projectData.setPendingAmountExplanation(request.getPendingAmountExplanation());
        projectData.setCashExplanation(request.getCashExplanation());
        projectData.setBadDebtExplanation(request.getBadDebtExplanation());
        if (null != projectData.getId()) {
            baseMapper.updateById(projectData);
        } else {
            projectData.setId(projectInfo.getId());
            baseMapper.insert(projectData);
        }

        // 保存后续计划
        projectFollowupPlanService.batchSave(request.getProjectFollowupPlanList());

    }

}
