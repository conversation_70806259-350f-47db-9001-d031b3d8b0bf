package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.ProjectSchedulePlan;
import com.gok.pboot.pms.cost.entity.dto.ProjectSchedulePlanDTO;
import com.gok.pboot.pms.cost.entity.vo.ProjectSchedulePlanVO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/07/31
 **/
public interface IProjectSchedulePlanService extends IService<ProjectSchedulePlan> {

    /**
     * 查询项目进度计划列表
     *
     * @param projectId 项目ID
     * @param versionId 版本ID
     * @return
     */
    ProjectSchedulePlanVO findList(Long projectId, Long versionId);

    /**
     * 批量保存项目进度计划
     *
     * @param request 保存请求
     * @return
     */
    List<Long> batchSaveOrUpdate(ProjectSchedulePlanDTO request);

}
