/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.pboot.pms;

import com.gok.pboot.common.feign.annotation.EnablePigxFeignClients;
import com.gok.pboot.common.security.annotation.EnablePigxResourceServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2022-08-16
 * <p>
 * PMS-业务服务
 */
@EnablePigxFeignClients(basePackages = {"com.gok"})
@EnablePigxResourceServer
@EnableDiscoveryClient
@SpringBootApplication
@EnableScheduling
@EnableAspectJAutoProxy
@MapperScan({"com.gok.module.file.mapper", "com.gok.pboot.pms.mapper", "com.gok.pboot.pms.*.mapper"})
@ComponentScan(basePackages = {"com.gok.module.excel.api", "com.gok.pboot.pms"})
public class PmsServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(PmsServiceApplication.class, args);

    }
}

