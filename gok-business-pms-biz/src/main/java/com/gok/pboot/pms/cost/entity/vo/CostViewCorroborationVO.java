package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.Util.DateUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 查看佐证 vo
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostViewCorroborationVO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 工单类型（0=售前支撑，1=售后交付）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    /**
     * 工单类型名称
     */
    private String taskTypeName;

    /**
     * 工单描述
     */
    private String taskDesc;

    /**
     * 关联成本科目id
     */
    private Long accountId;

    /**
     * 关联成本科目名称
     */
    private String accountName;

    /**
     * 预算成本
     */
    private BigDecimal budgetCost;

    /**
     * 拆解类型（0=标准工单，1=总成工单）
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum
     */
    private Integer disassemblyType;

    /**
     * 拆解类型名称
     */
    private String disassemblyTypeName;

    /**
     * 工单负责人id
     */
    private Long managerId;

    /**
     * 工单负责人
     */
    private String managerName;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = DateUtil.SIMPLE_DATE_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = DateUtil.SIMPLE_DATE_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDate endDate;

    /**
     * 交付说明
     */
    private String deliverDesc;

    /**
     * 佐证附件集合
     */
    private List<CostCompletionFilesVO> completeFileList;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = DateUtil.TIME_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDateTime submitCompletionTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = DateUtil.TIME_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDateTime auditTime;

    /**
     * 工作日加班
     */
    private BigDecimal workOverTimeHours;

    /**
     * 休息日加班
     */
    private BigDecimal restOverTimeHours;

    /**
     * 节假日加班
     */
    private BigDecimal holidayOverTimeHours;

    /**
     * 工单类别（来自工单类别管理）
     */
    private Integer taskCategory;

    /**
     * 工单类别文本
     */
    private String taskCategoryTxt;

    /**
     * 预计工时
     */
    private BigDecimal estimatedHours;

    /**
     * 工单状态
     */
    private Integer taskStatus;

    private String taskStatusTxt;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = DateUtil.DATETIME_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDateTime completionTime;

    /**
     * 实际人工成本
     */
    private BigDecimal actualLaborCost;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 等待审核工时
     */
    private BigDecimal waitReviewHours;

    /**
     * 上级工单负责人ID
     */
    private Long higherManagerId;

    /**
     * 上级工单负责人姓名
     */
    private String higherManagerName;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 日报详情
     */
    List<DailyPaperEntryDetailsVO> dailyPaperList;


    /**
     * 构建查看佐证 vo
     *
     * @param task                任务
     * @param completeFileList    完整文件列表
     * @param costTaskCategoryMap 工单类别
     * @return {@link CostViewCorroborationVO }
     */
    public static CostViewCorroborationVO buildCorroborationVo(CostDeliverTask task, List<CostCompletionFilesVO> completeFileList, Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap) {
        return new CostViewCorroborationVO()
                .setProjectId(task.getProjectId())
                .setProjectName(task.getProjectName())
                .setTaskName(task.getTaskName())
                .setTaskType(task.getTaskType())
                .setTaskTypeName(EnumUtils.getNameByValue(ProjectTaskKindEnum.class, task.getTaskType()))
                .setTaskDesc(task.getTaskDesc())
                .setAccountId(task.getAccountId())
                .setAccountName(task.getAccountName())
                .setBudgetCost(task.getBudgetCost() != null && StrUtil.isNotEmpty(task.getBudgetCost()) ? new BigDecimal(task.getBudgetCost()) : null)
                .setDisassemblyType(task.getDisassemblyType())
                .setDisassemblyTypeName(EnumUtils.getNameByValue(CostTaskDisassemblyTypeEnum.class, task.getDisassemblyType()))
                .setManagerId(task.getManagerId())
                .setManagerName(task.getManagerName())
                .setStartDate(task.getStartDate())
                .setEndDate(task.getEndDate())
                .setDeliverDesc(task.getDeliverDesc())
                .setCompleteFileList(completeFileList)
                .setSubmitCompletionTime(task.getSubmitCompletionTime())
                .setAuditTime(task.getAuditTime())
                .setWorkOverTimeHours(task.getWorkOvertimeHours())
                .setRestOverTimeHours(task.getRestOvertimeHours())
                .setTaskCategory(task.getTaskCategory())
                .setTaskCategoryTxt(costTaskCategoryMap.getOrDefault(task.getTaskCategory(), new CostTaskCategoryManagement()).getTaskCategoryName())
                .setHolidayOverTimeHours(task.getHolidayOvertimeHours())
                .setEstimatedHours(task.getEstimatedHours())
                .setTaskStatus(task.getTaskStatus())
                .setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, task.getTaskStatus()))
                .setCompletionTime(task.getCompletionTime())
                .setActualLaborCost(task.getActualLaborCost() != null && StrUtil.isNotEmpty(task.getActualLaborCost()) ? new BigDecimal(task.getActualLaborCost()) : null)
                .setNormalHours(task.getNormalHours())
                .setCreator(task.getCreator())
                .setCreatorId(task.getCreatorId());
    }
}
