package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.BcpMessageContentModel;
import com.gok.bcp.message.entity.model.WeComBatchModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.cost.entity.domain.ProjectSchedulePlanActivity;
import com.gok.pboot.pms.cost.entity.dto.ProjectSchedulePlanActivityDTO;
import com.gok.pboot.pms.cost.enums.ActivityStatusEnum;
import com.gok.pboot.pms.cost.mapper.ProjectSchedulePlanActivityMapper;
import com.gok.pboot.pms.cost.service.IProjectSchedulePlanActivityService;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.google.common.base.Charsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/07/31
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectSchedulePlanActivityServiceImpl
        extends ServiceImpl<ProjectSchedulePlanActivityMapper, ProjectSchedulePlanActivity> implements IProjectSchedulePlanActivityService {

    private final RosterMapper rosterMapper;
    private final IProjectInfoService projectInfoService;
    private final RemoteSendMsgService remoteSendMsgService;

    @Value("${pushMessage.clientId}")
    private Long clientId;
    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;
    @Value("${pushMessage.activityFinishRedirect}")
    private String activityFinishRedirect;

    @Override
    @Transactional
    public List<Long> batchSaveOrUpdate(Long projectId,
                                        List<ProjectSchedulePlanActivityDTO> activities,
                                        List<ProjectSchedulePlanActivity> oldSchedulePlanActivityList,
                                        boolean isSaveOperation) {
        if (null == projectId || CollUtil.isEmpty(activities)) {
            return ListUtil.empty();
        }

        List<ProjectSchedulePlanActivity> entities;
        if (isSaveOperation) {
            entities = activities.stream().map(dto -> {
                ProjectSchedulePlanActivity entity = BeanUtil.copyProperties(dto, ProjectSchedulePlanActivity.class);
                entity.setProjectId(projectId);
                entity.setSort(dto.getActivitySort());
                BaseBuildEntityUtil.buildInsert(entity);
                return entity;
            }).collect(Collectors.toList());

            this.saveBatch(entities);
        } else {
            List<ProjectSchedulePlanActivity> finishedList = new ArrayList<>();
            List<ProjectSchedulePlanActivity> updateList = new ArrayList<>();
            for (ProjectSchedulePlanActivity old : oldSchedulePlanActivityList) {
                ProjectSchedulePlanActivityDTO dto =
                        activities.stream().filter(e -> e.getId().equals(old.getId())).findFirst().get();

                if (!ActivityStatusEnum.COMPLETED.getValue().equals(old.getActivityStatus())
                        && ActivityStatusEnum.COMPLETED.getValue().equals(dto.getActivityStatus())) {
                    finishedList.add(old);
                }

                BeanUtil.copyProperties(dto, old, "planStartDate", "planEndDate", "versionId", "planId", "stage", "activity", "deliverables", "completionCriteria");
                BaseBuildEntityUtil.buildUpdate(old);
                updateList.add(old);
            }
            this.updateBatchById(updateList);
            entities = updateList;

            // 已完成的批量发送消息推送
            batchSendMsg(projectId, finishedList);
        }
        return entities.stream().map(ProjectSchedulePlanActivity::getId).collect(Collectors.toList());
    }


    /**
     * 推送活动完成消息
     *
     * @param finishedList 已完成活动列表
     * @return 消息
     */
    private void batchSendMsg(Long projectId, List<ProjectSchedulePlanActivity> finishedList) {
        if (CollUtil.isEmpty(finishedList)) {
            log.info("已完成活动列表为空，操作结束");
            return;
        }

        ProjectInfo projectInfo = projectInfoService.getById(projectId);
        Long managerUserId = projectInfo.getManagerUserId();
        if (null == projectInfo || null == managerUserId) {
            log.info("项目经理不存在，操作结束");
            return;
        }

        Roster roster = rosterMapper.selectById(managerUserId);
        Long managerUserLeaderId = null != roster ? roster.getLeaderId() : null;
        Roster managerLeader = rosterMapper.selectById(managerUserLeaderId);
        if (null == managerLeader) {
            log.info("项目经理职能上级不存在，操作结束");
            return;
        }

        String title = "【项目进度计划活动完成】";
        BcpMessageBatchDTO bcpMessageDto = new BcpMessageBatchDTO();
        List<WeComModel> weComModelList = new ArrayList<>(finishedList.size());
        Map<String, BcpMessageContentModel> bcMessageContentMap = new HashMap<>();
        List<BcpMessageTargetBatchDTO> bcpMessageTargetList = new ArrayList<>();
        for (ProjectSchedulePlanActivity activity : finishedList) {
            // 封装企微消息对象
            WeComModel weComModel = new WeComModel();
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle(title);
            weComModel.setSenderId(clientId);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());

            String redirectUrl = StrUtil.format(activityFinishRedirect, projectId);
            weComModel.setRedirectUrl(redirectPrefix + Base64.encode(redirectUrl, Charsets.UTF_8));

            BcpMessageTargetDTO target = BcpMessageTargetDTO.builder()
                    .targetId(String.valueOf(managerLeader.getId()))
                    .targetName(managerLeader.getAliasName())
                    .build();
            weComModel.setTargetList(Arrays.asList(target));
            String content = StrUtil.format("【项目进度计划】您好，{}项目-{}阶段-{}活动已完成，请知悉。", projectInfo.getItemName(), activity.getStage(), activity.getActivity());
            weComModel.setContent(content + "\n<a href=\"" + weComModel.getRedirectUrl() + "\">" + "查看详情</a>");
            weComModelList.add(weComModel);

            // 封装门户消息对象
            String contentId = UUID.randomUUID().toString();
            BcpMessageContentModel bcpMessageContentModel = new BcpMessageContentModel();
            bcpMessageContentModel.setTitle(weComModel.getTitle());
            bcpMessageContentModel.setContent(content);
            bcpMessageContentModel.setRedirectUrl(weComModel.getRedirectUrl());
            bcpMessageContentModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            bcMessageContentMap.put(contentId, bcpMessageContentModel);

            BcpMessageTargetBatchDTO bcpMessageTargetBatchDTO = new BcpMessageTargetBatchDTO();
            bcpMessageTargetBatchDTO.setContentId(contentId);
            bcpMessageTargetBatchDTO.setTargetId(target.getTargetId());
            bcpMessageTargetBatchDTO.setTargetName(target.getTargetName());
            bcpMessageTargetList.add(bcpMessageTargetBatchDTO);
        }
        // 批量发送企微消息
        WeComBatchModel weComBatchModel = new WeComBatchModel();
        weComBatchModel.setData(weComModelList);
        remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, weComBatchModel);

        // 发送门户消息
        bcpMessageDto.setSource(SourceEnum.PROJECT.getValue());
        bcpMessageDto.setChannel(ChannelEnum.MAIL.getValue());
        bcpMessageDto.setSenderId(clientId);
        bcpMessageDto.setSender(SourceEnum.PROJECT.getName());
        bcpMessageDto.setTargetType(TargetTypeEnum.USERS.getValue());
        bcpMessageDto.setSendTime(DateUtil.formatTime(new Date()));
        bcpMessageDto.setContentMap(bcMessageContentMap);
        bcpMessageDto.setTargetList(bcpMessageTargetList);
        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, bcpMessageDto);
    }

}
