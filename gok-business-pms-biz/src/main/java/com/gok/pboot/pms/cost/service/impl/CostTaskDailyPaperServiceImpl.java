package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.common.core.constant.CommonConstants;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.annotation.ProjectStatusCheck;
import com.gok.pboot.pms.common.base.*;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.common.validate.validator.ManHourValidator;
import com.gok.pboot.pms.cost.entity.domain.*;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.cost.enums.CostTypeEnum;
import com.gok.pboot.pms.cost.enums.TaskAbnormalTypeEnum;
import com.gok.pboot.pms.cost.mapper.*;
import com.gok.pboot.pms.cost.service.*;
import com.gok.pboot.pms.entity.Filing;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.OvertimeLeaveData;
import com.gok.pboot.pms.entity.bo.TaskBo;
import com.gok.pboot.pms.entity.bo.TaskReviewerInfoBO;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.entity.dto.ChangeApprovalStatusDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperEntryFilingDTO;
import com.gok.pboot.pms.entity.dto.UserPmsDTO;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO;
import com.gok.pboot.pms.entity.vo.SysUserPmsCqVO;
import com.gok.pboot.pms.entity.vo.TomorrowPlanPaperVo;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.handler.DailyPaperAnalyzer;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.HolidayMapper;
import com.gok.pboot.pms.mapper.OvertimeLeaveDataMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.service.BcpMessageService;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import com.gok.pboot.pms.service.RosterService;
import com.gok.pboot.pms.service.fegin.CenterUserService;
import com.google.common.collect.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gok.pboot.pms.enumeration.ApprovalStatusEnum.*;
import static com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum.LAG;
import static com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum.NORMAL;

/**
 * 工单日报服务实现类
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Slf4j
@Service
@Deprecated
@RequiredArgsConstructor
public class CostTaskDailyPaperServiceImpl implements ICostTaskDailyPaperService {

    private static final String PARAM_UPDATE = "update";

    private static final String PARAM_ADD = "add";
    private static final String NO_DAILY_PAPER_TO_BE_REVIEWED = "尚无要审核的日报";
    private final static int TASK_MAX_LEVEL = 3;

    @Value("${oa.xsfyOaId:62}")
    private Long xsfyOaId;

    @Value("${oa.xmglfysqrgOaId:8102}")
    private Long xmglfysqrgOaId;

    private final RosterMapper rosterMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final CostTaskDailyPaperEntryMapper dailyPaperEntryMapper;
    private final CostTomorrowPlanPaperEntryMapper costTomorrowPlanPaperEntryMapper;
    private final OvertimeLeaveDataMapper overtimeLeaveDataMapper;
    private final CostTaskFilingMapper filingMapper;
    private final CostTaskDailyPaperMapper mapper;
    private final HolidayMapper holidayMapper;
    private final ICostDeliverTaskService costDeliverTaskService;
    private final ICostConfigLevelPriceService costConfigLevelPriceService;
    private final CostDeliverTaskMapper costDeliverTaskMapper;

    private final BcpLoggerUtils bcpLoggerUtils;
    private final ICompensatoryLeaveDataService compensatoryLeaveDataService;

    private final CenterUserService centerUserService;
    private final PmsRetriever pmsRetriever;
    private final DailyPaperAnalyzer dailyPaperAnalyzer;


    private final BcpMessageService bcpMessageService;

    private final ICostManageVersionService costManageVersionService;
    private final CostManageEstimationResultsMapper costManageEstimationResultsMapper;

    private final RosterService rosterService;
    private final ICostTaskCategoryManagementService costTaskCategoryManagementService;

    private final ICostSalaryDetailsService costSalaryDetailsService;

    @Override
    public Page<CostTaskDailyPaper> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        return mapper.findList(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
    }

    @Override
    public CostTaskDailyPaper getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public CostTaskDailyPaperDetailsVO getDetailsById(Long id) {
        CostTaskDailyPaper dailyPaper = mapper.selectById(id);
        if (dailyPaper == null) {
            throw new ServiceException("没有找到指定日报");
        }
        String userRealName = rosterMapper.findAliasNameById(dailyPaper.getUserId());
        List<CostTaskDailyPaperEntry> entries = dailyPaperEntryMapper.findByDailyPaperId(id);
        List<CostTomorrowPlanPaperEntry> tomorrowPlanPaperEntries = costTomorrowPlanPaperEntryMapper.findByDailyPaperId(id);
        // 页面展示中可能会要求为每一个任务展示此任务所属项目的销售、项目经理和审核员，因此需要额外将Project查出来，去取这些字段
        Map<Long, ProjectInDailyPaperEntry> projects = this.findDailyAndTomorrowProjects(entries, tomorrowPlanPaperEntries);
        // 查询请假信息，用于获取日报异常情况
        BigDecimal leaveHour = findLeaveDataByDateAndUserId(dailyPaper.getSubmissionDate(), dailyPaper.getUserId());
        BigDecimal compensatoryLeave = findCompensatoryLeaveDataByDateAndUserId(dailyPaper.getSubmissionDate(), dailyPaper.getUserId());
        //查询假期类型
        Holiday holiday = holidayMapper.selByDate(dailyPaper.getSubmissionDate());
        BigDecimal add = leaveHour.add(compensatoryLeave);
        // 组装结果对象
        CostTaskDailyPaperDetailsVO dailyPaperDetailsVO = new CostTaskDailyPaperDetailsVO(
                dailyPaper,
                entries,
                tomorrowPlanPaperEntries,
                projects,
                userRealName,
                dailyPaperAnalyzer.getDailyPaperAbnormalInfo(dailyPaper, add)
                        .stream()
                        .map(Pair::getSecond)
                        .collect(Collectors.joining(";")),
                leaveHour,
                compensatoryLeave,
                holiday
        );
        // 组装结果对象：设置审核人

        // 判断日报、明日日报任务是否已结束、已删除或被移除参与人
        Stream<Long> entryStream = entries.stream().map(CostTaskDailyPaperEntry::getTaskId);
        Stream<Long> tomorrowEntryStream = tomorrowPlanPaperEntries.stream().map(CostTomorrowPlanPaperEntry::getTaskId);
        List<Long> entryTaskIds = Stream.concat(entryStream, tomorrowEntryStream).distinct().collect(Collectors.toList());

        Table<Long, Long, TaskReviewerInfoBO> reviewerInfoTable = pmsRetriever.getReviewerInfo(
                entries.stream().map(e -> Pair.of(e.getUserId(), e.getTaskId())).collect(Collectors.toList())
        );
        Set<Long> userIds = Sets.newHashSet();
        Map<Long, String> userIdAndAliasNameMap;

        // 设置日报、明日日报任务状态
        Set<Long> taskIds;
        if (CollUtil.isNotEmpty(entryTaskIds)) {
            Long userId = SecurityUtils.getUser().getId();
            taskIds = costDeliverTaskService.lambdaQuery().in(CostDeliverTask::getId, entryTaskIds)
                    .eq(CostDeliverTask::getManagerId, userId)
//                    .eq(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.ZC.getValue())
                    .list()
                    .stream().map(CostDeliverTask::getId).collect(Collectors.toCollection(LinkedHashSet::new));
        } else {
            taskIds = new LinkedHashSet<>();
        }
        dailyPaperDetailsVO.getEntries()
                .forEach(e -> e.setTaskFinishFlag(!taskIds.contains(e.getTaskId())));
        dailyPaperDetailsVO.getTomorrowPlanPaperEntries()
                .forEach(e -> e.setTaskFinishFlag(!taskIds.contains(e.getTaskId())));

        reviewerInfoTable.values().forEach(r -> {
            Set<TaskReviewerTypeEnum> types = r.getTypes();

            if (types.contains(TaskReviewerTypeEnum.TASK_LEADER)) {
                userIds.addAll(r.getTaskLeaderUserIds());
            }
            if (types.contains(TaskReviewerTypeEnum.IRON_TRIANGLE)) {
                userIds.add(r.getProjectManagerUserId());
                userIds.add(r.getProjectPreSalesmanUserId());
                userIds.add(r.getProjectSalesmanUserId());
            }
            if (types.contains(TaskReviewerTypeEnum.DIRECT_LEADER)) {
                userIds.add(r.getDirectLeaderUserId());
            }
        });
        if (userIds.isEmpty()) {
            userIdAndAliasNameMap = ImmutableMap.of();
        } else {
            userIdAndAliasNameMap = rosterMapper.selectBatchIds(userIds)
                    .stream()
                    .collect(Collectors.toMap(Roster::getId, Roster::getAliasName));
        }

        // 获取工单类别Map
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        
        // 设置工时类型txt、审核人
        dailyPaperDetailsVO.getEntries().forEach(entry -> {
            entry.setWorkTypeTxt(costTaskCategoryMap.getOrDefault(entry.getWorkType(), new CostTaskCategoryManagement()).getTaskCategoryName());
            setAuditorsInDailyPaperEntry(entry, reviewerInfoTable, userIdAndAliasNameMap);
        });
        dailyPaperDetailsVO.getTomorrowPlanPaperEntries().forEach(entry -> {
            entry.setWorkTypeTxt(costTaskCategoryMap.getOrDefault(entry.getWorkType(), new CostTaskCategoryManagement()).getTaskCategoryName());
        });

        // 设置是否归档状态
        LocalDate submissionDate = dailyPaper.getSubmissionDate();
        dailyPaperDetailsVO.getDailyPaper()
                .setIfFiled(Optional.ofNullable(submissionDate).isPresent() && YesOrNoEnum.YES.getValue().equals(filingMapper.isFiledByDate(submissionDate)));
        return dailyPaperDetailsVO;
    }

    /**
     * 设置日报条目的主审人字段
     *
     * @param entry                 日报条目
     * @param reviewerInfoBoTable   主审人信息
     * @param userIdAndAliasNameMap 用户id和姓名的映射
     */
    private void setAuditorsInDailyPaperEntry(
            CostTaskDailyPaperEntryVO entry,
            Table<Long, Long, TaskReviewerInfoBO> reviewerInfoBoTable,
            Map<Long, String> userIdAndAliasNameMap
    ) {
        TaskReviewerInfoBO reviewerInfoBO = reviewerInfoBoTable.get(entry.getUserId(), entry.getTaskId());
        List<String> auditorNames = new ArrayList<>();
        Set<TaskReviewerTypeEnum> types;
        String name;

        if (reviewerInfoBO == null) {
            return;
        }
        types = reviewerInfoBO.getTypes();
        if (types.contains(TaskReviewerTypeEnum.TASK_LEADER)) {
            reviewerInfoBO.getTaskLeaderUserIds().forEach(uId -> {
                String aName = userIdAndAliasNameMap.get(uId);

                if (StringUtils.isNotBlank(aName)) {
                    auditorNames.add(aName);
                }
            });
        }
        if (types.contains(TaskReviewerTypeEnum.IRON_TRIANGLE)) {
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getProjectSalesmanUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getProjectManagerUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getProjectPreSalesmanUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
        }
        if (types.contains(TaskReviewerTypeEnum.DIRECT_LEADER)) {
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getDirectLeaderUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
        }
        if (!auditorNames.isEmpty()) {
            entry.setAuditorNames(auditorNames);
        }
    }

    @Override
    public CostTaskDailyPaperDetailsVO getDetailsByDateForCurrentUser(LocalDate date) {
        Long userId = SecurityUtils.getUser().getId();
        CostTaskDailyPaper dailyPaper = mapper.findOneBeforeBySubmissionDateAndUserId(date, userId);
        CostTaskDailyPaperDetailsVO result;

        if (dailyPaper == null) {
            throw new ServiceException("没有找到目标日报");
        }
        result = buildDailyPaperDetailsVO(dailyPaper);
        // 将ID全部置空，防止前端在新增日报接口误传
        result.getDailyPaper().setId(null);
        result.getEntries().forEach(e -> {
            e.setId(null);
            e.setApprovalStatus(WTB.getValue());
            e.setApprovalStatusName(WTB.getName());
        });

        return result;
    }

    @Override
    public CostTaskDailyPaperDetailsVO getDetailsByDateForCurrentUserAccurate(LocalDate date) {
        Long userId = SecurityUtils.getUser().getId();
        Long paperId = mapper.findIdBySubmissionDateAndUserId(date, userId);
        BigDecimal leaveHour;
        CostTaskDailyPaperVO dailyPaper;
        CostTaskDailyPaperDetailsVO result;

        if (paperId == null) {
            // 查询请假信息
            leaveHour = findLeaveDataByDateAndUserId(date, userId);
            dailyPaper = new CostTaskDailyPaperVO();
            dailyPaper.setLeaveHourData(leaveHour);
            // 查询调休数据
            BigDecimal compensatoryLeave = findCompensatoryLeaveDataByDateAndUserId(date, userId);
            dailyPaper.setCompensatoryHourCount(compensatoryLeave);
            result = new CostTaskDailyPaperDetailsVO();
            result.setDailyPaper(dailyPaper);

            return result;
        }

        return getDetailsById(paperId);
    }

    /**
     * 根据日期和用户ID获取请休假数据
     *
     * @param date   日期
     * @param userId 用户ID
     * @return 请休假数据
     */
    private BigDecimal findLeaveDataByDateAndUserId(LocalDate date, Long userId) {
        List<OvertimeLeaveData> leaveDataList = overtimeLeaveDataMapper.getLeaveDataListByUserId(date, userId);
        BigDecimal leaveHour = BigDecimal.ZERO;

        for (OvertimeLeaveData l : leaveDataList) {
            if (EnumUtils.valueEquals(l.getType(), LeaveStatusEnum.XJ)) {
                leaveHour = leaveHour.subtract(l.getHourData());
            } else {
                leaveHour = leaveHour.add(l.getHourData());
            }
        }

        return BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHour) < 0 ? BigDecimalUtils.SEVEN_DECIMAL : leaveHour;
    }

    /**
     * 根据日期和用户ID获取项目调休数据
     *
     * @param date   日期
     * @param userId 用户ID
     * @return 请休假数据
     */
    private BigDecimal findCompensatoryLeaveDataByDateAndUserId(LocalDate date, Long userId) {
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        List<LocalDate> holidayDateList = holidayList.stream().map(Holiday::getDayDate).collect(Collectors.toList());

        //查询调休信息
        List<CompensatoryLeaveDataVO> compensatoryLeaveList =
                compensatoryLeaveDataService.findByDateTimeRangeAndUserId(date, date.plusDays(1), userId)
                        .stream().filter(e -> !holidayDateList.contains(e.getBelongDate())).collect(Collectors.toList());
        Map<LocalDate, List<CompensatoryLeaveDataVO>> compensatoryLeaveMap = compensatoryLeaveList.stream().collect(Collectors.groupingBy(CompensatoryLeaveDataVO::getBelongDate));

        BigDecimal compensatoryLeaveHour = BigDecimal.ZERO;
        List<CompensatoryLeaveDataVO> voList = compensatoryLeaveMap.getOrDefault(date, null);
        if (com.gok.pboot.pms.Util.CollectionUtils.isNotEmpty(voList)) {
            compensatoryLeaveHour = voList.stream()
                    .map(CompensatoryLeaveDataVO::getHourData)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
        }
        return BigDecimalUtils.SEVEN_DECIMAL.compareTo(compensatoryLeaveHour) < 0 ? BigDecimalUtils.SEVEN_DECIMAL : compensatoryLeaveHour;
    }

    /**
     * 查询日报条目和明日计划条目的项目
     */
    private Map<Long, ProjectInDailyPaperEntry> findDailyAndTomorrowProjects(List<CostTaskDailyPaperEntry> entries, List<CostTomorrowPlanPaperEntry> tomorrowPlanPaperEntries) {
        List<Long> projectIds = new ArrayList<>();
        List<Long> tomorrowPlanPaperEntryIds = tomorrowPlanPaperEntries.stream()
                .map(CostTomorrowPlanPaperEntry::getProjectId)
                .collect(Collectors.toList());
        List<Long> entryIds = entries.stream()
                .map(CostTaskDailyPaperEntry::getProjectId)
                .collect(Collectors.toList());
        projectIds.addAll(tomorrowPlanPaperEntryIds);
        projectIds.addAll(entryIds);
        List<Long> distinctProjectIds = projectIds.stream().distinct().collect(Collectors.toList());
        return projectInfoMapper.findByIdsForDailyPaperEntry(distinctProjectIds)
                .stream()
                .collect(Collectors.toMap(ProjectInDailyPaperEntry::getId, project -> project));
    }

    private CostTaskDailyPaperDetailsVO buildDailyPaperDetailsVO(CostTaskDailyPaper dailyPaper) {
        Long paperId = dailyPaper.getId();

        List<CostTaskDailyPaperEntry> entries = dailyPaperEntryMapper.findByDailyPaperId(paperId);

        List<CostTomorrowPlanPaperEntry> tomorrowPlanPaperEntries = costTomorrowPlanPaperEntryMapper.findByDailyPaperId(paperId);

        List<Long> allTaskIds = Lists.newArrayListWithCapacity(entries.size() + tomorrowPlanPaperEntries.size());

        entries.forEach(e -> allTaskIds.add(e.getTaskId()));

        tomorrowPlanPaperEntries.forEach(e -> allTaskIds.add(e.getTaskId()));

        List<CostDeliverTask> costDeliverTasks = CollUtil.isEmpty(allTaskIds) ?
                Collections.emptyList() : costDeliverTaskService.listByIds(allTaskIds);
        // 检查任务是否已结束
        Set<Long> taskIdsUnfinished = costDeliverTasks.stream()
                .filter(task -> task != null
                        && ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue().equals(task.getTaskType())
                        && CostTaskStatusEnum.ZC.getValue().equals(task.getTaskStatus()))
                .map(CostDeliverTask::getId)
                .collect(Collectors.toSet());
                
        // 需要额外将Project查出来，去取这些字段
        Map<Long, ProjectInDailyPaperEntry>  projects = this.findDailyAndTomorrowProjects(entries, tomorrowPlanPaperEntries);

        CostTaskDailyPaperDetailsVO dailyPaperDetailsVO = new CostTaskDailyPaperDetailsVO(dailyPaper, entries, tomorrowPlanPaperEntries, projects);
        List<CostTaskDailyPaperEntryVO> voEntries = dailyPaperDetailsVO.getEntries();
        List<CostTaskTomorrowPlanEntryVO> voTomorrowPlanPaperEntries = dailyPaperDetailsVO.getTomorrowPlanPaperEntries();
        
        // 获取工单类别Map
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        
        // 设置工时类型txt、工时是否结束
        if (CollUtil.isNotEmpty(voEntries)) {
            voEntries.forEach(entry -> {
                entry.setWorkTypeTxt(costTaskCategoryMap.getOrDefault(entry.getWorkType(), new CostTaskCategoryManagement()).getTaskCategoryName());
                if (taskIdsUnfinished.contains(entry.getTaskId())) {
                    entry.setTaskFinishFlag(false);
                } else {
                    entry.setTaskFinishFlag(true);
                    entry.setProjectId(null);
                    entry.setTaskId(null);
                }
            });
        }
        if (CollUtil.isNotEmpty(voTomorrowPlanPaperEntries)) {
            voTomorrowPlanPaperEntries.forEach(tomorrowPlanPaperEntry -> {
                tomorrowPlanPaperEntry.setWorkTypeTxt(costTaskCategoryMap.getOrDefault(tomorrowPlanPaperEntry.getWorkType(), new CostTaskCategoryManagement()).getTaskCategoryName());
                if (taskIdsUnfinished.contains(tomorrowPlanPaperEntry.getTaskId())) {
                    tomorrowPlanPaperEntry.setTaskFinishFlag(false);
                } else {
                    tomorrowPlanPaperEntry.setTaskFinishFlag(true);
                    tomorrowPlanPaperEntry.setProjectId(null);
                    tomorrowPlanPaperEntry.setTaskId(null);
                }
            });
        }

        return dailyPaperDetailsVO;
    }

    /**
     * ~ 添加/编辑 日报 ~
     *
     * @param request 请求实体
     * <AUTHOR>
     * @date 2022/8/24 9:58
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @ProjectStatusCheck(projectIdField = "#request.entries.![projectId]", userIdField = "#request.userId")
    public void newAddOrUpdate(CostTaskDailyPaperAddOrUpdateDTO request) {
        CostTaskDailyPaper addOrUpdate;
        Map<String, List<CostTaskDailyPaperEntry>> entries;
        List<Long> entriesNeedRemove;
        List<Long> tomorrowEntriesNeedRemove;
        List<CostTaskDailyPaperEntry> add;
        List<CostTaskDailyPaperEntry> update;

        // 明日计划
        Map<String, List<CostTomorrowPlanPaperEntry>> tomorrowPlanEntries;
        List<CostTomorrowPlanPaperEntry> tomorrowPlanAdd;
        List<CostTomorrowPlanPaperEntry> tomorrowPlanUpdate;

        validateDailyPaperAddOrUpdateDTO(request);
        if (request.getId() == null) {
            Timestamp submissionTime = null;
            if (request.getSubmit()) {
                //初始化提交时间
                submissionTime = new Timestamp(System.currentTimeMillis());
            }
            // add
            addOrUpdate = tryCreateDailyPaper(request, submissionTime);
            // 添加时手动将条目ID置空，防止前端传参错误，意外触发更新逻辑
            request.getEntries().forEach(e -> e.setId(null));
            request.getTomorrowPlanPaperEntries().forEach(e -> e.setId(null));

            entries = tryCreateDailyPaperEntryList(request, addOrUpdate);
            add = entries.get(PARAM_ADD);
            update = entries.get(PARAM_UPDATE);
            // 明日计划
            tomorrowPlanEntries = tryCreateTomorrowPlanPaperEntryList(request, addOrUpdate);
            tomorrowPlanAdd = tomorrowPlanEntries.get(PARAM_ADD);
            tomorrowPlanUpdate = tomorrowPlanEntries.get(PARAM_UPDATE);

            // 如果用户传条目参数时给定了id（传参不规范），则本应add的数据就会出现在update中，这时需要将其重新分配ID，添加到add中
            update.forEach(entry -> {
                BaseBuildEntityUtil.buildInsert(entry);
                add.add(entry);
            });
            tomorrowPlanUpdate.forEach(entry -> {
                BaseBuildEntityUtil.buildInsert(entry);
                tomorrowPlanAdd.add(entry);
            });

            // 插入日报数据
            mapper.insert(addOrUpdate);
            // 插入日报条目数据
            if (CollUtil.isNotEmpty(add)) {
                dailyPaperEntryMapper.batchSave(add);
            }
            // 插入明日计划条目数据
            if (CollUtil.isNotEmpty(tomorrowPlanAdd)) {
                costTomorrowPlanPaperEntryMapper.batchSave(tomorrowPlanEntries.get(PARAM_ADD));
            }
        } else {
            // update
            addOrUpdate = tryGetDailyPaperForUpdate(request);
            entries = tryCreateDailyPaperEntryList(request, addOrUpdate);
            add = entries.get(PARAM_ADD);
            update = entries.get(PARAM_UPDATE);
            // 明日计划
            tomorrowPlanEntries = tryCreateTomorrowPlanPaperEntryList(request, addOrUpdate);
            tomorrowPlanAdd = tomorrowPlanEntries.get(PARAM_ADD);
            tomorrowPlanUpdate = tomorrowPlanEntries.get(PARAM_UPDATE);
            // 找到传来的新日报中没有的条目数据，这些是要删除的
            entriesNeedRemove = dailyPaperEntryMapper.findByDailyPaperId(addOrUpdate.getId())
                    .stream()
                    .filter(e -> {
                        Long id = e.getId();
                        Predicate<CostTaskDailyPaperEntry> predicate = entry -> entry.getId().equals(id);

                        return entries.get(PARAM_ADD).stream().noneMatch(predicate) &&
                                entries.get(PARAM_UPDATE).stream().noneMatch(predicate) &&
                                // 如果遇到需要删除的数据是"已通过"状态，不进行删除，将这个数据保留处理（暂时这么处理，这种情况只会出现在请求体被篡改的情况下）
                                !YTG.getValue().equals(e.getApprovalStatus());
                    })
                    .map(BaseEntity::getId)
                    .collect(Collectors.toList());
            // 明日计划
            tomorrowEntriesNeedRemove = costTomorrowPlanPaperEntryMapper.findByDailyPaperId(addOrUpdate.getId())
                    .stream()
                    .filter(e -> {
                        Long id = e.getId();
                        Predicate<CostTomorrowPlanPaperEntry> predicate = entry -> entry.getDailyPaperId().equals(id);

                        return tomorrowPlanEntries.get(PARAM_ADD).stream().noneMatch(predicate) &&
                                tomorrowPlanEntries.get(PARAM_UPDATE).stream().noneMatch(predicate) &&
                                // 如果遇到需要删除的数据是"已通过"状态，不进行删除，将这个数据保留处理（暂时这么处理，这种情况只会出现在请求体被篡改的情况下）
                                !YTG.getValue().equals(e.getApprovalStatus());
                    })
                    .map(BaseEntity::getId)
                    .collect(Collectors.toList());

            mapper.updateById(addOrUpdate);
            // 保存请求新增的
            if (!add.isEmpty()) {
                dailyPaperEntryMapper.batchSave(entries.get(PARAM_ADD));
            }
            // 明日计划
            if (!tomorrowPlanAdd.isEmpty()) {
                costTomorrowPlanPaperEntryMapper.batchSave(tomorrowPlanEntries.get(PARAM_ADD));
            }
            // 修改原本已有的
            if (!update.isEmpty()) {
                dailyPaperEntryMapper.batchUpdate(entries.get(PARAM_UPDATE));
            }
            // 明日计划
            if (!tomorrowPlanUpdate.isEmpty()) {
                costTomorrowPlanPaperEntryMapper.batchUpdate(tomorrowPlanEntries.get(PARAM_UPDATE));
            }
            // 删除请求中没有，但原本有的
            if (!entriesNeedRemove.isEmpty()) {
                dailyPaperEntryMapper.deleteBatchIds(entriesNeedRemove);
                // 删除薪资明细
                costSalaryDetailsService.removeBatchByRelateIds(entriesNeedRemove,CostSalaryRelateTypeEnum.PRE_TASK_ACTUAL_COST);
            }
            // 明日计划
            if (!tomorrowEntriesNeedRemove.isEmpty()) {
                costTomorrowPlanPaperEntryMapper.deleteBatchIds(tomorrowEntriesNeedRemove);
            }
            // 应对这种情况：用户日报中原本有"已通过"条目和其他类型的条目，用户更新日报，删除了其他类型的条目，这时日报状态也需要更新
            updateApprovalStatusIfNeeded(ImmutableList.of(addOrUpdate.getId()), YTG);
            if (request.getSubmit()) {
                bcpLoggerUtils.log(FunctionConstants.REPORTING_OF_WORKING_HOURS, LogContentEnum.SUBMIT_DAILY_REPORT, request.getSubmissionDate());
            } else {
                bcpLoggerUtils.log(FunctionConstants.REPORTING_OF_WORKING_HOURS, LogContentEnum.SAVE_DAILY_REPORT, request.getSubmissionDate());

            }

        }
    }

    /**
     * ~ 验证添加/编辑 日报 的DTO对象是否合法 ~
     *
     * @param dto dto
     * <AUTHOR>
     * @date 2022/8/24 11:15
     */
    private void validateDailyPaperAddOrUpdateDTO(CostTaskDailyPaperAddOrUpdateDTO dto) {
        LocalDate nowLocalDate = LocalDate.now();
        LocalDate submissionDate = dto.getSubmissionDate();
        List<CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry> entries;
        Set<String> projectTaskTypeSet;
        BigDecimal sumHours, sumNormalHours;
        List<Long> taskIds;
        Set<Long> taskIdsUnfinished;

        if (nowLocalDate.isBefore(submissionDate)) {
            throw new ServiceException("不能填写未来的日报，请修改提交时间");
        }
        if (dto.getId() == null &&
                !mapper.findBySubmissionDateAndUserId(submissionDate, SecurityUtils.getUser().getId()).isEmpty()
        ) {
            throw new ServiceException("添加日报失败，目标日期下已存在日报");
        }

        entries = dto.getEntries();
        if (dto.getSubmit() && CollUtil.isEmpty(entries)) {
            throw new ServiceException("日报条目不能为空");
        }
        projectTaskTypeSet = Sets.newHashSetWithExpectedSize(entries.size());
        sumHours = BigDecimal.ZERO;
        sumNormalHours = BigDecimal.ZERO;
        for (CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry e : entries) {
            //由于部署前不存在workType这个字段导致有的人回退后的项目，因为通过的没有这个无法提交(暂时改成这样，后续可调整回去)
            int workType;
            if (YesOrNoEnum.NO.getValue().equals(e.getIsInsideProject()) && ObjectUtil.isEmpty(e.getWorkType())) {
//                throw new ServiceException("外部项目对应的工时类型不能为空");
                workType = -1;
            } else {
                workType = YesOrNoEnum.NO.getValue().equals(e.getIsInsideProject()) ? e.getWorkType() : 2;
            }
            String bigKey = "" + e.getProjectId() + e.getTaskId() + workType;

            if (dto.getSubmit()) {
                if (e.getNormalHours().compareTo(BigDecimal.ZERO) == 0 && e.getAddedHours().compareTo(BigDecimal.ZERO) == 0) {
                    throw new ServiceException("存在工时总计为0的日报条目，请认真检查");
                }
                if (projectTaskTypeSet.contains(bigKey)) {
                    throw new ServiceException("信息异常，项目+任务+工时类型不可重复");
                }
            }

            projectTaskTypeSet.add(bigKey);
            sumHours = sumHours.add(e.getNormalHours()).add(e.getAddedHours());
            sumNormalHours = sumNormalHours.add(e.getNormalHours());
        }

        if (sumHours.compareTo(ManHourValidator.TWENTY_FOUR_DECIMAL) > 0) {
            throw new ServiceException("当日填入工时数≥" + ManHourValidator.TWENTY_FOUR_DECIMAL + "小时，请确认后再提交");
        }
        if (sumNormalHours.compareTo(ManHourValidator.DAILY_NORMAL_WORKING_HOURS) > 0) {
            throw new ServiceException("日常工时总和不能超过" + ManHourValidator.DAILY_NORMAL_WORKING_HOURS + "小时，请确认后再提交");
        }
        if (YesOrNoEnum.YES.getValue().equals(filingMapper.isFiledByDate(submissionDate))) {
            throw new ServiceException("当前填报时间内存在归档日期，请重新选择或联系管理员！");
        }
        taskIds = dto.getEntries().stream()
                .filter(d -> !YTG.getValue().equals(d.getApprovalStatus()))
                .map(CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry::getTaskId)
                .distinct()
                .collect(Collectors.toList());
        if (taskIds.isEmpty()) {
            return;
        }
        List<CostDeliverTask> costDeliverTasks = costDeliverTaskService.listByIds(taskIds);
        // 检查任务是否已结束
        taskIdsUnfinished = costDeliverTasks.stream()
                .filter(task -> task != null && task.getTaskType().equals(0) && !task.getTaskStatus().equals(1))
                .map(CostDeliverTask::getId)
                .collect(Collectors.toSet());
                
        if (taskIdsUnfinished.size() != taskIds.size()) {
            throw new ServiceException("您填报的工单已结束，请重新选择或联系相关负责人");
        }
    }

    /**
     * ~ 根据DTO创建用于添加的日报对象 ~
     *
     * @param dto dto
     * @return com.gok.pboot.pms.entity.DailyPaper
     * <AUTHOR>
     * @date 2022/8/24 15:45
     */
    private CostTaskDailyPaper tryCreateDailyPaper(CostTaskDailyPaperAddOrUpdateDTO dto, Timestamp submissionTime) {
        Long userId = SecurityUtils.getUser().getId();
        CostTaskDailyPaper dailyPaper = new CostTaskDailyPaper();
        LocalDate submissionDate = dto.getSubmissionDate();
        List<CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry> entries = dto.getEntries();
        Set<Long> projectIds = Sets.newHashSetWithExpectedSize(entries.size());
        entries.forEach(e -> projectIds.add(e.getProjectId()));

        //根据submissionTime判定滞后，没有提交时间是正常的
        if (Optional.ofNullable(submissionTime).isPresent()) {
            dailyPaper.setSubmissionTime(submissionTime);
            //当前是提交日报，且日报没有晚于第二天14点都是正常的，不然算滞后
            boolean lag = isLag(submissionDate,
                    Instant.ofEpochMilli(submissionTime.getTime())
                            .atZone(ZoneId.systemDefault()).toLocalDateTime());
            dailyPaper.setFillingState(lag ? LAG.getValue() : NORMAL.getValue());
        } else {
            dailyPaper.setFillingState(NORMAL.getValue());
        }
        Roster userInfo = rosterMapper.selectById(userId);
        //填报状态， 如果不提交，要判断条目是否为空，据此决定日报是未提交还是未填报
        ApprovalStatusEnum approvalStatusEnum = dto.getSubmit() ? DSH : CollUtil.isEmpty(dto.getEntries()) ? WTB : WTJ;
        dailyPaper.setUserId(userId)
                .setUserStatus(userInfo == null ? PersonnelStatusEnum.ZS.getValue() :
                        PersonnelStatusEnum.getByEmployeeStatusEnum(
                                EnumUtils.getEnumByValue(EmployeeStatusEnum.class, userInfo.getEmployeeStatus())
                                ).getValue()
                )
                .setUserDeptId(userInfo == null ? null : userInfo.getDeptId())
                .setSubmissionDate(submissionDate)
                // 判断是否为工作日，此处先写死为否
                .setWorkday(holidayMapper.exists(submissionDate) ? BaseConstants.NO : BaseConstants.YES)
                .setApprovalStatus(approvalStatusEnum.getValue())
                .setProjectCount(projectIds.size())
                .setTaskCount(entries.size())
                .setDailyHourCount(
                        entries.stream()
                                .map(CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry::getNormalHours)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO)
                )
                .setAddedHourCount(
                        entries.stream()
                                .map(CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry::getAddedHours)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO)
                );

        return BaseBuildEntityUtil.buildInsert(dailyPaper);
    }

    /**
     * ~ 判断指定日期是否是滞后提交的日期 ~
     *
     * @param firstSubmitDate 日期
     * @return boolean
     * <AUTHOR>
     * @date 2022/9/19 9:59
     */
    public static boolean isLag(LocalDate submit, LocalDateTime firstSubmitDate) {
        long until = submit.until(firstSubmitDate, ChronoUnit.DAYS);

        if (until > 1) {
            return true;
        } else if (until == 0) {
            return false;
        } else {
            // util == 1：如果当前正好处在日报日期的第二天，则判断是否已经过了下午两点
            return firstSubmitDate.getHour() >= 14;
        }
    }

    /**
     * ~ 根据所给添加日报DTO获取项目 ~
     *
     * @param dto dto
     * @return com.gok.pboot.pms.entity.Project
     * <AUTHOR>
     * @date 2022/8/24 11:22
     */
    private CostTaskDailyPaper tryGetDailyPaperForUpdate(CostTaskDailyPaperAddOrUpdateDTO dto) {
        CostTaskDailyPaper dailyPaper = mapper.selectById(dto.getId());
        CostTaskDailyPaper result;
        boolean dailyPaperStatusInvalid;
        Integer dailyPaperStatus;
        if (dailyPaper == null) {
            throw new ServiceException("信息异常，没有找到要修改的日报");
        }
        dailyPaperStatus = dailyPaper.getApprovalStatus();
        // 已通过部分不可编辑，但可新增工时（不满7个小时部分和加班部分）
        dailyPaperStatusInvalid =
                !(
                        WTB.getValue().equals(dailyPaperStatus) ||
                                WTJ.getValue().equals(dailyPaperStatus) ||
                                DSH.getValue().equals(dailyPaperStatus) ||
                                BTG.getValue().equals(dailyPaperStatus) ||
                                YTH.getValue().equals(dailyPaperStatus) ||
                                YTG.getValue().equals(dailyPaperStatus)
                );
        if (dailyPaperStatusInvalid) {
            throw new ServiceException("当前日报的状态不允许编辑");
        }
        Timestamp submissionTime = dailyPaper.getSubmissionTime();
        //更新操作  日报提交,且提交时间没有值时进行赋值
        if (dto.getSubmit() && !Optional.ofNullable(dailyPaper.getSubmissionTime()).isPresent()) {
            submissionTime = new Timestamp(System.currentTimeMillis());
        }

        result = tryCreateDailyPaper(dto, submissionTime);
        result.setId(dailyPaper.getId());
        // 如果保存不提交，且日报处于"已退回"或"不通过"，则不改变日报的状态（否则按照tryCreateDailyPaper中设置的，将日报状态设置为"未提交"）
//        doNotUpdateApprovalStatus = !dto.getSubmit() &&
//                (
//                        YTH.getValue().equals(dailyPaper.getApprovalStatus()) ||
//                                BTG.getValue().equals(dailyPaper.getApprovalStatus())
//                );
//        if (doNotUpdateApprovalStatus) {
//            // 将tryCreate...方法生成的新日报设置为日报原本的状态
//            result.setApprovalStatus(dailyPaper.getApprovalStatus());
//        }
        //修改将创建时间置空

        result.setCtime(dailyPaper.getCtime());
        BaseBuildEntityUtil.buildUpdate(result);
        // 更新提交时间
        result.setSubmissionTime(new Timestamp(System.currentTimeMillis()));

        return result;
    }

    /**
     * ~ 根据所给添加日报DTO获取日报条目（Map Key：PARAM_ADD和"update"） ~
     *
     * @param dto        dto
     * @param dailyPaper 要添加或编辑的日报对象
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/8/24 11:28
     */
    private Map<String, List<CostTaskDailyPaperEntry>> tryCreateDailyPaperEntryList(CostTaskDailyPaperAddOrUpdateDTO dto, CostTaskDailyPaper dailyPaper) {
        Long userId = SecurityUtils.getUser().getId();
        List<CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry> entries = dto.getEntries();
        // 添加的日报条目
        List<CostTaskDailyPaperEntry> resultForAdd = Lists.newArrayListWithCapacity(entries.size());
        // 修改的日报条目
        List<CostTaskDailyPaperEntry> resultForUpdate = Lists.newArrayListWithCapacity(entries.size());
        final Integer approvalStatus = dto.getSubmit() ? DSH.getValue() : WTJ.getValue();
        // 获取计算工资的DTO
        List<CalculateLaborCostDTO> calculateLaborCostDTOList = new ArrayList<>();

        for (CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry e : entries) {
            CostTaskDailyPaperEntry entry;
            if (dto.getId() != null && e.getId() != null) {
                Long entryId = e.getId();
                 entry = dailyPaperEntryMapper.selectById(entryId);
                if (ObjectUtil.isEmpty(entry)) {
                    throw new ServiceException("信息异常，日报条目不存在");
                }
                Integer oldApprovalStatus = entry.getApprovalStatus();
                //已通过的跳过不做校验
                if (YTG.getValue().equals(oldApprovalStatus)) {
                    continue;
                }
                // 如果条目原本是已通过的，则原封不动将对象放入更新列表，不是已通过的才进行修改后再放入
                // 如果保存不提交，且原条目为"已退回"或"不通过"状态，则不改变原本状态
                entry = checkDailyPaper(e, dailyPaper).setApprovalStatus(approvalStatus);
                entry.setId(entryId);
                BaseBuildEntityUtil.buildUpdate(entry);
                distinguishOvertimeHoursType(entry);
                resultForUpdate.add(entry);
            } else {
                entry = checkDailyPaper(e, dailyPaper);
                entry.setApprovalStatus(approvalStatus);
                distinguishOvertimeHoursType(entry);
                resultForAdd.add(entry);
            }
            if (dto.getSubmit()) {
                calculateLaborCostDTOList.add(new CalculateLaborCostDTO()
                        .setId(entry.getId()).setUserId(userId)
                        .setRelateId(entry.getId())
                        .setRelateTypeEnum(CostSalaryRelateTypeEnum.PRE_TASK_ACTUAL_COST)
                        .setNormalHours(entry.getNormalHours())
                        .setWorkOvertimeHours(entry.getWorkOvertimeHours())
                        .setRestOvertimeHours(entry.getRestOvertimeHours())
                        .setHolidayOvertimeHours(entry.getHolidayOvertimeHours()));
            }
        }

        // 计算人工成本
        Map<Long, CostSalaryDTO> laborCostMap = costConfigLevelPriceService.batchCalculateLaborCost(calculateLaborCostDTOList);
        backFillLaborCost(resultForAdd, laborCostMap);
        backFillLaborCost(resultForUpdate, laborCostMap);
        // 保存人工成本明细
        costSalaryDetailsService.batchSaveOrUpdate(laborCostMap.values());
        return ImmutableMap.of(PARAM_ADD, resultForAdd, PARAM_UPDATE, resultForUpdate);
    }

    private static void backFillLaborCost(List<CostTaskDailyPaperEntry> paperEntries, Map<Long, CostSalaryDTO> laborCostMap) {
        paperEntries.forEach(entry -> {
            CostSalaryDTO costSalaryDTO = laborCostMap.getOrDefault(entry.getId(), CostSalaryDTO.empty());
            BigDecimal laborCost = costSalaryDTO.getLaborCost();
            if (laborCost != null) {
                entry.setActualLaborCost(laborCost.toString());
                entry.encrypt();
            }
        });
    }

    /**
     * 区分加班工时
     */
    private void distinguishOvertimeHoursType(CostTaskDailyPaperEntry entry) {
        //判断加班类型
        entry.setHolidayOvertimeHours(BigDecimal.ZERO);
        entry.setWorkOvertimeHours(BigDecimal.ZERO);
        entry.setRestOvertimeHours(BigDecimal.ZERO);
        Integer holidayType = holidayMapper.findHolidayType(entry.getSubmissionDate());
        if (holidayType != null && holidayType == 1) {
            entry.setHolidayOvertimeHours(entry.getAddedHours());
        } else if (holidayType != null && holidayType == 0) {
            entry.setRestOvertimeHours(entry.getAddedHours());
        } else {
            entry.setWorkOvertimeHours(entry.getAddedHours());
        }
    }


    /**
     * 新增编辑校验
     */
    private CostTaskDailyPaperEntry checkDailyPaper(CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry e, CostTaskDailyPaper dailyPaper) {
        Long projectId = e.getProjectId();
        ProjectInfo project = projectInfoMapper.selectById(projectId);
        if (project == null) {
            throw new ServiceException("信息异常，未找到目标项目");
        }
        // 新旧任务区分校验
        TaskBo taskBo = validateAndGetTask(true, e.getOldTaskFlag(), projectId, e.getTaskId());
        return createDailyPaperEntry(project, taskBo, dailyPaper, e);
    }

    /**
     * ~ 根据所给添加日报DTO获取明日计划条目（Map Key：PARAM_ADD和"update"）+ 明日计划 ~
     *
     * @param dto        dto
     * @param dailyPaper 要添加或编辑的日报对象
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 20223/2/07 16:11
     */
    private Map<String, List<CostTomorrowPlanPaperEntry>> tryCreateTomorrowPlanPaperEntryList(CostTaskDailyPaperAddOrUpdateDTO dto, CostTaskDailyPaper dailyPaper) {
        List<CostTaskDailyPaperAddOrUpdateDTO.TomorrowPlanPaperEntry> tomorrowPlanPaperEntrys;
        ProjectInfo project;
        Long projectId;

        List<CostTomorrowPlanPaperEntry> tomorrowPlanResultForAdd;
        List<CostTomorrowPlanPaperEntry> tomorrowPlanResultForUpdate;
        CostTomorrowPlanPaperEntry tomorrowPlanPaperEntry;
        Long entryId;
        final Integer approvalStatus = dto.getSubmit()
                ? DSH.getValue()
                : WTJ.getValue();
        Integer oldApprovalStatus;
        boolean doNotUpdateApprovalStatus;
        // 明日计划
        TaskBo taskBo;
        tomorrowPlanPaperEntrys = dto.getTomorrowPlanPaperEntries();
        tomorrowPlanResultForAdd = Lists.newArrayListWithCapacity(tomorrowPlanPaperEntrys.size());
        tomorrowPlanResultForUpdate = Lists.newArrayListWithCapacity(tomorrowPlanPaperEntrys.size());
        for (CostTaskDailyPaperAddOrUpdateDTO.TomorrowPlanPaperEntry e : tomorrowPlanPaperEntrys) {
            projectId = e.getProjectId();
            project = projectInfoMapper.selectById(projectId);
            if (project == null) {
                throw new ServiceException("信息异常，未找到目标项目");
            }
            // 校验并获取任务实体类
            taskBo = validateAndGetTask(false, e.getOldTaskFlag(), projectId, e.getTaskId());
            if (dto.getId() != null && e.getId() != null) {
                entryId = e.getId();
                tomorrowPlanPaperEntry = costTomorrowPlanPaperEntryMapper.selectById(entryId);
                if (tomorrowPlanPaperEntry == null) {
                    throw new ServiceException("明日计划填报信息异常，日报条目不存在");
                }
                oldApprovalStatus = tomorrowPlanPaperEntry.getApprovalStatus();
                doNotUpdateApprovalStatus = !dto.getSubmit() &&
                        (
                                YTH.getValue().equals(dailyPaper.getApprovalStatus()) ||
                                        BTG.getValue().equals(dailyPaper.getApprovalStatus())
                        );
                // 如果条目原本是已通过的，则原封不动将对象放入更新列表，不是已通过的才进行修改后再放入
                // 如果保存不提交，且原条目为"已退回"或"不通过"状态，则不改变原本状态
                if (!YTG.getValue().equals(oldApprovalStatus) && !doNotUpdateApprovalStatus) {
                    tomorrowPlanPaperEntry = createTomorrowPlanPaperEntry(project, taskBo, dailyPaper, e)
                            .setApprovalStatus(approvalStatus);
                    tomorrowPlanPaperEntry.setId(entryId);
                    BaseBuildEntityUtil.buildUpdate(tomorrowPlanPaperEntry);
                }
                tomorrowPlanResultForUpdate.add(tomorrowPlanPaperEntry);
            } else {
                tomorrowPlanPaperEntry = createTomorrowPlanPaperEntry(project, taskBo, dailyPaper, e);
                tomorrowPlanResultForAdd.add(tomorrowPlanPaperEntry.setApprovalStatus(approvalStatus));
            }
        }


        return ImmutableMap.of(PARAM_ADD, tomorrowPlanResultForAdd, PARAM_UPDATE, tomorrowPlanResultForUpdate);
    }

    private TaskBo validateAndGetTask(Boolean isDailyPaper, Integer oldTaskFlag, Long projectId, Long taskId) {
        // 取消新旧任务判断（新旧任务整合到新任务表）
        CostDeliverTask costDeliverTask = costDeliverTaskService.getById(taskId);
        if (!Optional.ofNullable(costDeliverTask).isPresent()) {
            throw new ServiceException("信息异常，未找到目标任务~");
        }
        if (!costDeliverTask.getProjectId().equals(projectId)) {
            throw new ServiceException("信息异常，目标项目与任务不匹配~");
        }
        // 只支持售前支撑类型的工单
        if (!EnumUtils.valueEquals(costDeliverTask.getTaskType(), ProjectTaskKindEnum.PRE_SALES_SUPPORT)) {
            throw new ServiceException("当前页面只支持售前支撑类型的工单填写工时~");
        }
        // 售前支撑类型的工单只有正常和结束两种状态,当状态为结束时不允许填写
        if (EnumUtils.valueEquals(costDeliverTask.getTaskStatus(), CostTaskStatusEnum.JS)) {
            if (Boolean.TRUE.equals(isDailyPaper)) {
                throw new ServiceException("操作失败，" + costDeliverTask.getTaskName() + "任务已关闭，无法继续填写工时");
            } else {
                throw new ServiceException("操作失败，" + costDeliverTask.getTaskName() + "任务已关闭，无法继续填写明日计划");
            }
        }
        return TaskBo.form(costDeliverTask);
    }

    private CostTaskDailyPaperEntry createDailyPaperEntry(
            ProjectInfo project,
            TaskBo task,
            CostTaskDailyPaper dailyPaper,
            CostTaskDailyPaperAddOrUpdateDTO.DailyPaperEntry dtoEntry
    ) {
        // 设置工时类型，从任务类型获取
        dtoEntry.setWorkType(task.getKind());
        // 设置旧任务标识（已取消旧任务，默认新任务）
        dtoEntry.setOldTaskFlag(YesOrNoEnum.NO.getValue());

        CostTaskDailyPaperEntry result = new CostTaskDailyPaperEntry();
        PigxUser user = SecurityUtils.getUser();
        Long deptId;
        Roster roster;

        log.info("填报获取用户{}", JSON.toJSON(user));
        result.setProjectId(project.getId());
        result.setProjectName(project.getItemName());
        result.setDailyPaperId(dailyPaper.getId());
        result.setSubmissionDate(dailyPaper.getSubmissionDate());
        result.setNormalHours(dtoEntry.getNormalHours());
        result.setAddedHours(dtoEntry.getAddedHours());
        result.setDescription(dtoEntry.getDescription());
        result.setApprovalReason("");
        result.setUserId(user.getId());
        result.setUserRealName(user.getName());
        deptId = user.getDeptId();
        if (deptId == null) {
            roster = rosterMapper.selectById(user.getId());
            deptId = roster.getDeptId();
        }
        result.setUserDeptId(deptId);
        result.setWorkType(dtoEntry.getWorkType());
        result.setOldTaskFlag(dtoEntry.getOldTaskFlag());
        result.setTaskId(task.getTaskId());
        result.setTaskName(task.getTaskName());

        BaseBuildEntityUtil.buildInsert(result);

        return result;
    }

    private CostTomorrowPlanPaperEntry createTomorrowPlanPaperEntry(
            ProjectInfo project,
            TaskBo task,
            CostTaskDailyPaper dailyPaper,
            CostTaskDailyPaperAddOrUpdateDTO.TomorrowPlanPaperEntry dtoEntry
    ) {
        // 设置工时类型，从任务类型获取
        dtoEntry.setWorkType(task.getKind());
        // 设置旧任务标识（已取消旧任务，默认新任务）
        dtoEntry.setOldTaskFlag(YesOrNoEnum.NO.getValue());

        CostTomorrowPlanPaperEntry result = new CostTomorrowPlanPaperEntry();
        PigxUser user = SecurityUtils.getUser();
        Roster roster;
        Long deptId;

        result.setProjectId(project.getId());
        result.setProjectName(project.getItemName());
        result.setDailyPaperId(dailyPaper.getId());
        result.setSubmissionDate(dailyPaper.getSubmissionDate());
        result.setDescription(dtoEntry.getDescription());
        result.setApprovalReason("");
        result.setUserId(user.getId());
        result.setUserRealName(user.getName());
        deptId = user.getDeptId();
        if (deptId == null) {
            roster = rosterMapper.selectById(user.getId());
            deptId = roster.getDeptId();
        }
        result.setUserDeptId(deptId);
        result.setWorkType(dtoEntry.getWorkType());
        result.setTaskId(task.getTaskId());
        result.setTaskName(task.getTaskName());
        result.setOldTaskFlag(dtoEntry.getOldTaskFlag());
        BaseBuildEntityUtil.buildInsert(result);

        return result;
    }

    @Override
    public List<CostTaskDailyPaperVO> listDailyPaperMonthly(CostTaskDailyPaperMonthlyDTO request) {
        LocalDate startDate = LocalDateTimeUtil.parseDate(request.getStartDate() + "-01", "yyyy-MM-dd");
        LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
        Long userId = SecurityUtils.getUser().getId();
        String projectNameLike = request.getProjectName();
        Integer approvalTab = request.getApprovalStatusTab();
        boolean queryAbnormal = EnumUtils.valueEquals(approvalTab, YC);
        List<Integer> approvalList = request.getApprovalStatusList();
        boolean queryUnFilled = approvalTab == null && CollUtil.contains(approvalList, WTB.getValue());
        // 查询月份是否归档
        boolean isFiled = EnumUtils.valueEquals(filingMapper.isFiledByDate(startDate), YesOrNoEnum.YES);
        // 查询假期数据
        Set<LocalDate> holidayDates = holidayMapper.findByDateRange(startDate, endDate);
        boolean useSearch = StringUtils.isNotBlank(projectNameLike) ||
                CollUtil.isNotEmpty(approvalList) ||
                approvalTab != null;
        // 查询oa的加班、请假数据
        List<OvertimeLeaveData> overtimeLeaveDataList = overtimeLeaveDataMapper.findByDateRangeAndUserId(startDate, endDate, userId);
        //查询OA项目调休数据
        List<CompensatoryLeaveDataVO> compensatoryLeaveDataDetailVOList
                = compensatoryLeaveDataService.findByDateTimeRangeAndUserId(startDate, endDate, userId);

        Map<LocalDate, BigDecimal> colcompensatoryLeaveDataMap = new HashMap<>(0);
        if (CollUtil.isNotEmpty(compensatoryLeaveDataDetailVOList)) {
            colcompensatoryLeaveDataMap = compensatoryLeaveDataDetailVOList.stream()
                    .collect(Collectors.groupingBy(CompensatoryLeaveDataVO::getBelongDate,
                            // 初始值
                            Collectors.reducing(BigDecimal.ZERO, CompensatoryLeaveDataVO::getHourData, BigDecimal::add)
                    ));
        }
        int olDataSize = overtimeLeaveDataList.size();
        Map<LocalDate, BigDecimal> dateAndOvertimeHourMap = Maps.newHashMapWithExpectedSize(olDataSize);
        Map<LocalDate, BigDecimal> dateAndLeaveHourMap = Maps.newHashMapWithExpectedSize(olDataSize);
        Stream<CostTaskDailyPaperVO> resultStream;
        // 查询日报：通过当月开始时间、当月结束时间、用户id、项目名称
        Set<LocalDate> datesHasApprovalStatusNotWtb;

        if (queryAbnormal) {
            approvalTab = null;
        }
        List<CostTaskDailyPaperVO> result = mapper.findBySubmissionDateRange(
                startDate, endDate, userId, projectNameLike, queryAbnormal ? null : approvalTab, approvalList
        );
        // 整理各日期的请假、加班数据
        overtimeLeaveDataList.forEach(ol -> {
            String type = ol.getType();
            LocalDate date = ol.getBelongdate();
            BigDecimal hours = ol.getHourData();

            if (EnumUtils.valueEquals(type, LeaveStatusEnum.XJ)) {
                dateAndLeaveHourMap.put(date, dateAndLeaveHourMap.getOrDefault(date, BigDecimal.ZERO).subtract(hours));
            } else if (EnumUtils.valueEquals(type, LeaveStatusEnum.JB)) {
                if (ol.getXmmc() == null) {
                    // 没有项目ID，说明加班工时没有挂靠在项目上，将其忽略
                    return;
                }
                dateAndOvertimeHourMap.put(date, dateAndOvertimeHourMap.getOrDefault(date, BigDecimal.ZERO).add(hours));
            } else {
                // 其他情况均为请假
                dateAndLeaveHourMap.put(date, dateAndLeaveHourMap.getOrDefault(date, BigDecimal.ZERO).add(hours));
            }
        });
        if (!useSearch) {
            // 如果用户没有使用搜索框，则将无日报的日子也填充上空日报信息
            fillPaperInHolidaysAndNones(
                    startDate, result, dateAndOvertimeHourMap, dateAndLeaveHourMap, holidayDates, isFiled, null
            );
        }
        if (queryUnFilled) {
            // 如果用户查询未填报的日报，则查询所有拥有非"未填报"状态日报的日期，并据此将缺失的日报补充上去
            datesHasApprovalStatusNotWtb = mapper.findSubmissionDateByUserIdAndSubmissionDateRangeAndApprovalStatusNot(
                    userId, startDate, endDate, WTB.getValue()
            );
            fillPaperInHolidaysAndNones(
                    startDate,
                    result,
                    dateAndOvertimeHourMap,
                    dateAndLeaveHourMap,
                    holidayDates,
                    isFiled,
                    datesHasApprovalStatusNotWtb
            );
            // 进行一次过滤，排除节假日且未填报的日报
            result = result.stream()
                    .filter(x -> !(
                            holidayDates.contains(x.getSubmissionDate()) &&
                                    EnumUtils.valueEquals(x.getApprovalStatus(), WTB)
                    ))
                    .collect(Collectors.toList());
        }
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        LinkedHashMap<LocalDate, Integer> holidayMap = holidayList.stream()
                .filter(h -> h.getHolidayType() != null)
                .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));
        // 组装数据到日报对象、排序
        Map<LocalDate, BigDecimal> finalColcompensatoryLeaveDataMap = colcompensatoryLeaveDataMap;
        resultStream = result.stream()
                .sorted(Comparator.comparing(CostTaskDailyPaperVO::getSubmissionDate, Comparator.reverseOrder()))
                .peek(x -> {
                    LocalDate submissionDate = x.getSubmissionDate();
                    BigDecimal hourData = dateAndOvertimeHourMap.getOrDefault(submissionDate, BigDecimal.ZERO);
                    BigDecimal leaveHourData = dateAndLeaveHourMap.getOrDefault(submissionDate, BigDecimal.ZERO);
                    BigDecimal colcompensatoryLeaveData = finalColcompensatoryLeaveDataMap.getOrDefault(submissionDate, BigDecimal.ZERO);
                    List<Pair<DailyPaperAbnormalEnum, String>> abnormalInfo = dailyPaperAnalyzer.getAbnormalInfo(
                                    x.getSubmissionDate(),
                                    x.getApprovalStatus(),
                                    x.getWorkday(),
                                    x.getDailyHourCount(),
                                    leaveHourData.add(colcompensatoryLeaveData)
                            ).stream()
                            .filter(pair -> DailyPaperAbnormalEnum.SUBMIT == pair.getFirst())
                            .collect(Collectors.toList());
                    LocalDateTime ctime = x.getCtime();

                    if (abnormalInfo.isEmpty()) {
                        x.setIfAbnormal(false);
                        x.setAbnormalMsg(StringUtils.EMPTY);
                    } else {
                        x.setIfAbnormal(true);
                        x.setAbnormalMsg(abnormalInfo.get(0).getSecond());
                    }
                    x.setIfFiled(isFiled);
                    x.setIfHoliday(holidayDates.contains(submissionDate));
                    x.setLeaveHourData(leaveHourData);
                    x.setCtimeFormatted(x.getSubmissionTime() == null ? "" : DailyPaperDateUtils.asDatetimeString(x.getSubmissionTime()));
                    if (ObjectUtil.isEmpty(x.getMtime())) {
                        x.setMtime(ctime);
                    }
                    x.setWorkdayName(
                            BaseConstants.YES.equals(x.getWorkday()) && !WTB.getValue().equals(x.getApprovalStatus()) ?
                                    "正常提交" : "无日报"
                    );
                    x.setUserStatusName(EnumUtils.getNameByValue(PersonnelStatusEnum.class, x.getUserStatus()));
                    x.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(submissionDate));
                    x.setHolidayType(holidayMap.get(submissionDate));
                    x.setCompensatoryHourCount(colcompensatoryLeaveData);
                    //假期若未填写日报，状态放空处理
                    if (BaseConstants.YES.equals(x.getWorkday()) || !WTB.getValue().equals(x.getApprovalStatus())) {
                        x.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, x.getApprovalStatus()));
                    }
                    leaveHourData = BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHourData) < 0 ?
                            BigDecimalUtils.SEVEN_DECIMAL : leaveHourData;
                    x.setHourData(hourData);
                    x.setTotalHour(x.getDailyHourCount().add(hourData).add(leaveHourData));
                    // 写入日期对应的多状态（若有）状态值
                    String approvalStatusStrings = x.getApprovalStatusStrings();
                    if (StringUtils.isNotBlank(approvalStatusStrings)) {
                        String[] split = approvalStatusStrings.split(",");
                        List<String> approvalStatusNameList = Arrays.stream(split)
                                .map(e -> EnumUtils.getNameByValue(ApprovalStatusEnum.class, Integer.parseInt(e)))
                                .collect(Collectors.toList());
                        x.setApprovalStatusNameList(approvalStatusNameList);
                    }
                    x.setProjectCount((x.getProjectCount() != null && x.getProjectCount() == 0) ? null : x.getProjectCount());
                    x.setTaskCount((x.getTaskCount() != null && x.getTaskCount() == 0) ? null : x.getTaskCount());
                    x.setDailyHourCount(getDataNull(x.getDailyHourCount()));
                    x.setAddedHourCount(getDataNull(x.getAddedHourCount()));
                    x.setHourData(getDataNull(x.getHourData()));
                    x.setCompensatoryHourCount(getDataNull(x.getCompensatoryHourCount()));
                    x.setLeaveHourData(getDataNull(x.getLeaveHourData()));

                });
        if (queryAbnormal) {
            // 如果查询的是异常日报，额外做一步过滤
            resultStream = resultStream.filter(CostTaskDailyPaperVO::getIfAbnormal);
        }
        return resultStream.collect(Collectors.toList());
    }

    private BigDecimal getDataNull(BigDecimal data) {
        return (data != null && (data.equals(BigDecimal.ZERO)
                || data.equals(new BigDecimal("0.0"))
                || data.equals(new BigDecimal("0.00"))))
                ? null : data;
    }

    /**
     * ~ 将日期月份剩余没有日报的天数填充进空日报，过滤掉无需填充的日报 ~
     *
     * @param papers              日报列表
     * @param startDate           日期
     * @param dateAndOvertimeMap  日期对应加班工时map
     * @param dateAndLeaveHourMap 日期对应请假工时map
     * @param holidayDates        节假日集合
     * @param ifFiled             月份数据是否已归档
     * @param datesExclude        无需填充的日期
     * <AUTHOR>
     */
    private void fillPaperInHolidaysAndNones(
            LocalDate startDate,
            List<CostTaskDailyPaperVO> papers,
            Map<LocalDate, BigDecimal> dateAndOvertimeMap,
            Map<LocalDate, BigDecimal> dateAndLeaveHourMap,
            Set<LocalDate> holidayDates,
            boolean ifFiled,
            @Nullable Set<LocalDate> datesExclude
    ) {
        // 获取已存在日报的日期列表
        Set<LocalDate> existsDates = ImmutableSet.copyOf(
                papers.stream()
                        .map(CostTaskDailyPaperVO::getSubmissionDate)
                        .collect(Collectors.toSet())
        );
        LocalDateTime now = LocalDateTime.now();
        LocalDate nowDate = now.toLocalDate();
        LocalDate date = startDate;
        int startMonth = startDate.getMonthValue();

        if (datesExclude == null) {
            datesExclude = ImmutableSet.of();
        }
        // 当日期≥当前时间，且日期对应月份=需添加空日报月份，进入循环
        while (!date.isAfter(nowDate) && date.getMonthValue() == startMonth) {
            // 当日期不包含在已存在日报的日期列表时，进行日报填充
            if (!existsDates.contains(date) && !datesExclude.contains(date)) {
                BigDecimal hourData = dateAndOvertimeMap.getOrDefault(date, BigDecimal.ZERO);
                BigDecimal leaveHourData = dateAndLeaveHourMap.getOrDefault(date, BigDecimal.ZERO);

                leaveHourData = BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHourData) < 0 ?
                        BigDecimalUtils.SEVEN_DECIMAL : leaveHourData;
                papers.add(
                        CostTaskDailyPaperVO.builder()
                                .approvalStatus(WTB.getValue())
                                .submissionDate(date)
                                .submissionDateFormatted(DailyPaperDateUtils.asDateString(date))
                                .approvalStatusName(holidayDates.contains(date) ? null : WTB.getName())
                                .hourData(hourData)
                                .leaveHourData(leaveHourData)
                                .totalHour(hourData.add(leaveHourData))
                                .ctimeFormatted(Strings.EMPTY)
                                .workdayName("无日报")
                                .projectCount(0)
                                .taskCount(0)
                                .dailyHourCount(BigDecimal.ZERO)
                                .addedHourCount(BigDecimal.ZERO)
                                .ifFiled(ifFiled)
                                .ifHoliday(holidayDates.contains(date))
                                .ifAbnormal(false)
                                .abnormalMsg(StringUtils.EMPTY)
                                .build()
                );
            }
            date = date.plusDays(1);
        }
    }

    @Override
    public CostTaskDailyPapersStatisticMonthlyVO getStatisticMonthly(CostTaskDailyPaperMonthlyDTO request) {
        List<CostTaskDailyPaperVO> papers = listDailyPaperMonthly(request);

        return sumWorkHourForDailyPapers(papers);
    }

    /**
     * ~ 根据日报计算按月统计数据 ~
     *
     * @param papers 日报列表
     * @return com.gok.pboot.pms.entity.vo.DailyPapersStatisticMonthlyVO
     * <AUTHOR>
     * @date 2022/11/1 15:36
     */
    private CostTaskDailyPapersStatisticMonthlyVO sumWorkHourForDailyPapers(List<CostTaskDailyPaperVO> papers) {
        int total = papers.size();
        int waitingReview = 0;
        int abnormal = 0;
        List<Pair<DailyPaperAbnormalEnum, String>> abnormalInfo;

        if (total < 1) {
            return CostTaskDailyPapersStatisticMonthlyVO.of(0, 0, 0);
        }
        for (CostTaskDailyPaperVO paper : papers) {
            if (paper.getIfAbnormal()) {
                abnormalInfo = dailyPaperAnalyzer.getAbnormalInfo(
                        paper.getSubmissionDate(),
                        paper.getApprovalStatus(),
                        paper.getWorkday(),
                        paper.getDailyHourCount(),
                        paper.getLeaveHourData()
                );
                for (Pair<DailyPaperAbnormalEnum, String> typeAndMsg : abnormalInfo) {
                    switch (typeAndMsg.getFirst()) {
                        case SUBMIT:
                            abnormal++;
                            break;
                        case WAITING_REVIEW:
                            waitingReview++;
                            break;
                        default:
                    }
                }
            } else if (EnumUtils.valueEquals(paper.getApprovalStatus(), DSH)) {
                waitingReview++;
            }

        }

        return CostTaskDailyPapersStatisticMonthlyVO.of(total, waitingReview, abnormal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatusIfNeeded(List<Long> dailyPaperIds, ApprovalStatusEnum targetStatus) {
        Map<Long, CostTaskDailyPaper> dailyPapers =
                mapper.selectList(new QueryWrapper<CostTaskDailyPaper>().in("id", dailyPaperIds))
                        .stream()
                        .collect(Collectors.toMap(BaseEntity::getId, paper -> paper));
        Multimap<Long, CostTaskDailyPaperEntry> entries = HashMultimap.create(dailyPapers.size(), 5);
        boolean needUpdate;
        CostTaskDailyPaper dailyPaper;
        List<CostTaskDailyPaper> papersNeedUpdate = Lists.newArrayListWithCapacity(dailyPapers.size());

        if (dailyPapers.isEmpty()) {
            return;
        }
        if (targetStatus == BTG) {
            // 如果要更新的状态是"不通过"，则直接变更日报状态后结束
            dailyPapers.values()
                    .stream()
                    // 筛选出需要变更状态的日报
                    .filter(paper -> !targetStatus.getValue().equals(paper.getApprovalStatus()))
                    .forEach(paper -> {
                        paper.setApprovalStatus(targetStatus.getValue());
                        BaseBuildEntityUtil.buildUpdate(paper);
                        papersNeedUpdate.add(paper);
                    });
            if (!papersNeedUpdate.isEmpty()) {
                mapper.updateApprovalStatus(papersNeedUpdate);
            }

            return;
        } else if (targetStatus == YTH) {
            // 如果要更新的状态是"已退回"，则直接变更日报状态后结束
            dailyPapers.values()
                    .stream()
                    // 筛选出需要变更状态的日报（日报为"不通过"时，不更新状态）
                    .filter(paper -> {
                        Integer approvalStatus = paper.getApprovalStatus();

                        return !EnumUtils.valueEquals(approvalStatus, targetStatus) &&
                                !EnumUtils.valueEquals(approvalStatus, BTG);
                    })
                    .forEach(paper -> {
                        paper.setApprovalStatus(targetStatus.getValue());
                        BaseBuildEntityUtil.buildUpdate(paper);
                        papersNeedUpdate.add(paper);
                    });
            if (!papersNeedUpdate.isEmpty()) {
                mapper.updateApprovalStatus(papersNeedUpdate);
            }

            return;
        }
        // 整理"日报ID-条目Set"
        dailyPaperEntryMapper.selectList(
                new QueryWrapper<CostTaskDailyPaperEntry>().in(
                        "daily_paper_id",
                        BaseEntityUtils.mapToIdList(dailyPapers.values())
                )
        ).forEach(entry -> entries.put(entry.getDailyPaperId(), entry));
        if (entries.isEmpty()) {
            return;
        }
        // 遍历"日报ID-条目Set"，更新所需
        for (Long dailyPaperId : entries.keySet()) {
            dailyPaper = dailyPapers.get(dailyPaperId);
            needUpdate = entries.get(dailyPaperId)
                    .stream()
                    .allMatch(entry -> targetStatus.getValue().equals(entry.getApprovalStatus()))
                    &&
                    !targetStatus.getValue().equals(dailyPaper.getApprovalStatus());
            if (needUpdate) {
                dailyPaper.setApprovalStatus(targetStatus.getValue());
                BaseBuildEntityUtil.buildUpdate(dailyPaper);
                papersNeedUpdate.add(dailyPaper);
            }
        }
        if (!papersNeedUpdate.isEmpty()) {
            mapper.updateApprovalStatus(papersNeedUpdate);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoSubmitJob(String now) {
        LocalDate lastDay = getNowDate(now).minusDays(1);
        List<SysUserPmsCqVO> users = new ArrayList<>();
        R<List<SysUserOutVO>> userListByMultiParameterPms = centerUserService.getUserListByMultiParameterPms(new UserPmsDTO());
        if (userListByMultiParameterPms.getCode() != CommonConstants.SUCCESS) {
            throw new ServiceException("用户服务调用异常");
        }
        Map<Long, Roster> userIdRosterMap = rosterMapper.findUserIdMap(null);
        Optional<List<SysUserOutVO>> sysUserOutVOList = userListByMultiParameterPms.unpack();
        if (sysUserOutVOList.isPresent() && !sysUserOutVOList.get().isEmpty()) {
            for (SysUserOutVO sysUserOutVO : sysUserOutVOList.get()) {
                Roster roster = userIdRosterMap.get(sysUserOutVO.getUserId());
                SysUserPmsCqVO sysUserPmsCqVO;
                LocalDate startDate;
                Long deptId;

                if (roster == null) {
                    continue;
                }
                startDate = roster.getStartDate();
                deptId = roster.getDeptId();
                if ("1".equals(sysUserOutVO.getStatus()) || deptId == null || startDate == null || lastDay.isBefore(startDate)) {
                    /*
                     * 两种情况下，不生成该用户前一天的日报：
                     * 1. 用户已禁用（status=1）
                     * 2. 用户入职时间在前一天之后（用户今天才入职，所以不生成昨天的日报）
                     * 此外，对于找不到花名册数据的用户，默认给TA生成日报
                     */
                    continue;
                }
                sysUserPmsCqVO = SysUserPmsCqVO.of(sysUserOutVO, null);
                sysUserPmsCqVO.setName(sysUserOutVO.getUsername());
                sysUserPmsCqVO.setJobName(com.google.common.base.Strings.nullToEmpty(roster.getJob()));
                sysUserPmsCqVO.setDeptId(deptId);
                users.add(sysUserPmsCqVO);
            }
        }
        if (users.isEmpty()) {
            // 没有需要生成日报的用户
            return;
        }

        List<CostTaskDailyPaper> paperNeedUpdate;
        List<CostTaskDailyPaper> paperNeedInsert;
        List<CostTaskDailyPaperEntry> entryNeedUpdate;
        // <userId, obj>
        Map<Long, CostTaskDailyPaper> papers;

        paperNeedUpdate = Lists.newArrayListWithCapacity(users.size());
        entryNeedUpdate = Lists.newArrayListWithExpectedSize(users.size() * 2);
        paperNeedInsert = Lists.newArrayListWithCapacity(users.size());
        papers = mapper.findBySubmissionDateAndUserIds(
                        lastDay,
                        com.gok.pboot.pms.Util.CollectionUtils.extractToList(users, "userId")
                )
                .stream()
                .collect(Collectors.toMap(CostTaskDailyPaper::getUserId, p -> p));
        users.forEach(user -> {
            Long userId = user.getUserId();
            CostTaskDailyPaper paper = papers.get(userId);
            Roster roster = userIdRosterMap.get(userId);

            if (paper == null) {
                paper = createBlankDailyPaper(user, roster, lastDay);
                paper.setCreator("定时任务");
                paperNeedInsert.add(paper);
            }
        });
        if (!paperNeedInsert.isEmpty()) {
            mapper.batchSave(paperNeedInsert);
        }
        if (!entryNeedUpdate.isEmpty()) {
            dailyPaperEntryMapper.batchUpdate(entryNeedUpdate);
            updateApprovalStatusIfNeeded(
                    CollectionUtils.extractToList(paperNeedUpdate, "id"),
                    DSH
            );
        }
        // 清理无用日报
        mapper.cleanUpUseless();
    }

    private LocalDate getNowDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return LocalDate.now();
        }

        try {
            return LocalDateTimeUtil.parseDate(dateStr, "yyyy-MM-dd");
        } catch (DateTimeParseException e) {
            return LocalDate.now();
        }
    }

    private CostTaskDailyPaper createBlankDailyPaper(SysUserPmsCqVO user, Roster roster, LocalDate submissionDate) {
        return BaseBuildEntityUtil.buildInsert(
                new CostTaskDailyPaper()
                        .setUserId(user.getUserId())
                        .setUserStatus(
                                roster == null ? PersonnelStatusEnum.ZS.getValue() :
                                        PersonnelStatusEnum.getByEmployeeStatusEnum(EnumUtils.getEnumByValue(
                                                EmployeeStatusEnum.class,
                                                roster.getEmployeeStatus()
                                        )).getValue()
                        )
                        .setUserDeptId(user.getDeptId())
                        .setSubmissionDate(submissionDate)
                        .setWorkday(holidayMapper.exists(submissionDate) ? BaseConstants.NO : BaseConstants.YES)
                        .setFillingState(NORMAL.getValue())
                        .setApprovalStatus(WTB.getValue())
                        .setProjectCount(0)
                        .setTaskCount(0)
                        .setDailyHourCount(BigDecimal.ZERO)
                        .setAddedHourCount(BigDecimal.ZERO)
        );
    }

    @Override
    public Page<CostTaskDailyPaperAbnormalVO> findAbnormalPage(PageRequest pageRequest, CostTaskDailyPaperAbnormalDTO dto) {
        Page<CostTaskDailyPaperAbnormalVO> page = mapper.findAbnormalList(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), dto);
        processAbnormalList(page.getRecords());
        return page;
    }

    @Override
    public List<CostTaskDailyPaperAbnormalVO> exportAbnormalPaper(CostTaskDailyPaperAbnormalDTO dto) {
        List<CostTaskDailyPaperAbnormalVO> list = mapper.findAbnormalList(dto);
        processAbnormalList(list);
        return list;
    }

    /**
     * 处理异常日报列表，添加用户名称和状态名称
     *
     * @param list 异常日报列表
     */
    private void processAbnormalList(List<CostTaskDailyPaperAbnormalVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 查询工单类别
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        for (CostTaskDailyPaperAbnormalVO vo : list) {
            // 工单类别名称
            vo.setTaskCategoryTxt(costTaskCategoryMap.getOrDefault(vo.getTaskCategory(), new CostTaskCategoryManagement()).getTaskCategoryName());
            // 任务状态名称
            vo.setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, vo.getTaskStatus()));
            // 工单异常类型
            vo.setAbnormalTypeTxt(EnumUtils.getNameByValue(TaskAbnormalTypeEnum.class, vo.getAbnormalType()));
        }
    }

    @Override
    public void sendAbnormalMsg(CostTaskDailyPaperAbnormalDTO dto) {
        // 查询异常工时数据
        List<CostTaskDailyPaperAbnormalVO> list = mapper.findAbnormalList(dto);
        Map<Integer, Map<Long, List<CostTaskDailyPaperAbnormalVO>>> abnormalVoMap = list.stream()
                .filter(vo -> vo.getAbnormalType() != null && vo.getReviewerId() != null)
                .collect(Collectors.groupingBy(
                        CostTaskDailyPaperAbnormalVO::getAbnormalType,
                        Collectors.groupingBy(CostTaskDailyPaperAbnormalVO::getReviewerId)
                ));
        List<BaseSendMsgDTO> noticeMsgList = new ArrayList<>();
        abnormalVoMap.forEach((abnormalType, voMap) -> {
            TaskAbnormalTypeEnum abnormalTypeEnum = EnumUtils.getEnumByValue(TaskAbnormalTypeEnum.class, abnormalType);
            if (abnormalTypeEnum == null) {
                return;
            }
            voMap.forEach((reviewerId, voList) -> {
                if (CollUtil.isEmpty(voList)) {
                    return;
                }
                BaseSendMsgDTO baseSendMsgDTO;
                switch (abnormalTypeEnum) {
                    case UN_REVIEWED:
                         baseSendMsgDTO = buildUnReviewedMsgDTO(voList);
                        break;
                    case UN_EVALUATED:
                        baseSendMsgDTO = buildUnEvaluatedMsgDTO(voList);
                        break;
                    default:
                        return;
                }
                noticeMsgList.add(baseSendMsgDTO);
            });
        });
        bcpMessageService.batchSendMsg(noticeMsgList);
    }

    private static BaseSendMsgDTO buildUnEvaluatedMsgDTO(List<CostTaskDailyPaperAbnormalVO> voList) {
        // 根据异常类型设置不同的消息内容
        String content = "您好，您目前存在%d个未评价的工单，请及时评价～";
        String title = "【工单未评价提醒】";
        String url = "/view-businesses/cooperate-office/work-order-evaluation-manage/work-order-evaluation/index";
        // 根据异常类型设置不同的消息内容
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(String.format(content, voList.size()))
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath(url)
                .populateSender()
                .toOneTarget(voList.get(0).getReviewerId(), voList.get(0).getManagerName());
    }

    private static BaseSendMsgDTO buildUnReviewedMsgDTO(List<CostTaskDailyPaperAbnormalVO> voList) {
        BigDecimal totalHours = voList.stream()
                .map(CostTaskDailyPaperAbnormalVO::getPendingReviewHours)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String url = "view-businesses/cooperate-office/work-order-review/work-order-time-review/index";
        String content = String.format("您好，您目前存在%d个未审核的售前工单，共%.2f工时，请及时审核～", voList.size(), totalHours);
        final String title = "【工单工时未审核提醒】";
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath(url)
                .populateSender()
                .toOneTarget(voList.get(0).getReviewerId(), voList.get(0).getReviewer());
    }

    @Override
    public void taskSendAbnormalMsg(TaskAbnormalTypeEnum abnormalTypeEnum) {
        CostTaskDailyPaperAbnormalDTO abnormalDTO = new CostTaskDailyPaperAbnormalDTO().setAbnormalType(abnormalTypeEnum.getValue());
        sendAbnormalMsg(abnormalDTO);
    }

    /**
     * 根据工单ID获取日报详情
     *
     * @param pageRequest    分页参数
     * @param taskId         工单ID
     * @param approvalStatus approvalStatus
     * @return 日报详情分页数据
     */
    @Override
    public Page<TaskDailyPaperDetailVO> getDailyDetailsByTaskId(PageRequest pageRequest, Long taskId, Integer approvalStatus) {
        if (taskId == null) {
            throw new BusinessException("工单ID不能为空");
        }
        Page<TaskDailyPaperDetailVO> page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        dailyPaperEntryMapper.findDailyDetailsByTaskId(page, taskId,approvalStatus);
        List<TaskDailyPaperDetailVO> records = page.getRecords();
        List<LocalDate> dates = records.stream().map(TaskDailyPaperDetailVO::getSubmissionDate).collect(Collectors.toList());
        Map<LocalDate, Integer> holidayMap = getHolidayMap(dates);
        records.forEach(vo -> vo.setHolidayType(holidayMap.get(vo.getSubmissionDate())));
        page.setRecords(records);
        return page;
    }

    private Map<LocalDate, Integer> getHolidayMap(List<LocalDate> dates) {
        if (CollUtil.isEmpty(dates)) {
            return Collections.emptyMap();
        }
        return holidayMapper.findBatchHolidayType(dates).stream()
                .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType));
    }

    @Override
    public Page<CostSupportTaskApprovalVO> findSupportTaskApprovalPage(PageRequest pageRequest, CostSupportTaskApprovalDTO dto) {
        List<CostSupportTaskApprovalDetailVO> approvalVOList = new ArrayList<>();
        //查询用户需审核的工单
        List<CostDeliverTask> allTasks = buildAllApprovalTask();
        dto.setTaskIds(allTasks.stream().map(CostDeliverTask::getId).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(dto.getTaskIds())){
            approvalVOList = mapper.findSupportTaskApprovalList(dto);
        }
        List<CostSupportTaskApprovalVO> resultList = new ArrayList<>();
        if (CollUtil.isNotEmpty(approvalVOList)){
            //查询项目所有售前工单一级工单已确认成本总和
            List<CostDeliverTask> subTaskList = costDeliverTaskService.lambdaQuery()
                    .eq(CostDeliverTask::getTaskLevel, NumberUtils.INTEGER_ONE)
                    .eq(CostDeliverTask::getTaskType,ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                    .in(CostDeliverTask::getProjectId,approvalVOList.stream().map(CostSupportTaskApprovalDetailVO::getProjectId).collect(Collectors.toList())).list()
                    .stream().map(CostDeliverTask::decrypt)
                    .collect(Collectors.toList());
            Table<Long, Long, BigDecimal> confirmedBudgetTable = HashBasedTable.create();
            subTaskList.forEach(a -> {
                Long projectId = a.getProjectId();
                Long accountOaId = a.getAccountOaId();
                BigDecimal actualLaborCost = new BigDecimal(StrUtil.isNotEmpty(a.getActualLaborCost()) ? a.getActualLaborCost() : "0");
                BigDecimal confirmedBudget = confirmedBudgetTable.get(projectId, accountOaId);
                if (confirmedBudget == null) {
                    confirmedBudgetTable.put(projectId, accountOaId, actualLaborCost);
                } else {
                    BigDecimal add = confirmedBudget.add(actualLaborCost);
                    confirmedBudgetTable.put(projectId, accountOaId, add);
                }
            });

            approvalVOList.stream().forEach(vo -> {
                vo.setPendingEstimatedCost(AESEncryptor.justDecrypt(vo.getPendingEstimatedCost()));
            });
            Map<Long, List<CostSupportTaskApprovalDetailVO>> approvalVOMap = approvalVOList.stream()
                    .collect(Collectors.groupingBy(CostSupportTaskApprovalDetailVO::getProjectId));

            resultList = approvalVOMap.keySet().stream().map(projectId -> {
                List<CostSupportTaskApprovalDetailVO> costSupportTaskApprovalVOList = approvalVOMap.get(projectId);

                BigDecimal normalHours = costSupportTaskApprovalVOList.stream()
                        .map(CostSupportTaskApprovalDetailVO::getNormalHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal restOvertimeHours = costSupportTaskApprovalVOList.stream()
                        .map(CostSupportTaskApprovalDetailVO::getRestOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal workOvertimeHours = costSupportTaskApprovalVOList.stream()
                        .map(CostSupportTaskApprovalDetailVO::getWorkOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal holidayOvertimeHours = costSupportTaskApprovalVOList.stream()
                        .map(CostSupportTaskApprovalDetailVO::getHolidayOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                String pendingEstimatedCost = costSupportTaskApprovalVOList.stream()
                        .map(CostSupportTaskApprovalDetailVO::getPendingEstimatedCost)
                        .filter(StrUtil::isNotEmpty)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
                BigDecimal usedBudget = confirmedBudgetTable.get(projectId, xmglfysqrgOaId);
                return CostSupportTaskApprovalVO.builder()
                        .projectId(projectId)
                        .projectName(costSupportTaskApprovalVOList.get(0).getProjectName())
                        .pendingCount(costSupportTaskApprovalVOList.size())
                        .normalHours(normalHours)
                        .workOvertimeHours(workOvertimeHours)
                        .holidayOvertimeHours(holidayOvertimeHours)
                        .restOvertimeHours(restOvertimeHours)
                        .usedBudget(null!=usedBudget?usedBudget:BigDecimal.ZERO)
                        .pendingEstimatedCost(pendingEstimatedCost)
                        .build();
            }).collect(Collectors.toList());
        }

        return PageUtils.page(resultList, pageRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> changeApprovalStatus(CostChangeApprovalStatusDTO dto) {
        CostTaskDailyPaperEntry entry = dailyPaperEntryMapper.selectById(dto.getId());
        if (null == entry) {
            return ApiResult.failure("数据异常：未找到日报数据，请联系管理员！");
        }
        //是否已归档
        LocalDate submissionDate = entry.getSubmissionDate();
        Filing filing = filingMapper.getFilingByDate(submissionDate);
        if (Optional.ofNullable(filing).isPresent()
                &&filing.getFiled()!=null
                &&FilingTypeEnum.YGD.getValue().equals(filing.getFiled())) {
            return ApiResult.failure("当前填报时间内存在归档日期，请重新选择或联系管理员！");
        }
        // 未通过时，校验未通过原因是否为空
        Integer approvalStatus = dto.getApprovalStatus();
        if (BTG.getValue().equals(approvalStatus) && StringUtils.isBlank(dto.getApprovalReason())) {
            return ApiResult.failure("请输入未通过原因！");
        }
        entry.decrypt();
        CostDeliverTask task = costDeliverTaskService.getById(entry.getTaskId());
        if (YTG.getValue().equals(approvalStatus)) {
            StringBuilder errorAll = new StringBuilder();
            verifyActualLaborCost(task, entry,errorAll);
            if (errorAll.length()>0){
                return ApiResult.failure(errorAll.toString()+"对应的成本科目预算将超支，请联系售前经理处理～");
            }
        }
        PigxUser user = getUser();
        dto.setApprovalName(user.getName());
        dto.setApprovalId(user.getId());
        dto.setMtime(new Timestamp(System.currentTimeMillis()));

        dailyPaperEntryMapper.updateApprovalStatusById(dto);
        //根据日报id集合 更新外层日报状态
        List<Long> dailyPaperIds = new ArrayList<>();
        dailyPaperIds.add(entry.getDailyPaperId());
        updateApprovalStatusIfNeeded(dailyPaperIds,ApprovalStatusEnum.getApprovalStatusEnum(approvalStatus));
        //更新售前工单实际人工成本
        if (YTG.getValue().equals(approvalStatus) || YTH.getValue().equals(approvalStatus)) {
            List<CostTaskDailyPaperEntry> allPaperEntryList = dailyPaperEntryMapper.selectList(Wrappers.<CostTaskDailyPaperEntry>lambdaQuery()
                .eq(CostTaskDailyPaperEntry::getTaskId, task.getId())
                .eq(CostTaskDailyPaperEntry::getApprovalStatus, YTG.getValue())
            ).stream().map(CostTaskDailyPaperEntry::decrypt).collect(Collectors.toList());
            Map<Long, BigDecimal> actualLaborCostMap = new HashMap<>();
            actualLaborCostMap.put(entry.getTaskId(), new BigDecimal(StrUtil.isNotEmpty(entry.getActualLaborCost()) ? entry.getActualLaborCost() : "0"));
            List<CostDeliverTask> allTaskListByProject = costDeliverTaskService.lambdaQuery()
                    .eq(CostDeliverTask::getProjectId, task.getProjectId()).list()
                    .stream().map(CostDeliverTask::decrypt)
                    .collect(Collectors.toList());
            batchUpdateTaskActualLaborCost(allPaperEntryList,actualLaborCostMap,allTaskListByProject);
        }
        return ApiResult.success("审核状态更新成功！");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ApiResult<String> batchApproval(String dailyPaperEntryIdStr) {
        if (StrUtil.isEmptyIfStr(dailyPaperEntryIdStr)) {
            return ApiResult.failure(NO_DAILY_PAPER_TO_BE_REVIEWED);
        }
        List<Long> dailyPaperEntryIds = Arrays.stream(dailyPaperEntryIdStr.split(","))
                .map(Long::parseLong).collect(Collectors.toList());
        if (dailyPaperEntryIds.isEmpty()){
            return ApiResult.failure(NO_DAILY_PAPER_TO_BE_REVIEWED);
        }
        // 查询日报及其对应填报日期的归档状态
        List<DailyPaperEntryFilingDTO> filing = dailyPaperEntryMapper.selectFilingByDailyPaperIds(dailyPaperEntryIds);

        // 区分可审核的日报和对应日期已归档的日期
        List<Long> auditableIds = new ArrayList<>();
        List<Long> dailyPaperIds = new ArrayList<>();
        Set<String> filingDate = new HashSet<>();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
        //批量审核总工时统计
        AtomicReference<BigDecimal> totalHours = new AtomicReference<>(BigDecimal.ZERO);
        filing.forEach(f -> {
            if (FilingTypeEnum.WGD.getValue().equals(f.getFiled())) {
                auditableIds.add(f.getDailyPaperEntryId());
                dailyPaperIds.add(f.getDailyPaperId());
                totalHours.set(totalHours.get().add(f.getNormalHours()).add(f.getAddedHours()));
            } else {
                String date = dtf.format(f.getSubmissionDate());
                filingDate.add(date);
            }
        });
        try {
            if (CollectionUtils.isNotEmpty(auditableIds)) {
                List<CostTaskDailyPaperEntry> paperEntryList = dailyPaperEntryMapper.selectList(Wrappers.<CostTaskDailyPaperEntry>lambdaQuery()
                        .in(CostTaskDailyPaperEntry::getId, auditableIds))
                        .stream().map(CostTaskDailyPaperEntry::decrypt).collect(Collectors.toList());

                // 校验实际人工成本是否超支
                //查询批量通过的工时的实际人工成本总和
                Map<Long, BigDecimal> actualLaborCostMap = new HashMap<>();
                paperEntryList.forEach(a -> {
                    Long taskId = a.getTaskId();
                    BigDecimal actualLaborCost = new BigDecimal(StrUtil.isNotEmpty(a.getActualLaborCost()) ? a.getActualLaborCost() : "0");
                    BigDecimal actualLaborCostSum = actualLaborCostMap.get(taskId);
                    if (actualLaborCostSum == null) {
                        actualLaborCostMap.put(taskId, actualLaborCost);
                    } else {
                        BigDecimal add = actualLaborCostSum.add(actualLaborCost);
                        actualLaborCostMap.put(taskId, add);
                    }
                });
                List<CostDeliverTask> allTaskListByProject = costDeliverTaskService.lambdaQuery()
                        .eq(CostDeliverTask::getProjectId, paperEntryList.get(NumberUtils.INTEGER_ZERO).getProjectId()).list()
                        .stream().map(CostDeliverTask::decrypt)
                        .collect(Collectors.toList());

                StrBuilder error = batchVerifyActualLaborCost(actualLaborCostMap,allTaskListByProject);
                if (error.length()>0){
                    return ApiResult.failure(error.toString()+"对应的成本科目预算将超支，请联系售前经理处理～");
                }
                //批量审核
                ChangeApprovalStatusDTO changeApprovalStatusDTO = new ChangeApprovalStatusDTO();
                changeApprovalStatusDTO.setApprovalName(SecurityUtils.getUser().getName());
                changeApprovalStatusDTO.setApprovalId(SecurityUtils.getUser().getId());
                changeApprovalStatusDTO.setMtime(new Timestamp(System.currentTimeMillis()));
                dailyPaperEntryMapper.batchApproval(auditableIds, changeApprovalStatusDTO);

                //根据日报id集合 更新外层日报状态
                updateApprovalStatusIfNeeded(dailyPaperIds, ApprovalStatusEnum.YTG);

                List<CostTaskDailyPaperEntry> ytgPaperEntryList = dailyPaperEntryMapper.selectList(Wrappers.<CostTaskDailyPaperEntry>lambdaQuery()
                        .in(CostTaskDailyPaperEntry::getTaskId, actualLaborCostMap.keySet())
                        .eq(CostTaskDailyPaperEntry::getApprovalStatus, YTG.getValue()))
                        .stream().map(CostTaskDailyPaperEntry::decrypt).collect(Collectors.toList());

                //更新售前工单实际人工成本
                batchUpdateTaskActualLaborCost(ytgPaperEntryList,actualLaborCostMap,allTaskListByProject);

            }
        } catch (Exception e) {
            log.error("工时审核（项目维度）一键审批接口报错", e);
            return ApiResult.failure("审核异常，请联系管理员");
        }

        if (CollectionUtils.isNotEmpty(filingDate)) {
            StringBuilder msg = new StringBuilder();
            for (String date : filingDate) {
                msg.append(date).append("月份 ");
            }
            msg.append("月份工时数据已归档，请联系管理员，未归档数据已审核成功！");
            return ApiResult.success(null, msg.toString());
        }
        return ApiResult.success("一键审核成功！");
    }


    private StrBuilder batchVerifyActualLaborCost(Map<Long, BigDecimal> actualLaborCostMap,
                                                  List<CostDeliverTask> allTaskListByProject) {
        StrBuilder error = new StrBuilder();
        List<CostDeliverTask> taskList = allTaskListByProject.stream().filter(f->actualLaborCostMap.keySet().contains(f.getId()))
                .collect(Collectors.toList());
        //查询项目所有一级工单已确认成本总和
        List<CostDeliverTask> subTaskList = allTaskListByProject.stream()
                .filter(e -> e.getTaskLevel() == 1 && CostTaskStatusEnum.YWC.getValue().equals(e.getTaskStatus()))
                .map(CostDeliverTask::decrypt).collect(Collectors.toList());
        Table<Long, Integer, BigDecimal> confirmedBudgetTable = HashBasedTable.create();
        subTaskList.forEach(a -> {
            Integer taxRate = a.getTaxRate();
            Long accountOaId = a.getAccountOaId();
            BigDecimal actualLaborCost = new BigDecimal(StrUtil.isNotEmpty(a.getActualLaborCost()) ? a.getActualLaborCost() : "0");
            BigDecimal confirmedBudget = confirmedBudgetTable.get(accountOaId, taxRate);
            if (confirmedBudget == null) {
                confirmedBudgetTable.put(accountOaId, taxRate, actualLaborCost);
            } else {
                BigDecimal add = confirmedBudget.add(actualLaborCost);
                confirmedBudgetTable.put(accountOaId, taxRate, add);
            }
        });
        //获取已确认人工成本预算
        taskList.forEach(task -> {
            DeliverCostBudgetListVO matchedBudget = getCostBudgetVO(task);
            if (matchedBudget == null) {
                return;
            }
            BigDecimal actualLaborCostNum = actualLaborCostMap.get(task.getId()) != null ? actualLaborCostMap.get(task.getId()) : BigDecimal.ZERO;
            BigDecimal confirmedBudget = confirmedBudgetTable.get(task.getAccountOaId(), task.getTaxRate()) != null ? confirmedBudgetTable.get(task.getAccountOaId(), task.getTaxRate()) : BigDecimal.ZERO;
            // 检查是否超出预算
            if (actualLaborCostNum.add(confirmedBudget).compareTo(matchedBudget.getBudgetAmountIncludedTax()) > 0) {
                error.append("【").append(task.getTaskName()).append("】");
            }
        });
            return error;
    }


    @Override
    public CostTaskDailyDetailVO taskDailyPaperPage(PageRequest pageRequest, Long taskId, Integer approvalStatus) {
        CostViewCorroborationVO costViewVO = costDeliverTaskService.taskViewCorroboration(taskId);
        if (costViewVO == null) {
            throw new ServiceException("任务不存在~");
        }
        Page<TaskDailyPaperDetailVO> page = getDailyDetailsByTaskId(pageRequest, taskId, approvalStatus);
        return new CostTaskDailyDetailVO().setTaskId(taskId)
                .setManagerId(costViewVO.getManagerId())
                .setManagerName(costViewVO.getManagerName())
                .setApprovalUserId(costViewVO.getHigherManagerId())
                .setApprovalUserName(costViewVO.getHigherManagerName())
                .setDailyPaperDetailPage(page);
    }

    /**
     * 获取当前用户信息
     *
     * @return {@link PigxUser }
     */
    private static PigxUser getUser() {
        PigxUser user = UserUtils.getUser();
        if (user == null) {
            throw new ValidationException("无操作权限");
        }
        return user;
    }

    @Override
    public CostTaskDailyPaperApprovalVO findTaskDailyPaperApprovalList(PageRequest pageRequest,CostSupportTaskApprovalDTO dto) {
        //查询用户需审核的工单
        List<CostTaskDailyPaperApprovalDetailVO> detailVOList = new ArrayList<>();
        List<CostDeliverTask> allTasks = buildApprovalTask(dto.getProjectId());
        if (CollectionUtils.isNotEmpty(allTasks)) {
            dto.setTaskIds(allTasks.stream().map(CostDeliverTask::getId).collect(Collectors.toList()));
            detailVOList = mapper.findApprovalDetailList(dto);
        }
        Page<CostTaskDailyPaperApprovalDetailVO> page = PageUtils.page(detailVOList, pageRequest);
        if (CollUtil.isEmpty(detailVOList)){
            return CostTaskDailyPaperApprovalVO.builder()
                    .pendingCount(0)
                    .totalHours(BigDecimal.ZERO)
                    .totalNormalHours(BigDecimal.ZERO)
                    .totalOvertimeHours(BigDecimal.ZERO)
                    .totalWorkOvertimeHours(BigDecimal.ZERO)
                    .totalRestOvertimeHours(BigDecimal.ZERO)
                    .totalHolidayOvertimeHours(BigDecimal.ZERO)
                    .detailsPage(page)
                    .build();
        }
        Set<Long> userIds = new HashSet<>();
        Set<LocalDate> submissionDates = new HashSet<>();
        // 查询工单类别
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        detailVOList.forEach(e -> {
            userIds.add(e.getUserId());
            submissionDates.add(e.getSubmissionDate());
        });
        Table<Long, LocalDate, List<CostTomorrowPlanPaperEntry>> tomorrowPlanTable = findLastDayPlanEntries(userIds, submissionDates);
        detailVOList.stream().forEach(detail -> {
            detail.setTaskCategoryTxt(costTaskCategoryMap.getOrDefault(detail.getTaskCategory(), new CostTaskCategoryManagement()).getTaskCategoryName());
            detail.setApprovalStatusTxt(EnumUtils.getNameByValue(ApprovalStatusEnum.class, detail.getApprovalStatus()));
            detail.setFillingStateTxt(EnumUtils.getNameByValue(DailyPaperFillingStateEnum.class, detail.getFillingState()));
            detail.setWeekDay(DailyPaperDateUtils.asDateString(detail.getSubmissionDate()));
            List<CostTomorrowPlanPaperEntry> lastDayPlans = ObjectUtils.defaultIfNull(
                    tomorrowPlanTable.get(detail.getUserId(), detail.getSubmissionDate()),
                    ImmutableList.of()
            );
            Map<Long, String> taskIdAndLastDayPlanDescMap = lastDayPlans.isEmpty() ?
                    ImmutableMap.of() :
                    lastDayPlans.stream().collect(Collectors.toMap(
                            CostTomorrowPlanPaperEntry::getTaskId, CostTomorrowPlanPaperEntry::getDescription)
                    );
            detail.setYesterdayPlan(taskIdAndLastDayPlanDescMap.getOrDefault(detail.getTaskId(), StringUtils.EMPTY));

        });
        BigDecimal totalNormalHours = detailVOList.stream()
                .map(CostTaskDailyPaperApprovalDetailVO::getNormalHours)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalWorkOvertimeHours = detailVOList.stream()
                .map(CostTaskDailyPaperApprovalDetailVO::getWorkOvertimeHours)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalRestOvertimeHours = detailVOList.stream()
                .map(CostTaskDailyPaperApprovalDetailVO::getRestOvertimeHours)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalHolidayOvertimeHours = detailVOList.stream()
                .map(CostTaskDailyPaperApprovalDetailVO::getHolidayOvertimeHours)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalOvertimeHours = totalWorkOvertimeHours.add(totalRestOvertimeHours).add(totalHolidayOvertimeHours);
        BigDecimal totalHours = totalNormalHours.add(totalOvertimeHours);
        return CostTaskDailyPaperApprovalVO.builder()
                .pendingCount(detailVOList.size())
                .totalHours(totalHours)
                .totalNormalHours(totalNormalHours)
                .totalOvertimeHours(totalOvertimeHours)
                .totalWorkOvertimeHours(totalWorkOvertimeHours)
                .totalRestOvertimeHours(totalRestOvertimeHours)
                .totalHolidayOvertimeHours(totalHolidayOvertimeHours)
                .detailsPage(page)
                .build();
    }

    /**
     * 根据日报条目集合，查询昨日计划
     * @return 用户id-日报条目提交日期-昨日计划
     */
    private Table<Long, LocalDate, List<CostTomorrowPlanPaperEntry>> findLastDayPlanEntries(Set<Long> userIds,Set<LocalDate>submissionDates) {
        Table<Long, LocalDate, List<CostTomorrowPlanPaperEntry>> result = HashBasedTable.create();
        List<TomorrowPlanPaperVo> resultList = costTomorrowPlanPaperEntryMapper.findBySubmissionDatesAndUserIds(
                submissionDates, userIds
        );

        resultList.forEach(e -> {
            Long userId = e.getUserId();
            LocalDate selectDate = e.getSelectDate();
            List<CostTomorrowPlanPaperEntry> list = result.get(userId, selectDate);

            if (list == null) {
                list = Lists.newArrayList();
                result.put(userId, selectDate, list);
            }
            CostTomorrowPlanPaperEntry tomorrowPlanPaperEntry = new CostTomorrowPlanPaperEntry();
            BeanUtil.copyProperties(e, tomorrowPlanPaperEntry);
            list.add(tomorrowPlanPaperEntry);
        });
        return result;
    }

    /**
     * 校验工单实际人工成本是否超出预算
     *
     * @param task 工单
     */
    private void verifyActualLaborCost(CostDeliverTask task,CostTaskDailyPaperEntry entry,StringBuilder errorAll) {
        //获取已确认人工成本预算
        DeliverCostBudgetListVO matchedBudget = getCostBudgetVO(task);
        if (matchedBudget == null) {
            return;
        }
        //查询项目所有已完成的一级工单
        List<CostDeliverTask> deliverTaskList = costDeliverTaskService.lambdaQuery()
                .eq(CostDeliverTask::getProjectId, task.getProjectId())
                .eq(CostDeliverTask::getTaskLevel, NumberUtils.INTEGER_ONE)
                .eq(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.YWC.getValue())
                .eq(CostDeliverTask::getAccountOaId, task.getAccountOaId())
                .list().stream()
                .map(CostDeliverTask::decrypt)
                .collect(Collectors.toList());

        // 1. 计算实际人工成本总和
        BigDecimal confirmedBudget = deliverTaskList.stream()
                .map(CostDeliverTask::getActualLaborCost)
                .filter(StrUtil::isNotEmpty)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 检查是否超出预算
        if (new BigDecimal(StrUtil.isNotEmpty(entry.getActualLaborCost())? entry.getActualLaborCost():"0").add(confirmedBudget).compareTo(matchedBudget.getBudgetAmountIncludedTax()) > 0) {
            errorAll.append("【" + task.getTaskName() + "】 ");
        }
    }

    public DeliverCostBudgetListVO getCostBudgetVO(CostDeliverTask task) {
        // 获取项目最新已确认人工成本预算版本信息
        CostManageVersionVO costManageVersionVO = getLatestConfirmedCostManageVersionVO(task.getProjectId());
        if (costManageVersionVO == null || costManageVersionVO.getVersionId() == null) {
            return null;
        }
        // 查询版本对应人工成本预算明细
        List<DeliverCostBudgetListVO> costBudgetList = costManageEstimationResultsMapper.findCostBudgetByVersionId(
                costManageVersionVO.getVersionId(), CostTypeEnum.RGCB.getValue());
        // 找到匹配的预算科目
        return costBudgetList.stream()
                .filter(budget -> Objects.equals(budget.getAccountOaId(), task.getAccountOaId())
                        && Objects.equals(budget.getTaxRate(), task.getTaxRate()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查询项目最新已确认人工成本预算版本信息
     *
     * @param projectId 项目 ID
     * @return {@link CostManageVersionVO }
     */
    private CostManageVersionVO getLatestConfirmedCostManageVersionVO(Long projectId) {
        List<CostManageVersionVO> costManageVersionVOList = costManageVersionService.getHistoryVersions(
                new PageRequest(1, 1000),
                CostManageVersionDTO.builder().projectId(projectId).build()).getRecords();
        return costManageVersionVOList.stream()
                .filter(costManageVersion -> EnumUtils.valueEquals(costManageVersion.getStatus(), CostManageStatusEnum.CONFIRMED))
                .findFirst().orElse(null);
    }


    /**
     * 批量更新工单实际人工成本
     * @param allPaperEntryList 工单下的所有日报条目
     * @param actualLaborCostMap 工单实际人工成本
     * @param allTaskListByProject 项目下所有工单
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateTaskActualLaborCost(List<CostTaskDailyPaperEntry> allPaperEntryList,
                                               Map<Long, BigDecimal> actualLaborCostMap,
                                               List<CostDeliverTask> allTaskListByProject) {
        // 当前项目下所有工单Map
        final Map<Long, CostDeliverTask> allTaskMap = new HashMap<>(allTaskListByProject.size());
        allTaskListByProject.forEach(costDeliverTask -> {
            allTaskMap.put(costDeliverTask.getId(), costDeliverTask);
        });
        List<CostDeliverTask> taskList = allTaskListByProject.stream()
                .filter(f -> actualLaborCostMap.keySet().contains(f.getId())).collect(Collectors.toList());
        // 已通过的工时
        Map<Long, List<CostTaskDailyPaperEntry>> entryMapByTaskId = allPaperEntryList.stream()
                .collect(Collectors.groupingBy(CostTaskDailyPaperEntry::getTaskId));
        // 需要更新的工单列表
        List<CostDeliverTask> updateTaskList = new ArrayList<>();
        taskList.forEach(task -> {
            List<CostTaskDailyPaperEntry> taskPaperEntryList = entryMapByTaskId.get(task.getId());
            BigDecimal totalNormalHours = BigDecimal.ZERO;
            BigDecimal totalWorkOvertimeHours = BigDecimal.ZERO;
            BigDecimal totalRestOvertimeHours = BigDecimal.ZERO;
            BigDecimal totalHolidayOvertimeHours = BigDecimal.ZERO;
            BigDecimal childrenActualLaborCost = BigDecimal.ZERO;
            BigDecimal budgetDiff = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(taskPaperEntryList)){
                 totalNormalHours = taskPaperEntryList.stream()
                        .map(CostTaskDailyPaperEntry::getNormalHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                 totalWorkOvertimeHours = taskPaperEntryList.stream()
                        .map(CostTaskDailyPaperEntry::getWorkOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                 totalRestOvertimeHours = taskPaperEntryList.stream()
                        .map(CostTaskDailyPaperEntry::getRestOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                 totalHolidayOvertimeHours = taskPaperEntryList.stream()
                        .map(CostTaskDailyPaperEntry::getHolidayOvertimeHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                 childrenActualLaborCost = taskPaperEntryList.stream()
                        .map(CostTaskDailyPaperEntry::getActualLaborCost)
                        .filter(StrUtil::isNotEmpty)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                 budgetDiff = new BigDecimal(StrUtil.isNotEmpty(task.getActualLaborCost()) ? task.getActualLaborCost() : "0").subtract(childrenActualLaborCost);
            }
            // 更新当前父级工单实际人工成本
            task.setActualLaborCost(childrenActualLaborCost.toString());
            task.setNormalHours(totalNormalHours);
            task.setWorkOvertimeHours(totalWorkOvertimeHours);
            task.setRestOvertimeHours(totalRestOvertimeHours);
            task.setHolidayOvertimeHours(totalHolidayOvertimeHours);
            updateTaskList.add(BaseBuildEntityUtil.buildUpdate(task));
            // 如果不是一级工单,向上逐级修改
            Long currentParentId = task.getParentId();
            while (currentParentId != null) {
                CostDeliverTask currentTask = allTaskMap.get(currentParentId);
                if (currentTask != null) {
                    // 非直属上级工单
                    currentTask.setActualLaborCost(new BigDecimal(StrUtil.isNotEmpty(currentTask.getActualLaborCost()) ? currentTask.getActualLaborCost() : "0").subtract(budgetDiff).toString());
                    updateTaskList.add(BaseBuildEntityUtil.buildUpdate(currentTask));
                    currentParentId = currentTask.getParentId();
                } else {
                    break;
                }
            }
        });
        // 批量更新工单实际人工成本
        if (!updateTaskList.isEmpty()) {
            List<CostDeliverTask> distinctList = updateTaskList.stream().distinct().collect(Collectors.toList());
            distinctList.forEach(CostDeliverTask::encrypt);
            costDeliverTaskService.updateBatchById(distinctList);
        }
    }

    /**
     * 查询用户需审核的工单
     * 子级工单的工时审核人为上一级工单负责人，一级工单的工时审核人为售前经理，当一级工单的负责人也是售前经理时，由售前经理的直接上级进行审核
     * @param projectId
     * @return
     */
    private List<CostDeliverTask> buildApprovalTask(Long projectId) {
        PigxUser user = getUser();
        //直属下级
        Map<Long, List<Pair<Long,String>>> userUnderlingMap = rosterService.findUserUnderlingMap(Collections.singleton(user.getId()));
        List<Long> subUserIds = userUnderlingMap.getOrDefault(user.getId(), ListUtil.empty())
                .stream().map(Pair::getFirst).collect(Collectors.toList());
        //判断当前用户是否是工单负责人
        boolean projectHeader = CollectionUtil.isNotEmpty(costDeliverTaskService.lambdaQuery()
                .eq(CostDeliverTask::getManagerId, user.getId())
                .eq(CostDeliverTask::getProjectId, projectId).list());
        List<CostDeliverTask> allTasks = new ArrayList<>();

            CostDeliverTaskDTO request = new CostDeliverTaskDTO();
            request.setProjectId(projectId);
            request.setProjectManagerUserId(user.getId());
            request.setSubUserIds(subUserIds);
            List<CostDeliverTask> managerTasks = costDeliverTaskMapper.findPreManagerTask(request)
                    .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
            allTasks.addAll(managerTasks);

        if (projectHeader) {
            //当前用户是工单负责人，查询任务级别是第2级和第3级的，父级的负责人是当前用户的数据
            CostDeliverTaskDTO query = new CostDeliverTaskDTO();
            query.setProjectId(projectId);
            query.setManagerId(user.getId());
            query.setTaskLevelList(CollUtil.newArrayList(NumberUtils.INTEGER_TWO, TASK_MAX_LEVEL));
            List<CostDeliverTask> headerTasks = costDeliverTaskMapper.findTaskAndParent(query)
                    .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
            allTasks.addAll(headerTasks);
        }
        return allTasks;
    }
    
    @Override
    public Map<Integer, Integer> getAbnormalCount(CostTaskDailyPaperAbnormalDTO dto) {
        List<TaskAbnormalCountDTO> abnormalCount = mapper.findAbnormalCount(dto);
        Map<Integer, Integer> countMap = abnormalCount.stream()
                .filter(e -> e.getAbnormalType() != null)
                .collect(Collectors.toMap(TaskAbnormalCountDTO::getAbnormalType, TaskAbnormalCountDTO::getCount));
        List<Integer> values = CollUtil.newArrayList(TaskAbnormalTypeEnum.UN_REVIEWED.getValue(), TaskAbnormalTypeEnum.UN_EVALUATED.getValue());
        for (Integer value : values) {
            if (!countMap.containsKey(value)) {
                countMap.put(value, 0);
            }
        }
        return countMap;
    }


    /**
     * 查询用户需审核的工单
     * 子级工单的工时审核人为上一级工单负责人，一级工单的工时审核人为售前经理，当一级工单的负责人也是售前经理时，由售前经理的直接上级进行审核
     * @return
     */
    private List<CostDeliverTask> buildAllApprovalTask() {
        PigxUser user = getUser();
        //直属下级
        Map<Long, List<Pair<Long,String>>> userUnderlingMap = rosterService.findUserUnderlingMap(Collections.singleton(user.getId()));
        List<Long> subUserIds = userUnderlingMap.getOrDefault(user.getId(), ListUtil.empty())
                .stream().map(Pair::getFirst).collect(Collectors.toList());
        //判断当前用户是否是工单负责人
        boolean projectHeader = CollectionUtil.isNotEmpty(costDeliverTaskService.lambdaQuery()
                .eq(CostDeliverTask::getManagerId, user.getId()).list());
        List<CostDeliverTask> allTasks = new ArrayList<>();

        CostDeliverTaskDTO request = new CostDeliverTaskDTO();
        request.setProjectManagerUserId(user.getId());
        request.setSubUserIds(subUserIds);
        List<CostDeliverTask> managerTasks = costDeliverTaskMapper.findPreManagerTask(request)
                    .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
        allTasks.addAll(managerTasks);

        if (projectHeader) {
            //当前用户是工单负责人，查询任务级别是第2级和第3级的，父级的负责人是当前用户的数据
            CostDeliverTaskDTO query = new CostDeliverTaskDTO();
            query.setManagerId(user.getId());
            query.setTaskLevelList(CollUtil.newArrayList(NumberUtils.INTEGER_TWO, TASK_MAX_LEVEL));
            List<CostDeliverTask> headerTasks = costDeliverTaskMapper.findTaskAndParent(query)
                    .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
            allTasks.addAll(headerTasks);
        }
        return allTasks;
    }
}