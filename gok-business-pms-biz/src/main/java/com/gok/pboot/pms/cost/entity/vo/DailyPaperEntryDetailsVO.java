package com.gok.pboot.pms.cost.entity.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@Accessors(chain = true)
public class DailyPaperEntryDetailsVO {

    /**
     * 日报ID
     */
    private Long dailyPaperId;

    /**
     * 项目任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 审核状态名称
     */
    private String approvalStatusName;

    /**
     * 填报状态
     * @see com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum
     */
    private Integer fillingState;

    /**
     * 填报状态名称
     */
    private String fillingStateName;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 填报日期带周
     */
    private String submissionDateFormatted;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;
    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 工作内容
     */
    private String description;

    /**
     * 审核不通过原因
     */
    private String approvalReason;

    /**
     * 提交人ID
     */
    private Long userId;

    /**
     * 提交人姓名
     */
    private String userRealName;

    /**
     * 提交人所属部门ID
     */
    private Long userDeptId;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    private Integer workType;

    /**
     * 工时类型名称（0=售前支撑，1=售后交付）
     */
    private String workTypeName;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 实际人工成本
     */
    private String actualLaborCost;
    /**
     * 解密
     *
     * @return {@link CostTaskDailyPaperEntry }
     */
    public DailyPaperEntryDetailsVO decrypt() {
        this.actualLaborCost = AESEncryptor.justDecrypt(this.actualLaborCost);
        return this;
    }

    /**
     * 加密
     *
     * @return {@link CostTaskDailyPaperEntry }
     */
    public DailyPaperEntryDetailsVO encrypt() {
        this.actualLaborCost = AESEncryptor.justEncrypt(this.actualLaborCost);
        return this;
    }
}
