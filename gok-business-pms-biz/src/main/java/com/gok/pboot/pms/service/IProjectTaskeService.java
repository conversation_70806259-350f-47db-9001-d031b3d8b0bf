package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.entity.domain.ProjectTaske;
import com.gok.pboot.pms.entity.dto.ProjectTaskeDto;
import com.gok.pboot.pms.entity.dto.ProjectTaskeUserDto;
import com.gok.pboot.pms.entity.vo.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 项目1任务服务类
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
public interface IProjectTaskeService extends IService<ProjectTaske> {

    /**
     * 获取任务列表
     *
     * @param projectId 项目id
     * @return {@link List}<{@link ProjectTaskeVo}>
     */
    List<ProjectTaskeVo> getByProjectId(Long projectId);

    /**
     * 获取项目概览
     *
     * @param pageRequest 分页请求对象
     * @param request     请求对象
     * @return {@link ProjectOverviewVo}
     */
    ProjectOverviewVo findOverview(PageRequest pageRequest, HttpServletRequest request);

    /**
     * 获取分页列表
     *
     * @param pageRequest 分页请求对象
     * @param request     请求对象
     * @return {@link Page}<{@link ProjectTaskeVo}>
     */
    Page<ProjectTaskeVo> findList(PageRequest pageRequest, HttpServletRequest request);

    /**
     * 获取我的任务
     *
     * @param pageRequest 分页请求对象
     * @param request 请求对象
     * @customParam filter_S_projectName 项目名称
     * @return {@link ApiResult}<{@link Page}<{@link MyTaskVo}>>
     */
    Page<MyTaskVo> findMyTaskList(PageRequest pageRequest, HttpServletRequest request);

    /**
     * 获取分页列表（甘特图）
     *
     * @param pageRequest 分页请求对象
     * @param request     请求对象
     * @return {@link Page}<{@link ProjectTaskeGanttVo}>
     */
    Page<ProjectTaskeGanttVo> findGanttList(PageRequest pageRequest, HttpServletRequest request);

    /**
     * 分页查询任务下的操作记录
     *
     * @param pageRequest 分页对象
     * @param id          任务id
     * @return {@link ApiResult <Page<  Task  >>}
     */
    Page<OperatingRecordPageVO> findOperatingRecordPage(Long id, PageRequest pageRequest);

    /**
     * 获取任务详情
     *
     * @param id 任务id
     * @return {@link ProjectTaskeVo}
     */
    ProjectTaskeVo findOne(Long id);

    /**
     * 获取最新填报日期
     *
     * @param id 任务id
     * @return {@link LocalDate}
     */
    LocalDate findFinalDailyDate(Long id);

    /**
     * 新建项目任务
     *
     * @param request 任务保存请求对象
     * @return {@link Long}
     */
    Long save(ProjectTaskeDto request);

    /**
     * 编辑项目任务
     *
     * @param request 任务更新请求对象
     * @return {@link Long}
     */
    Long update(ProjectTaskeDto request);

    /**
     * 根据任务id删除任务
     *
     * @param id 任务id
     * @return {@link Boolean}
     */
    Boolean deleteById(Long id);

    /**
     * 获取任务参与人列表
     *
     * @param id          任务id
     * @param pageRequest 分页请求
     * @return {@link List}<{@link ProjectTaskeUserVo}>
     */
    Page<ProjectTaskeUserVo> getMembers(Long id, PageRequest pageRequest);

    /**
     * 删除参与人
     *
     * @param id 关联表id
     * @return {@link Boolean}
     */
    Boolean delMember(Long id);

    /**
     * 结束任务
     *
     * @param id         任务id
     * @param finishDate 结束日期
     * @return {@link Long}
     */
    Long finish(Long id, LocalDate finishDate);

    /**
     * 重启任务
     *
     * @param id 任务id
     * @return {@link Long}
     */
    Long restart(Long id);

    /**
     * 添加项目参与人
     *
     * @param id      任务id
     * @param members 项目参与人列表
     * @return {@link Long}
     */
    Long addMember(Long id, List<ProjectTaskeUserDto> members);

    /**
     * 同步创建项目默认任务
     *
     * @return {@link Boolean}
     */
    Boolean syncCreateDefaultTask();

    /**
     * 判断是否是铁三角
     *
     * @param projectId 项目id
     * @param userId    用户id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> isIronTriangle(Long projectId, Long userId);

    /**
     *
     * 是否是铁三角
     *
     * @param projectId 项目id
     * @param userIds   用户id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> isIronTriangle(Long projectId, List<Long> userIds);

    /**
     * 判断是否是操作助理
     *
     * @param projectId 项目id
     * @param userId 用户id
     * @return boolean
     */
    boolean isOperationsAssistant(Long projectId, Long userId);

    /**
     * 判断是否是操作助理
     *
     * @param projectId 项目id
     * @param userIds   用户di列表
     * @return boolean
     */
    boolean isOperationsAssistant(Long projectId, List<Long> userIds);

    /**
     * 判断是否是项目成员
     *
     * @param projectId 项目id
     * @param userIds   用户di列表
     * @return boolean
     */
    boolean isMember(Long projectId, List<Long> userIds);

    /**
     * 判断是否是任务负责人
     *
     * @param userIds    用户id
     * @param projectId 项目id
     * @return boolean
     */
    boolean isLeader(Long projectId, List<Long> userIds);

    /**
     * 任务即将结束通知
     */
    void deadLineNotify();

    /**
     * 项目任务自动结束
     */
    void autoFinish();

    /**
     * 同步旧任务
     */
    void syncOldTask();

    /**
     * 同步旧任务二次修复接口
     */
    void syncOldTaskFix();


    void syncRemoveProjectTask(CostDeliverTask costDeliverTask);

    /**
     * 批量将工单映射为项目任务并自动创建或更新
     *
     * @param costDeliverTaskList 成本交付任务(工单)列表
     */
    void batchSyncProjectTask(List<CostDeliverTask> costDeliverTaskList);


    /**
     * 按工单id 完成任务
     *
     * @param costTaskIds 成本任务 ID
     */
    void finishByCostTaskIds(Collection<Long> costTaskIds);


    void startByCostTaskIds(Collection<Long> costTaskIds);

    /**
     * 同步项目成本任务成员
     *
     * @param projectId 项目id
     */
    void syncProjectCostTaskMember(Long projectId);
}
