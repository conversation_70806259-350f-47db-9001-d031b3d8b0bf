package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 成本交付任务(工单)
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName("cost_deliver_task")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CostDeliverTask extends BeanEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目阶段
     */
    private String stage;

    /**
     * 工单编号
     */
    private String taskNo;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 工单类型（0=售前支撑，1=售后交付）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    /**
     * 工单级别
     */
    private Integer taskLevel;

    /**
     * 工单状态
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskStatusEnum
     */
    private Integer taskStatus;

    /**
     * 工单描述
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String taskDesc;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 成本科目OA ID
     */
    private Long accountOaId;

    /**
     * 成本科目名称
     */
    private String accountName;

    /**
     * 税率OA字典ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer taxRate;

    /**
     * 预算成本
     */
    private String budgetCost;

    /**
     * 拆解类型（0=标准工单，1=总成工单）
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum
     */
    private Integer disassemblyType;

    /**
     * 工单负责人ID
     */
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    private String managerName;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 交付说明
     */
    private String deliverDesc;

    /**
     * 完成佐证文档id，多个逗号隔开
     */
    private String completeFiles;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 实际人工成本
     */
    private String actualLaborCost;

    /**
     * 预计工时
     */
    private BigDecimal estimatedHours;

    /**
     * 产值
     */
    private BigDecimal income;

    /**
     * 退回原因
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String returnReason;

    /**
     * 退回时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime returnTime;

    /**
     * 退回人ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long returnUserId;

    /**
     * 退回人姓名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String returnUserName;

    /**
     * 完成时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime completionTime;

    /**
     * 提交完成时间
     */
    private LocalDateTime submitCompletionTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 工单类别（来自工单类别管理）
     */
    private Integer taskCategory;

    /**
     * 是否为默认配置的工单(1是 0否)
     */
    private Boolean defaultConf;

    /**
     * 是否异常(1是 0否)
     */
    @TableField(exist = false)
    private Boolean abnormal;

    /**
     * 评价状态
     */
    private Integer evaluationStatus;

    /**
     * 已分配产值
     */
    private BigDecimal assignedIncome;

    /**
     * 待结算产值
     */
    private BigDecimal pendingIncome;

    /**
     * 已结算产值
     */
    private BigDecimal settledIncome;

    /**
     * 核算产值
     */
    private BigDecimal calculateIncome;

    /**
     * 解密
     *
     * @return {@link CostDeliverTask }
     */
    public CostDeliverTask decrypt() {
        this.budgetCost = AESEncryptor.justDecrypt(this.budgetCost);
        this.actualLaborCost = AESEncryptor.justDecrypt(this.actualLaborCost);
        return this;
    }

    /**
     * 加密
     *
     * @return {@link CostDeliverTask }
     */
    public CostDeliverTask encrypt() {
        this.budgetCost = AESEncryptor.justEncrypt(this.budgetCost);
        this.actualLaborCost = AESEncryptor.justEncrypt(this.actualLaborCost);
        return this;
    }

    /**
     * 判断工单是否异常
     * 当总成工单的周期含近60天的日期，未拆解的添加异常标识
     *
     * @param task 工单
     * @return 是否异常
     */
    public static boolean isTaskAbnormal(CostDeliverTask task) {
        // 只判断总成工单
        if (!EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
            return false;
        }

        // 只判断待拆解状态
        if (!EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.DCJ)) {
            return false;
        }

        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 计算60天后的日期
        LocalDate sixtyDaysLater = now.plusDays(60);

        // 获取工单的开始日期和结束日期
        LocalDate startDate = task.getStartDate();
        LocalDate endDate = task.getEndDate();

        if (startDate == null || endDate == null) {
            return false;
        }

        // 判断工单周期是否包含近60天的日期
        // 情况1: 开始日期在60天范围内
        // 情况2: 结束日期在60天范围内
        // 情况3: 工单周期包含了60天范围
        return (startDate.isAfter(now) && startDate.isBefore(sixtyDaysLater)) ||
                (endDate.isAfter(now) && endDate.isBefore(sixtyDaysLater)) ||
                (startDate.isBefore(now) && endDate.isAfter(sixtyDaysLater));
    }
}
