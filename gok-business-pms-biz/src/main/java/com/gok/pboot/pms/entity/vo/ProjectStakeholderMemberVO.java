package com.gok.pboot.pms.entity.vo;


import cn.hutool.core.text.StrPool;
import com.alibaba.excel.util.StringUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.enumeration.EmployeeStatusEnum;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;

/**
 * 项目干系人-成员vo
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectStakeholderMemberVO {

    /**
     * ID id
     */
    private Long id;
    /**
     * 成员id
     */
    private Long memberId;

    /**
     * 成员名
     */
    private String memberName;

    /**
     * 部门名
     */
    private String deptName;
    /**
     * 岗位/职位
     */
    private String position;

    /**
     * 角色类型（0=项目销售经理，1=项目售前经理，2=项目经理，3=项目操作助理，4=项目成员）
     * {@link com.gok.pboot.pms.enumeration.RoleTypeEnum}
     */
    private Integer roleType;

    /**
     * 角色类型txt
     */
    private String roleTypeTxt;

    /**
     * 职责
     */
    private String duty;
    /**
     * 备注
     */
    private String remark;

    /**
     * 是否同步OA（0=是，1=否，2=关键角色默认同步OA）
     */
    private Integer syncOaType;

    /**
     * 是否同步txt
     */
    private String syncOaTypeTxt;

    /**
     * 员工状态
     */
    private Integer employeeStatus;

    /**
     * 员工状态txt
     */
    private String employeeStatusTxt;

    public static ProjectStakeholderMemberVO of(ProjectStakeholderMember po, @Nullable Integer employeeStatus) {
        ProjectStakeholderMemberVO result = new ProjectStakeholderMemberVO();
        Integer roleType = po.getRoleType();
        Integer syncOaType = po.getSyncOaType();

        result.setId(po.getId());
        result.setMemberId(po.getMemberId());
        result.setMemberName(po.getMemberName());
        result.setDeptName(po.getDeptName());
        result.setPosition(po.getPosition());
        if (roleType == null) {
            result.setRoleTypeTxt(StringUtils.EMPTY);
        } else {
            result.setRoleType(roleType);
            result.setRoleTypeTxt(EnumUtils.getNameByValue(RoleTypeEnum.class, roleType));
        }
        result.setDuty(po.getDuty());
        result.setRemark(po.getRemark());
        if (syncOaType == null) {
            result.setSyncOaTypeTxt(StrPool.SLASH);
        } else {
            result.setSyncOaType(syncOaType);
            result.setSyncOaTypeTxt(EnumUtils.getNameByValue(YesOrNoEnum.class, syncOaType));
        }
        if (employeeStatus == null) {
            result.setEmployeeStatusTxt(StringUtils.EMPTY);
        } else {
            result.setEmployeeStatus(employeeStatus);
            if (
                    EnumUtils.valueEquals(employeeStatus, EmployeeStatusEnum.RESIGN)
            ) {
                result.setEmployeeStatusTxt("离职");
            } else {
                result.setEmployeeStatusTxt("在职");
            }
        }

        return result;
    }

}
