package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostTaskFiling;
import com.gok.pboot.pms.entity.Filing;
import com.gok.pboot.pms.entity.vo.FilingFindPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工单工时归档数据库操作接口
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Deprecated
@Mapper
public interface CostTaskFilingMapper extends BaseMapper<CostTaskFiling> {


    /**
     * 判断日期是否存在
     */
    boolean exists(@Param("date") LocalDate date);

    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList  实体集合
     */
    void batchSave(@Param("poList") List<Filing> poList);



    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<Filing> list);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);

    /**
     * 批量修改不为空字段
     *
     * @param list id集合
     */
    void updateBatch(@Param("list") List<Long> list);

    void getCancelFileById(@Param("id")Long id,@Param("operator")String operator);

    void getFileById(@Param("id")Long id,@Param("operator")String operator);

    Page<FilingFindPageVO> FilingFindPageVO(Page<Filing> filingPage, @Param("filter") Map<String, Object> filter);

    /**
     * 通过日期获取归档对象
     */
    Filing getFilingByDate(@Param("date")LocalDate submissionDate);

    /**
     * 通过日期列表获取归档对象
     * @param dates 日期列表
     * @return 归档对象列表
     */
    List<Filing> findByDates(@Param("dates") Collection<LocalDate> dates);

    /**
     * ~ 通过日期查询是否归档 ~
     * @param submissionDate 日期
     * @return boolean
     * <AUTHOR>
     * @date 2022/9/6 15:45
     */
    Integer isFiledByDate(LocalDate submissionDate);



    /**
     * 查询最大的归档日期
     * @return boolean
     */
    Filing getMaxFiling();

    /**
     * 根据归档状态查询
     * @param filed 是否归档
     * @return 数据列表
     */
    List<Filing> findByFiled(Integer filed);
} 