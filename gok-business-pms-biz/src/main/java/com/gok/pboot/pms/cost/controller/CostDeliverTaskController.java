package com.gok.pboot.pms.cost.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.annotation.ProjectStatusCheck;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.TaskAbnormalTypeEnum;
import com.gok.pboot.pms.cost.service.ICostDeliverTaskService;
import com.gok.pboot.pms.entity.vo.ProjectWithTasksInDailyPaperEntryUnifyVO;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 交付工单管理
 *
 * <AUTHOR>
 * @date 2025/01/15
 * @menu 交付工单管理
 */
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/costDeliverTask")
public class CostDeliverTaskController {

    private final ICostDeliverTaskService costDeliverTaskService;

    /**
     * 工单头部统计信息
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @GetMapping("/topCountMsg")
    public ApiResult<CostTaskTopCountMsgVO> taskTopCountMsg(@RequestParam(name = "taskType", required = false) Integer taskType) {
        return ApiResult.success(costDeliverTaskService.taskTopCountMsg(taskType), "获取成功");
    }

    /**
     * 通过工单
     *
     * @param id id
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/pass/{id}")
    public ApiResult<String> passTask(@PathVariable("id") Long id,@RequestParam String dailyPaperIds) {
        costDeliverTaskService.passTask(id,dailyPaperIds);
        return ApiResult.success("通过成功");
    }


    /**
     * 退回工单
     *
     * @param id  id
     * @param dto DTO
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/return/{id}")
    public ApiResult<String> returnTask(@PathVariable("id") Long id, @RequestBody @Valid CostTaskReturnReasonDTO dto) {
        costDeliverTaskService.returnTask(id, dto);
        return ApiResult.success("退回成功");
    }

    /**
     * 完成
     *
     * @param id  id
     * @param dto DTO
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/finishCorroboration/{id}")
    public ApiResult<String> taskFinishCorroboration(@PathVariable("id") Long id, @RequestBody @Valid CostFinishCorroborationDTO dto) {
        costDeliverTaskService.taskFinishCorroboration(id, dto);
        return ApiResult.success("提交成功");
    }

    /**
     * 查看佐证
     *
     * @param id id
     * @return {@link ApiResult }<{@link CostViewCorroborationVO }>
     */
    @GetMapping("/viewCorroboration/{id}")
    public ApiResult<CostViewCorroborationVO> taskViewCorroboration(@PathVariable("id") Long id) {
        return ApiResult.success(costDeliverTaskService.taskViewCorroboration(id), "查看成功");
    }

    /**
     * 查看退回信息
     *
     * @param id id
     * @return {@link ApiResult }<{@link CostTaskReturnInfoVO }>
     */
    @GetMapping("/returnInfo/{id}")
    public ApiResult<CostTaskReturnInfoVO> returnInfo(@PathVariable("id") Long id) {
        return ApiResult.success(costDeliverTaskService.returnInfo(id), "查看成功");
    }

    /**
     * 获取子工单分页
     *
     * @param pageRequest 页面请求
     * @param taskId      任务 ID
     * @return {@link ApiResult }<{@link Page }<{@link CostDeliverTaskVO }>>
     */
    @PostMapping("/getChildrenPage/{taskId}")
    public ApiResult<Page<CostDeliverTaskVO>> getChildrenPage(PageRequest pageRequest,
                                                              @PathVariable("taskId") Long taskId,
                                                              @RequestBody JSONObject requestObject) {
        return ApiResult.success(costDeliverTaskService.getChildrenPage(pageRequest, taskId, requestObject));
    }

    /**
     * 批量创建一级工单
     *
     * @param costDeliverTaskAddEditDTOList 成本 交付任务 添加 编辑 dto list
     * @return {@link R }<{@link String }>
     */
    @PutMapping("/batchCreateFirst/{projectId}")
    @ProjectStatusCheck(projectIdField = "#projectId", userIdField = "#costDeliverTaskAddEditDTOList.![managerId]")
    public ApiResult<String> batchCreateFirstDeliverTask(@PathVariable("projectId") Long projectId,
                                                         @RequestBody @Valid List<CostDeliverTaskAddEditDTO> costDeliverTaskAddEditDTOList) {
        // 设置项目ID
        costDeliverTaskService.batchCreateFirstDeliverTask(projectId, costDeliverTaskAddEditDTOList);
        return ApiResult.successMsg("创建成功");
    }

    @GetMapping("/test/{id}")
    public ApiResult<BigDecimal> calculateAssignedIncome(@PathVariable("id") Long id) {
        return ApiResult.success(costDeliverTaskService.calculateAssignedIncome(costDeliverTaskService.getBaseMapper().selectById(id).decrypt()));
    }

    /**
     * 工单管理列表分页查询
     *
     * @param pageRequest 分页请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link CostDeliverTaskVO}>>
     * customParam filter_L_projectId 项目ID
     * customParam filter_I_taskType 工单类型
     */
    @GetMapping("/findPage")
    public ApiResult<Page<CostDeliverTaskVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return ApiResult.success(costDeliverTaskService.findPage(pageRequest, filter));
    }

    /**
     * 通过id查询工单详情
     *
     * @param id 工单ID
     * @return {@link ApiResult }<{@link CostDeliverTaskVO }>
     */
    @GetMapping("/{id}")
    public ApiResult<CostDeliverTaskVO> findById(@PathVariable Long id) {
        return ApiResult.success(costDeliverTaskService.findById(id));
    }

    /**
     * 工单编辑
     *
     * @param costDeliverTaskAddEditDTO 成本 交付任务 添加编辑 DTO
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/edit")
    @ProjectStatusCheck(projectIdField = "#costDeliverTaskAddEditDTO.projectId", userIdField = "managerId")
    public ApiResult<String> editFirstTask(@RequestBody @Valid CostDeliverTaskAddEditDTO costDeliverTaskAddEditDTO) {
        costDeliverTaskService.editFirstDeliverTask(costDeliverTaskAddEditDTO);
        return ApiResult.successMsg("修改成功");
    }

    /**
     * 批量编辑售前支撑工单
     *
     * @param costSupportTaskAddEditDTO 售前支撑工单添加 编辑 dto
     * @return {@link ApiResult }<{@link String }>
     */
    @PutMapping("/batchEditSupportTask")
    @ProjectStatusCheck(projectIdField = "#costSupportTaskAddEditDTO.projectId", userIdField = "managerId")
    public ApiResult<String> batchEditSupportTask(@RequestBody @Valid CostSupportTaskAddEditDTO costSupportTaskAddEditDTO) {
        costDeliverTaskService.batchEditSupportTask(costSupportTaskAddEditDTO);
        return ApiResult.successMsg("修改成功");
    }

    /**
     * 批量创建售前支撑工单
     *
     * @param costSupportTaskAddEditDTOList 成本支持任务添加编辑 dto list
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/batchCreateSupportTask")
    @ProjectStatusCheck(projectIdField = "#costSupportTaskAddEditDTOList[0].projectId", userIdField = "managerId")
    public ApiResult<String> batchCreateSupportTask(@RequestBody @Valid List<CostSupportTaskAddEditDTO> costSupportTaskAddEditDTOList) {
        costDeliverTaskService.batchAddSupportTask(costSupportTaskAddEditDTOList);
        return ApiResult.successMsg("创建成功");
    }


    /**
     * 售后交付工单拆解
     *
     * @param parentId                      父工单 ID
     * @param costDeliverTaskAddEditDTOList 成本 交付任务 添加编辑 DTO list
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/decomposition/{parentId}")
    @ProjectStatusCheck(projectIdField = "#costDeliverTaskAddEditDTOList[0].projectId", userIdField = "costDeliverTaskAddEditDTOList.![managerId]")
    public ApiResult<String> decomposition(@PathVariable("parentId") Long parentId,
                                           @RequestBody @Valid List<CostDeliverTaskAddEditDTO> costDeliverTaskAddEditDTOList) {
        costDeliverTaskService.decomposition(parentId, costDeliverTaskAddEditDTOList);
        return ApiResult.successMsg("操作成功");
    }


    /**
     * 获取售后交付工单拆解草稿
     *
     * @param taskId 工单id
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/decomposition/draft/{taskId}")
    public ApiResult<Object> decompositionDraft(@PathVariable("taskId") Long taskId) {
        return ApiResult.success(costDeliverTaskService.decompositionDraft(taskId), "操作成功");
    }

    /**
     * 保存售后交付工单拆解草稿
     *
     * @param taskId 工单id
     * @param object 对象
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/decomposition/draft/save/{taskId}")
    public ApiResult<String> decompositionSaveDraft(@PathVariable("taskId") Long taskId,
                                                    @RequestBody Object object) {
        costDeliverTaskService.decompositionSaveDraft(taskId, object);
        return ApiResult.successMsg("操作成功");
    }

    /**
     * 获取创建工单草稿
     *
     * @param projectId projectId
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/draft/{taskType}/{projectId}")
    public ApiResult<Object> draft(@PathVariable("taskType") Integer taskType,
                                   @PathVariable("projectId") Long projectId) {
        return ApiResult.success(costDeliverTaskService.draft(taskType, projectId), "操作成功");
    }

    /**
     * 保存创建工单草稿
     *
     * @param projectId projectId
     * @param object    对象
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/draft/save/{taskType}/{projectId}")
    public ApiResult<String> saveDraft(@PathVariable("taskType") Integer taskType,
                                       @PathVariable("projectId") Long projectId,
                                       @RequestBody Object object) {
        costDeliverTaskService.saveDraft(taskType, projectId, object);
        return ApiResult.successMsg("操作成功");
    }

    /**
     * 查询当前用户工单
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link CostDeliverTaskVO}>>
     */
    @GetMapping("/personalTask")
    public ApiResult<Page<CostDeliverTaskVO>> findPersonalTask(PageRequest pageRequest, CostDeliverTaskDTO request) {
        return ApiResult.success(costDeliverTaskService.findPersonalTask(pageRequest, request));
    }

    /**
     * 查询最新已确认的人工成本预算
     *
     * @param projectId 项目id
     * @return {@link ApiResult}<{@link List}<{@link com.gok.pboot.pms.cost.entity.vo.DeliverCostBudgetListVO}>>
     */
    @GetMapping("/getConfirmedCost/{projectId}")
    public ApiResult<List<DeliverCostBudgetListVO>> getConfirmedCost(@PathVariable Long projectId) {
        return ApiResult.success(costDeliverTaskService.getConfirmedCost(projectId), "获取成功");
    }

    /**
     * 删除工单（只针对一级工单）
     *
     * @param id 工单id
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/remove/{id}")
    public ApiResult<String> remove(@PathVariable Long id) {
        costDeliverTaskService.removeTask(id);
        return ApiResult.successMsg("删除成功");
    }


    /**
     * 批量通过
     *
     * @param costTaskPassDTO costTaskPassDTO
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/batchPassTask")
    public ApiResult<String> passTask(@RequestBody CostTaskPassDTO costTaskPassDTO) {
        costDeliverTaskService.batchPassTask(costTaskPassDTO.getIds());
        return ApiResult.successMsg("通过成功");
    }


    /**
     * 查询工单审核
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link CostDeliverTaskVO}>>
     */
    @GetMapping("/findTaskApprovalPage")
    public ApiResult<Page<CostDeliverApprovalVO>> findTaskApprovalPage(PageRequest pageRequest, CostDeliverTaskDTO request) {
        return ApiResult.success(costDeliverTaskService.findTaskApprovalPageV1(pageRequest, request));
    }


    /**
     * 查看项目工单审核情况
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link CostDeliverTaskVO}>>
     */
    @GetMapping("/findProjectApproval")
    public ApiResult<Page<CostDeliverTaskVO>> findProjectApproval(PageRequest pageRequest, CostDeliverTaskDTO request) {
        return ApiResult.success(costDeliverTaskService.findProjectApprovalV1(pageRequest, request));
    }

    /**
     * 查询我待审核/已审核工单
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link CostDeliverTaskVO}>>
     */
    @GetMapping("/findPersonalApproval")
    public ApiResult<Page<CostDeliverTaskVO>> findPersonalApproval(PageRequest pageRequest, CostDeliverTaskDTO request) {
        request.setPersonal(true);
        return ApiResult.success(costDeliverTaskService.findProjectApprovalV1(pageRequest, request));
    }

    /**
     * 计算人员时薪
     *
     * @param userId 用户ID
     * @return {@link ApiResult}<{@link BigDecimal}>
     */
    @PostMapping("/calculateUserHourlyWages")
    public ApiResult<List<Map<String, Object>>> calculateUserHourlyWages(@RequestBody Set<Long> userId) {
        return ApiResult.success(costDeliverTaskService.calculateUserHourlyWages(userId), "计算成功");
    }

    /**
     * 计算人工成本
     *
     * @param dto DTO
     * @return {@link R }<{@link BigDecimal }>
     */
    @PostMapping("/calculateLaborCost")
    public R<BigDecimal> calculateLaborCost(@RequestBody CostCalculateLaborCostDTO dto) {
        return R.ok(costDeliverTaskService.calculateEstimateLaborCost(dto).getLaborCost());
    }

    /**
     * 获取工单预估产值
     *
     * @param projectId 项目 ID
     * @return {@link ApiResult }<{@link CostDeliverTaskIncomeVo }>
     */
    @GetMapping("/getDeliverTaskIncome")
    public ApiResult<CostDeliverTaskIncomeVo> getDeliverTaskIncome(@RequestParam Long projectId) {
        return ApiResult.success(costDeliverTaskService.getDeliverTaskIncome(projectId), "获取成功");
    }

    /**
     * 结束工单
     *
     * @param id 工单ID
     * @return {@link ApiResult}<{@link String}>
     */
    @GetMapping("/end/{id}")
    public ApiResult<String> endTask(@PathVariable("id") Long id) {
        costDeliverTaskService.endTask(id);
        return ApiResult.successMsg("结束成功");
    }

    /**
     * 重启工单
     *
     * @param id 工单ID
     * @return {@link ApiResult}<{@link String}>
     */
    @GetMapping("/restart/{id}")
    public ApiResult<String> restartTask(@PathVariable("id") Long id) {
        costDeliverTaskService.restartTask(id);
        return ApiResult.successMsg("重启成功");
    }

    /**
     * 查询所有当前用户可填工时的所有项目和对应工单
     *
     * @param date 填报日期
     * @return {@link ApiResult}<{@link List}<{@link ProjectWithTasksInDailyPaperEntryUnifyVO}>>
     */
    @GetMapping("/findCurrentForDailyPaperEntry")
    public ApiResult<List<CostDeliverTaskInDailyPaperEntryVO>> findCurrentForDailyPaperEntry(LocalDate date) {
        return ApiResult.success(costDeliverTaskService.findCurrentForDailyPaperEntry(date));
    }

    /**
     * 查询异常工单列表
     *
     * @param pageRequest 分页请求
     * @param request     查询条件
     * @return {@link ApiResult}<{@link Page}<{@link CostTaskAbnormalVO}>>
     */
    @GetMapping("/findAbnormalPage")
    @Inner(value = false)
    public ApiResult<Page<CostTaskAbnormalVO>> findAbnormalPage(PageRequest pageRequest, CostTaskAbnormalDTO request) {
        return ApiResult.success(costDeliverTaskService.findAbnormalPage(pageRequest, request));
    }

    /**
     * 导出异常工单列表
     *
     * @param request 查询条件
     * @return {@link ApiResult}<{@link Page}<{@link CostTaskAbnormalVO}>>
     */
    @ResponseExcel(name = "异常工单")
    @GetMapping("/exportAbnormal")
    public List<CostTaskAbnormalVO> exportAbnormal(CostTaskAbnormalDTO request) {
        return costDeliverTaskService.exportAbnormal(request);
    }

    /**
     * 发送未提交异常工单消息推送
     *
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/sendUnSubmittedAbnormalMsg")
    @Inner(value = false)
    public ApiResult<String> sendUnSubmittedAbnormalMsg() {
        costDeliverTaskService.sendAbnormalMsg(TaskAbnormalTypeEnum.UN_SUBMITTED);
        return ApiResult.successMsg("推送成功");
    }

    /**
     * 发送未审核异常工单消息推送
     *
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/sendUnReviewedAbnormalMsg")
    @Inner(value = false)
    public ApiResult<String> sendUnReviewedAbnormalMsg() {
        costDeliverTaskService.sendAbnormalMsg(TaskAbnormalTypeEnum.UN_REVIEWED);
        return ApiResult.successMsg("推送成功");
    }

    /**
     * 发送未拆解异常工单消息推送
     *
     * @return {@link ApiResult }<{@link String }>
     */
    @GetMapping("/sendUnDecomposedAbnormalMsg")
    @Inner(value = false)
    public ApiResult<String> sendUnDecomposedAbnormalMsg() {
        costDeliverTaskService.sendAbnormalMsg(TaskAbnormalTypeEnum.UN_DECOMPOSED);
        return ApiResult.successMsg("推送成功");
    }


    /**
     * 发送异常工单消息推送
     * 包含三种异常工单：
     * 1. 未提交：超过工单的起止日期的截止日期1天，工单尚未提交完成
     * 2. 未审核：超过工单提交日期1天后，工单尚未进行审核
     * 3. 未拆解：工单为总成工单，起止日期含近60天内的日期，工单尚未进行拆解
     * 4. 未评价：工单已经审核通过，工单尚未进行评价
     *
     * @param request 查询条件
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/sendAbnormalMsg")
    public ApiResult<String> sendAbnormalMsg(@RequestBody @Valid CostTaskAbnormalDTO request) {
        costDeliverTaskService.sendAbnormalMsg(request);
        return ApiResult.successMsg("推送成功");
    }

    /**
     * 获取异常工单头部统计信息
     *
     * @return {@link ApiResult }<{@link CostTaskAbnormalCountVO }>
     */
    @GetMapping("/getAbnormalCount")
    public ApiResult<CostTaskAbnormalCountVO> getAbnormalCount(CostTaskAbnormalDTO request) {
        return ApiResult.success(costDeliverTaskService.getAbnormalCount(request), "获取成功");
    }

    /**
     * 结束售前支撑工单
     *
     * @return {@link ApiResult }<{@link String }>
     */
    @Inner(false)
    @GetMapping("/endSupportTask")
    public ApiResult<String> endSupportTask() {
        costDeliverTaskService.endSupportTask();
        return ApiResult.successMsg("操作成功");
    }

    /**
     * 定时任务：自动为在建项目创建成本挂靠工单
     * 用于会议、学习、空转、其他等事项的成本挂靠
     *
     * @return {@link ApiResult}<{@link String}>
     */
    @GetMapping("/autoCreateCostAttachmentTasks")
    @Inner(value = false)
    public ApiResult<String> autoCreateCostAttachmentTasks() {
        costDeliverTaskService.autoCreateCostAttachmentTasks();
        return ApiResult.success("成本挂靠工单自动创建任务执行成功");
    }

    @GetMapping("/autoCreateCostAttachmentTasks/{id}")
    @Inner(value = false)
    public ApiResult<String> autoCreateCostAttachmentTasks(@PathVariable Long id) {
        costDeliverTaskService.test(id);
        return ApiResult.success("成本挂靠工单自动创建任务执行成功");
    }

}


