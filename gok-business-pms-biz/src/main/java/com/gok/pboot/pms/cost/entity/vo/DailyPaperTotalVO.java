package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 待审核日报 VO
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
@Accessors(chain = true)
public class DailyPaperTotalVO {
    /**
     * 工时
     */
    private BigDecimal workHours;

    /**
     * 成本
     */
    private BigDecimal laborCosts;

    /**
     * 工时日报列表
     */
    List<TaskDailyPaperDetailVO> list;
}
