<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostManageEstimationResultsMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostManageEstimationResults">
        <id column="id" property="id"/>
        <result column="version_id" property="versionId"/>
        <result column="project_id" property="projectId"/>
        <result column="source" property="source"/>
        <result column="cost_budget_type" property="costBudgetType"/>
        <result column="account_id" property="accountId"/>
        <result column="account_name" property="accountName"/>
        <result column="account_type" property="accountType"/>
        <result column="subsidy_custom_config_id" property="subsidyCustomConfigId"/>
        <result column="subsidy_custom_config_name" property="subsidyCustomConfigName"/>
        <result column="budget_amount_included_tax" property="budgetAmountIncludedTax"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="budget_amount_excluding_tax" property="budgetAmountExcludingTax"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        version_id,
        project_id,
        source,
        cost_budget_type,
        account_id,
        account_name,
        subsidy_custom_config_id,
        subsidy_custom_config_name,
        account_type,
        budget_amount_included_tax,
        tax_rate,
        budget_amount_excluding_tax,
        remark,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
    </sql>

    <select id="findByVersionId" resultType="com.gok.pboot.pms.cost.entity.vo.CostManageEstimationResultsVO">
        SELECT
            cmer.id,
            cmer.source,
            cmer.project_id,
            cmer.version_id,
            cmer.cost_budget_type,
            cmer.account_id,
            cmer.account_name,
            cmer.subsidy_custom_config_id,
            cmer.subsidy_custom_config_name,
            cmer.account_type,
            cca.account_category_id,
            cca.account_category_name,
            cca.account_code,
            cca.oa_id AS accountOaId,
            cmer.budget_amount_included_tax,
            cmer.tax_rate,
            cmer.budget_amount_excluding_tax,
            cmer.remark,
            cca.account_type AS costType
        FROM
            cost_manage_estimation_results AS cmer
        LEFT JOIN cost_config_account AS cca ON cmer.account_id = cca.id
        <where>
            cmer.del_flag = ${@<EMAIL>()}
            <if test="versionIds !=null and versionIds.size > 0">
                AND cmer.version_id IN
                <foreach collection="versionIds" item="versionId" index="index" open="(" close=")" separator=",">
                    #{versionId}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="batchSave">
        INSERT INTO cost_manage_estimation_results (
        id,
        version_id,
        project_id,
        source,
        cost_budget_type,
        account_id,
        account_name,
        subsidy_custom_config_id,
        subsidy_custom_config_name,
        account_type,
        budget_amount_included_tax,
        tax_rate,
        budget_amount_excluding_tax,
        remark,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
        )VALUES
        <foreach collection="saveEntries" item="item" separator=",">
            (
            #{item.id},
            #{item.versionId},
            #{item.projectId},
            #{item.source},
            #{item.costBudgetType},
            #{item.accountId},
            #{item.accountName},
            #{item.subsidyCustomConfigId},
            #{item.subsidyCustomConfigName},
            #{item.accountType},
            #{item.budgetAmountIncludedTax},
            #{item.taxRate},
            #{item.budgetAmountExcludingTax},
            #{item.remark},
            #{item.creator},
            #{item.creatorId},
            #{item.modifier},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <select id="getCostConfigVersion" resultType="com.gok.pboot.pms.cost.entity.vo.CostManageConfigVersionVO">
        SELECT
        t1.cost_config_version_type,
        MAX( current_version_id ) AS current_version_id,
        MAX( current_version_name ) AS current_version_name,
        MAX( latest_version_id ) AS latest_version_id,
        MAX( latest_version_name ) AS latest_version_name
        FROM
        (
        (
        SELECT
            version_type AS cost_config_version_type,
            NULL AS current_version_id,
            NULL AS current_version_name,
            id AS latest_version_id,
            version_name AS latest_version_name
        FROM
            cost_config_version
        WHERE
        del_flag = ${@<EMAIL>()}
        AND version_status = ${@<EMAIL>()}
        )
        <if test="versionId !=null">
            UNION ALL
            ( SELECT
                ${@<EMAIL>()} AS cost_config_version_type,
                ccts.version_id AS current_version_id,
                ccv.version_name AS current_version_name,
                NULL AS latest_version_id,
                NULL AS latest_version_name
            FROM
                cost_manage_personnel_level_detail AS cmpld
            LEFT JOIN cost_config_travel_stay AS ccts ON cmpld.travel_stay_config_id = ccts.id
            LEFT JOIN cost_config_version AS ccv ON ccts.version_id = ccv.id
            WHERE
                cmpld.version_id = #{versionId}
                AND cmpld.travel_stay_config_id IS NOT NULL
                AND cmpld.del_flag = ${@<EMAIL>()}
            LIMIT 1
            ) UNION ALL
            (
            SELECT
                ${@<EMAIL>()},
                ccts.version_id,
                ccv.version_name,
                NULL,
                NULL
            FROM
                cost_manage_personnel_level_detail AS cmpld
            LEFT JOIN cost_config_travel_subsidy AS ccts ON cmpld.travel_subsidy_config_id = ccts.id
            LEFT JOIN cost_config_version AS ccv ON ccts.version_id = ccv.id
            WHERE
                cmpld.version_id = #{versionId}
                AND cmpld.travel_stay_config_id IS NOT NULL
                AND cmpld.del_flag = ${@<EMAIL>()}
            LIMIT 1
            ) UNION ALL
            (
            SELECT
                ${@<EMAIL>()},
                ccsc.version_id,
                ccv.version_name,
                NULL,
                NULL
            FROM
                cost_manage_personnel_custom_detail AS cmpcd
            LEFT JOIN cost_config_subsidy_custom AS ccsc ON cmpcd.subsidy_custom_config_id = ccsc.id
            LEFT JOIN cost_config_version AS ccv ON ccsc.version_id = ccv.id
            WHERE
                cmpcd.version_id = #{versionId}
                AND cmpcd.subsidy_custom_config_id IS NOT NULL
                AND cmpcd.del_flag = ${@<EMAIL>()}
            LIMIT 1
            ) UNION ALL
            (
            SELECT
                ${@<EMAIL>()},
                cca.version_id,
                ccv.version_name,
                NULL,
                NULL
            FROM
                cost_manage_estimation_results AS cmer
            LEFT JOIN cost_config_account AS cca ON cmer.account_id = cca.id
            LEFT JOIN cost_config_version AS ccv ON cca.version_id = ccv.id
            WHERE
                cmer.version_id = #{versionId}
                AND cmer.account_id IS NOT NULL
                AND cmer.del_flag = ${@<EMAIL>()}
            LIMIT 1
            ) UNION ALL (
            SELECT
                ${@<EMAIL>()},
                cclp.version_id,
                ccv.version_name,
                NULL,
                NULL
            FROM
            cost_manage_personnel_level_detail AS cmpld
            LEFT JOIN cost_config_level_price AS cclp ON cmpld.level_config_id = cclp.id
            LEFT JOIN cost_config_version AS ccv ON cclp.version_id = ccv.id
            WHERE
                cmpld.version_id = #{versionId}
                AND cmpld.level_config_id IS NOT NULL
                AND cmpld.del_flag = ${@<EMAIL>()}
            LIMIT 1
            ) UNION ALL
            (
            SELECT
                5,
                NULL AS current_version_id,
                NULL AS current_version_name,
                id AS latest_version_id,
                version_no AS latest_version_name
            FROM
            cost_cash_plan_version
            WHERE
                del_flag = ${@<EMAIL>()}
                AND project_id = #{projectId}
                AND version_status = ${@<EMAIL>()}
            ) UNION ALL
            (
            SELECT
                5,
                cash_plan_version_id AS current_version_id,
                cash_plan_version AS current_version_name,
                NULL AS latest_version_id,
                NULL AS latest_version_name
            FROM
                cost_manage_version AS cmv
            WHERE
                del_flag = ${@<EMAIL>()}
                AND id = #{versionId}
            )
        </if>
        ) AS t1
        GROUP BY
        t1.cost_config_version_type
    </select>

    <select id="findAllById" resultType="com.gok.pboot.pms.cost.entity.vo.CostManageEstimationResultsVO">
        SELECT
            cmer.id,
            cmer.source,
            cmer.cost_budget_type,
            cmer.account_id,
            cmer.account_name,
            cmer.subsidy_custom_config_id,
            cmer.subsidy_custom_config_name,
            cmer.account_type,
            cca.account_category_id,
            cca.account_category_name,
            cca.account_code,
            cca.oa_id AS accountOaId,
            cmer.budget_amount_included_tax,
            cmer.tax_rate,
            cmer.budget_amount_excluding_tax
        FROM
            cost_manage_estimation_results AS cmer
                LEFT JOIN cost_config_account AS cca ON cmer.account_id = cca.id
        WHERE cmer.id = #{id}
            AND cmer.del_flag = ${@<EMAIL>()}
    </select>

    <select id="findCostBudgetByVersionId" resultType="com.gok.pboot.pms.cost.entity.vo.DeliverCostBudgetListVO">
        SELECT
            cmer.id,
            cmer.cost_budget_type,
            cmer.account_id,
            cmer.account_name,
            cmer.subsidy_custom_config_id,
            cmer.subsidy_custom_config_name,
            cmer.account_type,
            cca.account_category_id,
            cca.account_category_name,
            cca.account_code,
            cca.oa_id AS accountOaId,
            cmer.budget_amount_included_tax,
            cmer.tax_rate,
            cmer.budget_amount_excluding_tax,
            cmer.budget_amount_included_tax AS remainBudget,
            cmer.remark,
            cca.account_type as costType
        FROM
            cost_manage_estimation_results AS cmer
        LEFT JOIN cost_config_account AS cca ON cmer.account_id = cca.id
        WHERE cmer.version_id = #{versionId}
        AND cmer.del_flag = ${@<EMAIL>()}
        <if test="costType != null">
           AND cca.account_type = #{costType}
        </if>
        ORDER BY cmer.id desc
    </select>


<select id="findCostManageSelectListVO" resultType="com.gok.pboot.pms.cost.entity.vo.CostManageSelectVO">
        SELECT
            cmer.account_id,
            cmer.account_name,
            cmer.tax_rate,
            cca.oa_id AS accountOaId,
            sum(cmer.budget_amount_included_tax) as budgetAmountIncludedTax
        FROM
            cost_manage_estimation_results AS cmer
        LEFT JOIN cost_config_account AS cca ON cmer.account_id = cca.id
        WHERE cmer.version_id = #{versionId}
        AND cmer.del_flag = ${@<EMAIL>()}
        AND cmer.account_id = #{accountId}
        group by cmer.account_id,cmer.account_name
    </select>

</mapper>
