<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostManageVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostManageVersion">
        <id column="id" property="id" />
        <result column="version_type" property="versionType" />
        <result column="version_status" property="versionStatus" />
        <result column="version_name" property="versionName" />
        <result column="project_id" property="projectId" />
        <result column="request_id" property="requestId" />
        <result column="request_name" property="requestName" />
        <result column="request_type" property="requestType" />
        <result column="cost_budget_type" property="costBudgetType" />
        <result column="status" property="status" />
        <result column="pre_sale_version_id" property="preSaleVersionId" />
        <result column="pre_sale_version_name" property="preSaleVersionName" />
        <result column="refuse_reason" property="refuseReason" />
        <result column="cash_plan_version_id" property="cashPlanVersionId" />
        <result column="cash_plan_version" property="cashPlanVersion" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="operator_dept_id" property="operatorDeptId" />
        <result column="operator_dept_name" property="operatorDeptName" />
        <result column="operator_role" property="operatorRole" />
        <result column="generate_total_cost" property="generateTotalCost" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version_type, version_status, version_name, project_id, request_id, request_name, request_type, cost_budget_type,
        status, pre_sale_version_id, pre_sale_version_name, operator_id, operator_name, operator_dept_id, operator_dept_name,
        operator_role, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag, generate_total_cost
    </sql>

    <select id="getLatestCostManageEstimation" resultType="com.gok.pboot.pms.cost.entity.domain.CostManageVersion">
        SELECT
            *
        FROM
            cost_manage_version
        WHERE
          project_id = #{query.projectId}
          AND version_type = ${@<EMAIL>()}
          AND del_flag = ${@<EMAIL>()}
          <if test="query.status != null">
            AND status = #{query.status}
          </if>
          <if test="query.costBudgetTypeList != null and query.costBudgetTypeList.size > 0">
              AND cost_budget_type IN
              <foreach collection="query.costBudgetTypeList" item="costBudgetType" open="(" separator="," close=")">
                  #{costBudgetType}
              </foreach>
          </if>
          ORDER BY
            <if test="query.costBudgetTypeList != null and query.costBudgetTypeList.size > 0  ">
              <if test="query.costBudgetTypeList.contains(1) or query.costBudgetTypeList.contains(2)">
                cost_budget_type DESC,
              </if>
            </if>
            ctime DESC
          LIMIT 1
    </select>



  <select id="findPage" resultType="com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO">
    SELECT
       id AS versionId,
       version_type,
       version_status,
       version_name,
       project_id,
       request_id,
       request_name,
       pre_sale_version_id,
       pre_sale_version_name,
       cost_budget_type,
       status,
       creator as operatorName,
       operator_role,
       creator_id as creatorId,
       ctime
    FROM
      cost_manage_version
    WHERE  project_id = #{query.projectId}
      AND  del_flag = ${@<EMAIL>()}
      AND  version_type = #{query.versionType}
      <if test="query.costBudgetTypeList != null and query.costBudgetTypeList.size > 0">
          and cost_budget_type IN
          <foreach collection="query.costBudgetTypeList" item="costBudgetType" open="(" separator="," close=")">
              #{costBudgetType}
          </foreach>
      </if>
    ORDER BY
      ctime DESC
  </select>

    <select id="getCostManageTargetVersionInfo"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostManageTargetVersionIInfoVO"
            parameterType="java.lang.Long">
        select id,
               version_name,
               project_id,
               request_id,
               request_name,
               status,
               creator,
               ctime
        from cost_manage_version
        where del_flag = 0
          and version_type = 0
          and project_id = #{projectId}
        order by ctime desc
    </select>

    <update id="updateVersionStatusById">
        update cost_manage_version
        set version_status = #{versionStatus}
        where id = #{id}
    </update>

    <select id="queryProjectVersionByProjectId"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO">
        SELECT
            ccv.id,
            ccv.project_id,
            ccv.cost_budget_type,
            ccv.request_id,
            ccv.version_type,
            ccv.version_name,
            ccv.ctime
        FROM
            cost_manage_version AS ccv
        WHERE
          ccv.project_id = #{projectId}
          <if test="versionStatus != null">
            AND ccv.version_status =  #{versionStatus}
          </if>
          AND ccv.del_flag = ${@<EMAIL>()}
        GROUP BY
            ccv.id,
            ccv.version_type,
            ccv.version_name
    </select>

    <select id="getLatestPreSalesCost" resultType="com.gok.pboot.pms.cost.entity.domain.CostManageVersion">
        SELECT
            cmv.*
        FROM
            cost_manage_version AS cmv
                INNER JOIN (
                SELECT
                    project_id,
                    MAX(ctime) AS max_ctime
                FROM
                    cost_manage_version
                WHERE
                    cost_budget_type = 0
                  AND version_type = 1
                  AND version_status = 0
                  AND generate_total_cost = 1
                  AND del_flag = 0
                  AND request_id IS NOT NULL
                GROUP BY
                    project_id
            ) latest_versions ON
                        cmv.project_id = latest_versions.project_id
                    AND cmv.ctime = latest_versions.max_ctime
        WHERE
                cmv.id IN (
                SELECT DISTINCT version_id
                FROM cost_manage_estimation_results
                WHERE del_flag = 0
                  AND cost_budget_type = 0
            );
    </select>

    <select id="getLatestTotalCost" resultType="com.gok.pboot.pms.cost.entity.domain.CostManageVersion">
        SELECT
            cmv.*
        FROM
            cost_manage_version AS cmv
                INNER JOIN (
                SELECT
                    project_id,
                    MAX( ctime ) AS max_ctime
                FROM
                    cost_manage_version
                WHERE
                    cost_budget_type IN ( 1, 2 )
                  AND version_type = 1
                  AND version_status = 0
                  AND del_flag = 0
                  AND project_id IN
                  <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                      #{projectId}
                  </foreach>
                GROUP BY
                    project_id
            ) latest_versions ON cmv.project_id = latest_versions.project_id
                AND cmv.ctime = latest_versions.max_ctime
    </select>


    <select id="getLatestCostManageVersion" resultType="com.gok.pboot.pms.cost.entity.domain.CostManageVersion">
        SELECT
            *
        FROM
            cost_manage_version
        WHERE
        del_flag = ${@<EMAIL>()}
        <if test="query.projectId != null">
            AND project_id = #{query.projectId}
        </if>
        <if test="query.versionType != null">
            AND version_type = #{query.versionType}
        </if>
        <if test="query.costBudgetTypeList != null and query.costBudgetTypeList.size > 0">
            AND cost_budget_type IN
            <foreach collection="query.costBudgetTypeList" item="costBudgetType" open="(" separator="," close=")">
                #{costBudgetType}
            </foreach>
        </if>
        <if test="query.id != null">
            AND id = #{query.id}
        </if>
        order by ctime DESC
        limit 1
    </select>
</mapper>
