<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.ProjectSchedulePlanMapper">
    <update id="batchDelByVersionId">
        update project_schedule_plan
        SET del_flag = ${@<EMAIL>()}
        WHERE version_id = #{versionId}
    </update>

    <select id="getLastSchedulePlan" resultType="com.gok.pboot.pms.cost.entity.domain.ProjectSchedulePlan">
        SELECT *
        FROM project_schedule_plan
        WHERE project_id = #{projectId}
          AND del_flag = 0
          AND version_id = (SELECT version_id
                            FROM project_schedule_plan
                            WHERE project_id = #{projectId} AND del_flag = 0
                            ORDER BY version_id DESC
                            LIMIT 1)
    </select>
</mapper>