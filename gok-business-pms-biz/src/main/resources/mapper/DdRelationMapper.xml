<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.didi.mapper.DdRelationMapper">


    <!-- 根据关联ID和类型查询关联关系 -->
    <select id="selectByRelateIdAndType" resultType="com.gok.pboot.pms.didi.entity.domain.DdRelation">
        SELECT
        *
        FROM dd_relation
        WHERE relate_id = #{relateId} AND relate_type = #{relateType}
        LIMIT 1
    </select>


    <!-- 根据滴滴ID和关联类型查询关联关系 -->
    <select id="selectByDidiIdAndType" resultType="com.gok.pboot.pms.didi.entity.domain.DdRelation">
        SELECT
        *
        FROM dd_relation
        WHERE didi_id = #{didiProjectId} AND relate_type = #{relateType}
        LIMIT 1
    </select>

    <!-- 更新同步时间 -->
    <update id="updateSyncTimeByRelateIdAndType">
        UPDATE dd_relation
        SET sync_time = NOW()
        WHERE relate_id = #{relateId}
          AND relate_type = #{relateType}
    </update>

    <select id="selectDidiProjectIdsByUserId" resultType="java.lang.String">
        select distinct dr.didi_id
        from project_stakeholder_member psm
                 left join dd_relation dr on dr.relate_id = psm.project_id
        where dr.relate_type = 1
          and psm.member_id = #{userId}
    </select>

    <!-- 批量根据关联ID和类型查询关联关系 -->
    <select id="selectBatchByRelateIdAndType" resultType="com.gok.pboot.pms.didi.entity.domain.DdRelation">
        SELECT *
        FROM dd_relation
        WHERE
        <foreach collection="relations" item="relation" open="(" separator="or" close=")">
            (relate_id=#{relation.relateId} and relate_type= #{relation.relateType})
        </foreach>
    </select>





</mapper>
