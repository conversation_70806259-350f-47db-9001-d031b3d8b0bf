<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.ProjectSchedulePlanActivityMapper">

    <update id="batchDelByVersionId">
        update project_schedule_plan_activity
        SET del_flag = ${@<EMAIL>()}
        WHERE version_id = #{versionId}
    </update>

    <select id="findList" resultType="com.gok.pboot.pms.cost.entity.vo.ProjectScheduleActivityPlanVO">
        SELECT
            pspa.*,
            psp.budget_cost,
            psp.sort AS stageSort
        FROM
            project_schedule_plan_activity AS pspa
            LEFT JOIN ( SELECT *  FROM project_schedule_plan WHERE project_id =  #{projectId} AND version_id =  #{versionId} ) AS psp ON psp.id = pspa.plan_id
        WHERE
            pspa.del_flag = ${@<EMAIL>()}
          AND pspa.project_id =  #{projectId}
          AND pspa.version_id =  #{versionId}
        ORDER BY
            psp.sort,
            pspa.sort
    </select>

</mapper>