<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.eval.mapper.EvalTaskMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.eval.entity.domain.EvalTask">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="quality_score" property="qualityScore"/>
        <result column="quality_remark" property="qualityRemark"/>
        <result column="timeliness_score" property="timelinessScore"/>
        <result column="timeliness_remark" property="timelinessRemark"/>
        <result column="cooperation_score" property="cooperationScore"/>
        <result column="cooperation_remark" property="cooperationRemark"/>
        <result column="discipline_score" property="disciplineScore"/>
        <result column="discipline_remark" property="disciplineRemark"/>
        <result column="Comprehensive_score" property="comprehensiveScore"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="findListByDto" resultType="com.gok.pboot.pms.eval.entity.vo.EvalTaskListVO">
        SELECT t.id                                                                                        AS taskId,
               t.project_id                                                                                AS projectId,
               t.project_name                                                                              AS projectName,
               p.item_no                                                                                   AS projectNo,
               t.manager_id                                                                                AS managerId,
               r.work_code                                                                                 AS managerNo,
               t.manager_name                                                                              AS managerName,
               t.task_no                                                                                   AS taskNo,
               t.task_name                                                                                 AS taskName,
               t.evaluation_status                                                                         AS evaluationStatus,
               e.comprehensive_score                                                                       AS comprehensiveScore,
               t.income                                                                                    AS income,
               t.income / t2.incomeSum                                                                     AS settlementValueRatio,
               t.budget_cost                                                                               AS budgetCostStr,
               t.actual_labor_cost                                                                         AS actualLaborCostStr,
               t.estimated_hours                                                                           AS estimatedHours,
               (IFNULL(t.normal_hours,0) + IFNULL(t.work_overtime_hours,0)
               + IFNULL(t.rest_overtime_hours,0) + IFNULL(t.holiday_overtime_hours,0))                     AS actualHours,
               t.task_status                                                                               AS taskStatus
        FROM cost_deliver_task t
        LEFT JOIN eval_task e ON t.id = e.task_id AND e.del_flag = ${@<EMAIL>()}
        LEFT JOIN project_info p on t.project_id = p.id
        LEFT JOIN mhour_roster r on t.manager_id = r.id
        LEFT JOIN (
            select project_id,manager_id,sum(income) as incomeSum
            from cost_deliver_task
            where del_flag = ${@<EMAIL>()}
            group by project_id,manager_id
        )t2 on t.project_id = t2.project_id and t.manager_id = t2.manager_id
        WHERE
        t.del_flag = ${@<EMAIL>()}
        AND t.task_category != ${@com.gok.pboot.pms.cost.enums.TaskCategoryEnum@PROJECT_COST.getValue()}
        AND t.task_type =  #{dto.taskType}
        AND t.disassembly_type = ${@com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum@STANDARD_WORK_ORDER.getValue()}
        AND t.actual_labor_cost IS NOT NULL
        AND t.task_status in (${@<EMAIL>()},
                                ${@<EMAIL>()},
                                ${@<EMAIL>()},
                                ${@<EMAIL>()},
                                ${@<EMAIL>()})
        <if test="dto.evaluationStatue != null">
            AND t.evaluation_status = #{dto.evaluationStatue}
        </if>
        <if test="dto.managerName != null and dto.managerName != ''">
            AND t.manager_name LIKE CONCAT('%', #{dto.managerName}, '%')
        </if>
        <if test="dto.managerNo != null and dto.managerNo != ''">
            AND r.work_code LIKE CONCAT('%', #{dto.managerNo}, '%')
        </if>
        <if test="dto.startDate != null and dto.endDate != null">
            <if test="dto.taskType != null and dto.taskType == 1">
                AND ((t.start_date &gt;= #{dto.startDate} AND t.start_date &lt;= #{dto.endDate})
                    OR (t.end_date &gt;= #{dto.startDate} AND t.end_date &lt;= #{dto.endDate}))
            </if>
            <if test="dto.taskType != null and dto.taskType == 0">
                AND ((DATE_FORMAT(t.ctime, '%Y-%m-%d') &gt;= #{dto.startDate} AND DATE_FORMAT(t.ctime, '%Y-%m-%d') &lt;= #{dto.endDate})
                    OR (DATE_FORMAT(t.completion_time, '%Y-%m-%d') &gt;= #{dto.startDate} AND DATE_FORMAT(t.completion_time, '%Y-%m-%d') &lt;= #{dto.endDate}))
            </if>
        </if>
        <if test="dto.evaluationStatus != null and dto.evaluationStatus.size() > 0">
            AND t.evaluation_status IN
            <foreach collection="dto.evaluationStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="dto.projectNo != null and dto.projectNo != ''">
            AND p.item_no LIKE CONCAT('%', #{dto.projectNo}, '%')
        </if>
        <if test="dto.projectName != null and dto.projectName != ''">
            AND t.project_name LIKE CONCAT('%', #{dto.projectName}, '%')
        </if>
        <if test="dto.taskNo != null and dto.taskNo != ''">
            AND t.task_no LIKE CONCAT('%', #{dto.taskNo}, '%')
        </if>
        <if test="dto.taskName != null and dto.taskName != ''">
            AND t.task_name LIKE CONCAT('%', #{dto.taskName}, '%')
        </if>
        <if test="dto.authTaskIdList != null and dto.authTaskIdList.size() != 0">
            AND t.id IN
            <foreach collection="dto.authTaskIdList" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        ORDER BY t.evaluation_status ASC,
                 t.ctime DESC
    </select>

    <select id="getEvalDistribution" resultType="com.gok.pboot.pms.eval.entity.vo.EvalTaskDistributionVO">
        SELECT t.manager_id      AS managerId,
               r.work_code       AS managerNo,
               t.manager_name    AS managerName,
               t.task_type       AS taskType,
               t2.current_score  AS personalScore,
               t2.adjusted_score AS adjustedScore,
               IFNULL(t2.adjusted_level,t2.current_level)  AS personalLevelInt,
               t2.rank           AS rank,
               COUNT(t.id)       as evaluatedTaskCount
        FROM cost_deliver_task t
        LEFT JOIN eval_task e ON t.id = e.task_id AND e.del_flag = ${@<EMAIL>()}
        LEFT JOIN mhour_roster r on t.manager_id = r.id
        LEFT JOIN eval_task_calibration t2 ON t.project_id = t2.project_id
                AND t.manager_id = t2.employee_id
                AND t.task_type = t2.task_type
                AND t2.del_flag = ${@<EMAIL>()}
        WHERE
        t.del_flag = ${@<EMAIL>()}
        AND t2.current_level is not null
        AND t.project_id = #{dto.projectId}
        <if test="dto.taskType != null">
            AND t.task_type = #{dto.taskType}
        </if>
          AND t.evaluation_status = ${@<EMAIL>()}
        GROUP BY t.manager_id, t.manager_name,t.task_type
        ORDER BY t2.rank
    </select>

    <select id="getAfterEvalDistributionDetail" resultType="com.gok.pboot.pms.eval.entity.vo.EvalTaskDistributionAfterDetailVO">
        SELECT t.id                                              AS taskId,
               t.task_no                                         AS taskNo,
               t.task_name                                       AS taskName,
               t.manager_id                                      AS managerId,
               t.manager_name                                    AS managerName,
               t.budget_cost                                     AS budgetCost,
               t.actual_labor_cost                               AS actualLaborCost,
               t.estimated_hours                                 AS estimatedHours,
               IFNULL(t.normal_hours, 0) + IFNULL(t.work_overtime_hours, 0) + IFNULL(t.rest_overtime_hours, 0) +
               IFNULL(t.holiday_overtime_hours, 0)               AS actualHours,
               t.task_status                                     AS taskStatus,
               t.income                                          AS income,
               t.income / t2.incomeSum                           AS settlementValueRatio,
               e.comprehensive_score                             AS comprehensiveScore
        FROM cost_deliver_task t
        LEFT JOIN eval_task e ON t.id = e.task_id
        LEFT JOIN (SELECT project_id, manager_id, sum(income) AS incomeSum
                   FROM cost_deliver_task
                   WHERE del_flag = ${@<EMAIL>()}
                   AND project_id = #{dto.projectId}
                   GROUP BY project_id, manager_id) t2 ON t.project_id = t2.project_id AND t.manager_id = t2.manager_id
        WHERE t.del_flag = ${@<EMAIL>()}
          AND t.project_id = #{dto.projectId}
          AND t.manager_id = #{dto.managerId}
          AND t.task_type = #{dto.taskType}
          AND t.evaluation_status = ${@<EMAIL>()}
        order by t.ctime
    </select>

    <select id="getPreEvalDistributionDetail" resultType="com.gok.pboot.pms.eval.entity.vo.EvalTaskDistributionPreDetailVO">
        SELECT t.id                                                   AS taskId,
               t.task_no                                              AS taskNo,
               t.task_name                                            AS taskName,
               e.comprehensive_score                                  AS comprehensiveScore,
               t.manager_id                                           AS managerId,
               t.manager_name                                         AS managerName,
               t.actual_labor_cost                                    AS actualLaborCost,
               IFNULL(t.normal_hours, 0) + IFNULL(t.work_overtime_hours, 0) + IFNULL(t.rest_overtime_hours, 0) +
               IFNULL(t.holiday_overtime_hours, 0)                    AS auditedHours,
               IFNULL(pe.normal_hours, 0) + IFNULL(pe.added_hours, 0) AS unAuditedHours,
               t.task_status
        FROM cost_deliver_task t
        LEFT JOIN cost_task_daily_paper_entry pe on t.id = pe.task_id
                    AND pe.approval_status = ${@<EMAIL>}
                    AND pe.del_flag = ${@<EMAIL>()}
        LEFT JOIN eval_task e ON t.id = e.task_id
        WHERE t.del_flag = ${@<EMAIL>()}
          AND t.project_id = #{dto.projectId}
          AND t.manager_id = #{dto.managerId}
          AND t.task_type = #{dto.taskType}
          AND t.evaluation_status = ${@<EMAIL>()}
        order by t.ctime
    </select>

    <select id="countComprehensiveScore" resultType="com.gok.pboot.pms.eval.entity.vo.ProjectEvalRankVO">
        select t1.projectId as projectId,
               t1.managerId as employeeId,
               t1.income as income,
               t1.personalScore as comprehensiveScore,
               CASE
                       WHEN t1.personalScore >= 3 THEN 3
                       WHEN t1.personalScore >= 2 THEN 2
                       WHEN t1.personalScore >= 1 THEN 1
                       WHEN t1.personalScore >= 0 THEN 0
                       ELSE null
                       END AS currentLevel
        from (
                     SELECT t.project_id                                           AS projectId,
                            t.manager_id                                           AS managerId,
                            t2.incomeSum                                           AS income,
                            SUM(e.comprehensive_score * IFNULL((t.income / t2.incomeSum),0)) AS personalScore
                     FROM cost_deliver_task t
                                  LEFT JOIN eval_task e ON t.id = e.task_id
                                  LEFT JOIN (select project_id, manager_id, sum(income) as incomeSum
                                             from cost_deliver_task
                                             where del_flag = ${@<EMAIL>()}
                                               and project_id = #{projectId}
                                             group by project_id, manager_id
                             ) t2 on t.project_id = t2.project_id and t.manager_id = t2.manager_id
                     WHERE t.del_flag = ${@<EMAIL>()}
                       AND t.project_id = #{projectId}
                       AND t.manager_id = #{managerId}
                       AND t.evaluation_status = ${@<EMAIL>()}
                     GROUP BY t.project_id, t.manager_id
                     ) t1
    </select>

    <update id="delById">
        UPDATE eval_task SET
            del_flag = 1
        WHERE id = #{id}
    </update>

</mapper>