alter table project_taske add column type tinyint(2) DEFAULT 0 COMMENT '任务类型';

alter table project_taske add column relate_id bigint DEFAULT null COMMENT '关联id';

ALTER TABLE project_taske ADD INDEX idx_relate_id (relate_id);

ALTER TABLE mhour_daily_paper_entry ADD INDEX idx_task_id (task_id);


alter table cost_task_category_management MODIFY task_type tinyint(2) NOT NULL COMMENT '工单类型(0=售前支撑, 1=售后交付, 2=工时类型)';

UPDATE `gok_pms_service`.`cost_task_category_management` SET `task_category` = 6 WHERE `id` = 10001;


UPDATE `gok_pms_service`.`cost_deliver_task` SET `task_category` = 6 WHERE `task_category` = 1;

alter table gok_pms_service.cost_deliver_task MODIFY account_id bigint(20) DEFAULT NULL COMMENT '成本科目ID';

alter table gok_pms_service.cost_deliver_task MODIFY manager_id bigint(20) DEFAULT NULL COMMENT '工单负责人id';

alter table mhour_daily_paper_entry add  column actualLaborCost varchar(256) DEFAULT NULL COMMENT '实际人工成本';


INSERT INTO `gok_pms_service`.`cost_task_category_management` (`id`, `task_category`, `task_category_name`, `task_type`, `can_self_create`, `sort`, `creator`, `creator_id`, `modifier`, `modifier_id`, `ctime`, `mtime`, `del_flag`)
VALUES
    (10003, 0, '售前支撑', 2, 1, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:44:25', 0),
    (10004, 1, '售后交付', 2, 1, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:45:25', 0),
    (10005, 7, '成本工单', 1, 1, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:45:25', 0),
    (20001, 11, '会议', 2, 0, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:45:25', 0),
    (20002, 12, '学习', 2, 0, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:45:25', 0),
    (20003, 13, '培训', 2, 0, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:45:25', 0),
    (20004, 14, '空转', 2, 0, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:45:25', 0),
    (20005, 15, '其他', 2, 0, 0, '超级管理员', 10000, NULL, NULL, '2025-08-21 14:43:57', '2025-08-21 14:45:25', 0)
;

