drop table if EXISTS dd_flight_ticket_order;
CREATE TABLE `dd_flight_ticket_order`
(
    `id`                           bigint(20)   NOT NULL COMMENT '主键ID',
    `order_no`                     varchar(64)  NOT NULL COMMENT '订单号',
    `passenger_id`                 bigint(20)            DEFAULT NULL COMMENT '乘机人ID',
    `passenger_employee_no`        varchar(64)           DEFAULT NULL COMMENT '乘机人工号',
    `passenger_name`               varchar(64)  NOT NULL COMMENT '乘机人姓名',
    `passenger_dept_id`            bigint(20)            DEFAULT NULL COMMENT '乘机人部门ID',
    `passenger_dept_name`          varchar(200)          DEFAULT NULL COMMENT '乘机人部门名称',
    `ticket_no`                    varchar(64)           DEFAULT NULL COMMENT '票号',
    `ticket_status`                varchar(32)           DEFAULT NULL COMMENT '票号状态',
    `departure_location`           varchar(100)          DEFAULT NULL COMMENT '出发地',
    `arrival_location`             varchar(100)          DEFAULT NULL COMMENT '到达地',
    `flight_no`                    varchar(32)           DEFAULT NULL COMMENT '航班号',
    `departure_time`               datetime              DEFAULT NULL COMMENT '起飞时间',
    `landing_time`                 datetime              DEFAULT NULL COMMENT '降落时间',
    `company_actual_payment`       decimal(12, 2)        DEFAULT NULL COMMENT '企业实付金额',
    `service_fee`                  decimal(12, 2)        DEFAULT NULL COMMENT '服务费',
    `booking_date`                 date         NOT NULL COMMENT '预订日期',
    `booking_user_id`              bigint(20)   NOT NULL COMMENT '预订人ID',
    `booking_employee_no`          varchar(64)  NOT NULL COMMENT '预订人工号',
    `booking_employee_name`        varchar(64)           DEFAULT NULL COMMENT '预订人姓名',
    `booking_dept_id`              bigint(20)            DEFAULT NULL COMMENT '预订人部门ID',
    `booking_dept_name`            varchar(200)          DEFAULT NULL COMMENT '预订人部门名称',
    `business_trip_application_no` varchar(64)           DEFAULT NULL COMMENT '出差申请单号',
    `business_trip_reason`         varchar(500)          DEFAULT NULL COMMENT '出差事由',
    `cost_center_id`               bigint(20)   NULL COMMENT '成本中心ID',
    `cost_center_name`             varchar(200) NULL COMMENT '成本中心名称',
    `project_id`                   bigint(20)            DEFAULT NULL COMMENT '所属项目ID',
    `project_name`                 varchar(200)          DEFAULT NULL COMMENT '所属项目名称',
    `project_code`                 varchar(64)           DEFAULT NULL COMMENT '项目编码',
    `company_id`                   bigint(20)   NOT NULL COMMENT '所属公司ID',
    `company_name`                 varchar(200) NOT NULL COMMENT '所属公司名称',
    `accounting_period`            varchar(7)   NOT NULL COMMENT '所属账期(YYYY-MM)',
    `expense_report_no`            varchar(64)           DEFAULT NULL COMMENT '报销单号',
    `initiation_status`            tinyint(2)   NOT NULL DEFAULT '0' COMMENT '发起状态(0=待发起,1=已发起,2=发起错误-OA接口报错,3=发起错误-预算不足)',
    `initiation_date`              date                  DEFAULT NULL COMMENT '发起日期',
    `request_id`                   bigint(20)            DEFAULT NULL COMMENT '流程发起ID',
    `initiation_remark`            text                  DEFAULT NULL COMMENT '发起备注',
    `creator`                      varchar(64)           DEFAULT NULL COMMENT '创建人',
    `creator_id`                   bigint(20)            DEFAULT NULL COMMENT '创建人ID',
    `modifier`                     varchar(64)           DEFAULT NULL COMMENT '修改人',
    `modifier_id`                  bigint(20)            DEFAULT NULL COMMENT '修改人ID',
    `ctime`                        datetime              DEFAULT NULL COMMENT '创建时间',
    `mtime`                        datetime              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `del_flag`                     int(11)      NOT NULL DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY `idx_passenger_id` (`passenger_id`) USING BTREE,
    KEY `idx_booking_user_id` (`booking_user_id`) USING BTREE,
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_cost_center_id` (`cost_center_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='滴滴机票订单表';

drop table if EXISTS dd_hotel_order;
CREATE TABLE `dd_hotel_order`
(
    `id`                           bigint(20)   NOT NULL COMMENT '主键ID',
    `order_no`                     varchar(64)  NOT NULL COMMENT '订单号',
    `checkin_user_id`              bigint(20)            DEFAULT NULL COMMENT '入住人ID',
    `checkin_employee_no`          varchar(64)           DEFAULT NULL COMMENT '入住人工号',
    `checkin_person_name`          varchar(64)  NOT NULL COMMENT '入住人姓名',
    `checkin_dept_id`              bigint(20)            DEFAULT NULL COMMENT '入住人部门ID',
    `checkin_dept_name`            varchar(200)          DEFAULT NULL COMMENT '入住人部门名称',
    `city_name`                    varchar(100)          DEFAULT NULL COMMENT '城市名称',
    `hotel_name`                   varchar(200)          DEFAULT NULL COMMENT '酒店名称',
    `room_type`                    varchar(100)          DEFAULT NULL COMMENT '房间房型',
    `checkin_time`                 varchar(64)           DEFAULT NULL COMMENT '入住时间',
    `checkout_time`                varchar(64)           DEFAULT NULL COMMENT '离店时间',
    `company_actual_payment`       decimal(12, 2)        DEFAULT NULL COMMENT '企业实付金额',
    `service_fee`                  decimal(12, 2)        DEFAULT NULL COMMENT '服务费',
    `number_of_days`               decimal(5, 1)         DEFAULT NULL COMMENT '天数',
    `number_of_rooms`              decimal(5, 1)         DEFAULT NULL COMMENT '房间数',
    `room_nights`                  decimal(5, 1)         DEFAULT NULL COMMENT '间夜',
    `unit_price`                   decimal(12, 2)        DEFAULT NULL COMMENT '单价',
    `room_standard_difference`     decimal(12, 2)        DEFAULT NULL COMMENT '房间差标',
    `order_status`                 varchar(32)           DEFAULT NULL COMMENT '订单状态',
    `booking_date`                 date         NOT NULL COMMENT '预订日期',
    `booking_user_id`              bigint(20)   NOT NULL COMMENT '预订人ID',
    `booking_employee_no`          varchar(64)  NOT NULL COMMENT '预订人工号',
    `booking_employee_name`        varchar(64)           DEFAULT NULL COMMENT '预订人姓名',
    `booking_dept_id`              bigint(20)            DEFAULT NULL COMMENT '预订人部门ID',
    `booking_dept_name`            varchar(200)          DEFAULT NULL COMMENT '预订人部门名称',
    `business_trip_application_no` varchar(64)           DEFAULT NULL COMMENT '出差申请单号',
    `business_trip_reason`         varchar(500)          DEFAULT NULL COMMENT '出差事由',
    `cost_center_id`               bigint(20)   NULL COMMENT '成本中心ID',
    `cost_center_name`             varchar(200) NULL COMMENT '成本中心名称',
    `project_id`                   bigint(20)            DEFAULT NULL COMMENT '所属项目ID',
    `project_name`                 varchar(200)          DEFAULT NULL COMMENT '所属项目名称',
    `project_code`                 varchar(64)           DEFAULT NULL COMMENT '项目编码',
    `company_id`                   bigint(20)   NOT NULL COMMENT '所属公司ID',
    `company_name`                 varchar(200) NOT NULL COMMENT '所属公司名称',
    `accounting_period`            varchar(7)   NOT NULL COMMENT '所属账期(YYYY-MM)',
    `expense_report_no`            varchar(64)           DEFAULT NULL COMMENT '报销单号',
    `initiation_status`            tinyint(2)   NOT NULL DEFAULT '0' COMMENT '发起状态(0=待发起,1=已发起,2=发起错误-OA接口报错,3=发起错误-预算不足)',
    `initiation_date`              date                  DEFAULT NULL COMMENT '发起日期',
    `request_id`                   bigint(20)            DEFAULT NULL COMMENT '流程发起ID',
    `initiation_remark`            text                  DEFAULT NULL COMMENT '发起备注',
    `creator`                      varchar(64)           DEFAULT NULL COMMENT '创建人',
    `creator_id`                   bigint(20)            DEFAULT NULL COMMENT '创建人ID',
    `modifier`                     varchar(64)           DEFAULT NULL COMMENT '修改人',
    `modifier_id`                  bigint(20)            DEFAULT NULL COMMENT '修改人ID',
    `ctime`                        datetime              DEFAULT NULL COMMENT '创建时间',
    `mtime`                        datetime              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `del_flag`                     int(11)      NOT NULL DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY `idx_checkin_user_id` (`checkin_user_id`) USING BTREE,
    KEY `idx_booking_user_id` (`booking_user_id`) USING BTREE,
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_cost_center_id` (`cost_center_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='滴滴酒店订单表';

drop table if EXISTS dd_vehicle_order;
CREATE TABLE `dd_vehicle_order`
(
    `id`                           bigint(20)   NOT NULL COMMENT '主键ID',
    `order_no`                     varchar(64)  NOT NULL COMMENT '订单号',
    `passenger_id`                 bigint(20)            DEFAULT NULL COMMENT '乘车人ID',
    `passenger_employee_no`        varchar(64)           DEFAULT NULL COMMENT '乘车人工号',
    `passenger_name`               varchar(64)  NOT NULL COMMENT '乘车人姓名',
    `passenger_dept_id`            bigint(20)            DEFAULT NULL COMMENT '乘车人部门ID',
    `passenger_dept_name`          varchar(200)          DEFAULT NULL COMMENT '乘车人部门名称',
    `travel_time`                  datetime              DEFAULT NULL COMMENT '乘坐时间',
    `arrival_time`                 datetime              DEFAULT NULL COMMENT '到达时间',
    `vehicle_type`                 varchar(64)           DEFAULT NULL COMMENT '用车类型',
    `departure_city`               varchar(100)          DEFAULT NULL COMMENT '出发城市',
    `departure_address`            varchar(500)          DEFAULT NULL COMMENT '出发地址',
    `arrival_city`                 varchar(100)          DEFAULT NULL COMMENT '到达城市',
    `arrival_address`              varchar(500)          DEFAULT NULL COMMENT '到达地址',
    `travel_distance`              decimal(8, 1)         DEFAULT NULL COMMENT '用车行驶距离(公里)',
    `company_actual_payment`       decimal(12, 2)        DEFAULT NULL COMMENT '实付金额',
    `service_fee`                  decimal(12, 2)        DEFAULT NULL COMMENT '服务费',
    `payment_type`                 varchar(32)           DEFAULT NULL COMMENT '支付类型',
    `booking_date`                 date         NOT NULL COMMENT '预订日期',
    `booking_user_id`              bigint(20)   NOT NULL COMMENT '预订人ID',
    `booking_employee_no`          varchar(64)  NOT NULL COMMENT '预订人工号',
    `booking_employee_name`        varchar(64)           DEFAULT NULL COMMENT '预订人姓名',
    `booking_dept_id`              bigint(20)            DEFAULT NULL COMMENT '预订人部门ID',
    `booking_dept_name`            varchar(200)          DEFAULT NULL COMMENT '预订人部门名称',
    `business_trip_application_no` varchar(64)           DEFAULT NULL COMMENT '出差申请单号',
    `business_trip_reason`         varchar(500)          DEFAULT NULL COMMENT '出差事由',
    `cost_center_id`               bigint(20)   NULL COMMENT '成本中心ID',
    `cost_center_name`             varchar(200) NULL COMMENT '成本中心名称',
    `project_id`                   bigint(20)            DEFAULT NULL COMMENT '所属项目ID',
    `project_name`                 varchar(200)          DEFAULT NULL COMMENT '所属项目名称',
    `project_code`                 varchar(64)           DEFAULT NULL COMMENT '项目编码',
    `company_id`                   bigint(20)   NOT NULL COMMENT '所属公司ID',
    `company_name`                 varchar(200) NOT NULL COMMENT '所属公司名称',
    `accounting_period`            varchar(7)   NOT NULL COMMENT '所属账期(YYYY-MM)',
    `expense_report_no`            varchar(64)           DEFAULT NULL COMMENT '报销单号',
    `initiation_status`            tinyint(2)   NOT NULL DEFAULT '0' COMMENT '发起状态(0=待发起,1=已发起,2=发起错误-OA接口报错,3=发起错误-预算不足)',
    `initiation_date`              date                  DEFAULT NULL COMMENT '发起日期',
    `request_id`                   bigint(20)            DEFAULT NULL COMMENT '流程发起ID',
    `initiation_remark`            text                  DEFAULT NULL COMMENT '发起备注',
    `creator`                      varchar(64)           DEFAULT NULL COMMENT '创建人',
    `creator_id`                   bigint(20)            DEFAULT NULL COMMENT '创建人ID',
    `modifier`                     varchar(64)           DEFAULT NULL COMMENT '修改人',
    `modifier_id`                  bigint(20)            DEFAULT NULL COMMENT '修改人ID',
    `ctime`                        datetime              DEFAULT NULL COMMENT '创建时间',
    `mtime`                        datetime              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `del_flag`                     int(11)      NOT NULL DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY `idx_passenger_id` (`passenger_id`) USING BTREE,
    KEY `idx_booking_user_id` (`booking_user_id`) USING BTREE,
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_cost_center_id` (`cost_center_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='滴滴用车订单表';


drop table if EXISTS dd_relation;
CREATE table `dd_relation`
(
    `id`          bigint(20)  NOT NULL COMMENT '主键ID',
    `relate_id`   bigint(20)  NOT NULL COMMENT '关联ID（项目ID或人员ID）',
    `relate_type` tinyint(2)  NOT NULL COMMENT '关联类型 1-项目，2-人员',
    `didi_id`     varchar(64) NOT NULL COMMENT '滴滴ID（',
    `sync_time`   datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '同步时间',
    PRIMARY KEY (`id`),
    KEY `idx_relate_id_type` (`relate_id`, `relate_type`),
    KEY `idx_didi_id` (`didi_id`),
    KEY `idx_relate_type` (`relate_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='滴滴关联表（支持项目和人员）';