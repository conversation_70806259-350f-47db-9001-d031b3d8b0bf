# CLAUDE.md

必须使用中文回复我
This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Java Spring Boot microservice for Project Management System (PMS) - 业务一体化服务 (Business Integration Service). The project is built using Maven and runs on port 4003. It provides comprehensive project management capabilities including cost management, evaluation systems, daily reporting, and DiDi integration.

## Build and Development Commands

### Maven Build
```bash
# Clean and compile the project
mvn clean compile

# Build the project
mvn clean package

# Run with specific profile (dev is default)
mvn spring-boot:run -Dspring-boot.run.profiles=dev
mvn spring-boot:run -Dspring-boot.run.profiles=test
mvn spring-boot:run -Dspring-boot.run.profiles=pre
mvn spring-boot:run -Dspring-boot.run.profiles=master
```

### Docker
```bash
# Build Docker image
docker build -t gok-business-pms .

# Run container
docker run -p 4003:4003 gok-business-pms
```

### Testing
```bash
# Run tests
mvn test

# Run specific test class
mvn test -Dtest=CostCashPlanControllerTest
```

## Architecture Overview

### Multi-Module Structure
- **gok-business-pms** (parent): Maven parent project with common dependencies
- **gok-business-pms-biz**: Main business logic module containing all controllers, services, entities, and mappers
- **gok-business-pms-api**: API definitions and interfaces for Feign clients

### Core Functional Modules

1. **Project Management Core** (`com.gok.pboot.pms`)
   - Project lifecycle management, tasks, milestones, stakeholders
   - Contract management and payment tracking
   - Customer relationship management
   - Daily reporting and time tracking

2. **Cost Management** (`com.gok.pboot.pms.cost`)
   - Comprehensive cost control system with baseline quotations, cash planning
   - Personnel cost management with level-based pricing
   - Expense reimbursement and income settlement
   - Task-based cost tracking and statistics

3. **Evaluation System** (`com.gok.pboot.pms.eval`)
   - Project and personnel performance evaluation
   - Customer satisfaction surveys
   - Task calibration and role-based assessments

4. **DiDi Integration** (`com.gok.pboot.pms.didi`)
   - Travel expense management with DiDi services
   - Vehicle, hotel, and flight order synchronization

### Technical Stack

- **Framework**: Spring Boot 3.x with Spring Cloud Alibaba
- **Database**: Multi-database support (MySQL, Oracle, PostgreSQL, SQL Server)
- **ORM**: MyBatis Plus with XML mappers
- **Service Discovery**: Nacos
- **Security**: Custom authentication with CAS client integration
- **File Handling**: Integrated file system with Excel processing
- **Message Queue**: BCP message API integration
- **Documentation**: Uses extensive enums for business logic representation

### Database and Persistence

- **Entity Design**: Uses JPA annotations with custom base entities (`BaseEntity`, `BeanEntity`)
- **Mapper Pattern**: MyBatis XML mappers in `src/main/resources/mapper/`
- **Enum-Driven**: Heavy use of enums for business rules and validation
- **Multi-tenant**: Data permission control through `gok-components-data`

### Configuration and Profiles

- **Environment Profiles**: dev, test, pre, master (maps to prod)
- **Nacos Integration**: Dynamic configuration with namespace separation
- **Custom Config**: DidiConfig, WeiXinCpConfig, RedisConfig for external integrations

## Important Development Notes

### Code Structure Patterns
- Controllers use `@RestController` with consistent endpoint patterns
- Services follow interface-implementation pattern with `@Service`
- DTOs and VOs are extensively used for data transfer
- Extensive use of enums for business logic validation

### Database Access
- All database operations use MyBatis mappers
- SQL queries are in XML files under `resources/mapper/`
- Entity classes use JPA annotations for ORM mapping

### Testing
- JUnit 4 and Jupiter for testing
- Mockito framework for mocking
- Specific test classes like `CostCashPlanControllerTest` show testing patterns

### External Integrations
- WeChat Work integration for messaging
- DiDi API for travel management
- EHR system for personnel data
- Financial system for cost management

The project follows enterprise-level Java development patterns with comprehensive business domain modeling through extensive enum usage and layered architecture separation.